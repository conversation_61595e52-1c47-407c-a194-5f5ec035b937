// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "APCGNaniteOptimizer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAPCGNaniteOptimizer() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer();
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGNaniteInstanceData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGNaniteMeshData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGNanitePerformanceStats();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGNaniteOptimizationLevel ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel;
static UEnum* EPCGNaniteOptimizationLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGNaniteOptimizationLevel"));
	}
	return Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGNaniteOptimizationLevel>()
{
	return EPCGNaniteOptimizationLevel_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EPCGNaniteOptimizationLevel::Aggressive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EPCGNaniteOptimizationLevel::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for Nanite Optimization\n" },
#endif
		{ "Conservative.DisplayName", "Conservative" },
		{ "Conservative.Name", "EPCGNaniteOptimizationLevel::Conservative" },
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EPCGNaniteOptimizationLevel::Disabled" },
		{ "Maximum.DisplayName", "Maximum" },
		{ "Maximum.Name", "EPCGNaniteOptimizationLevel::Maximum" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for Nanite Optimization" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGNaniteOptimizationLevel::Disabled", (int64)EPCGNaniteOptimizationLevel::Disabled },
		{ "EPCGNaniteOptimizationLevel::Conservative", (int64)EPCGNaniteOptimizationLevel::Conservative },
		{ "EPCGNaniteOptimizationLevel::Balanced", (int64)EPCGNaniteOptimizationLevel::Balanced },
		{ "EPCGNaniteOptimizationLevel::Aggressive", (int64)EPCGNaniteOptimizationLevel::Aggressive },
		{ "EPCGNaniteOptimizationLevel::Maximum", (int64)EPCGNaniteOptimizationLevel::Maximum },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGNaniteOptimizationLevel",
	"EPCGNaniteOptimizationLevel",
	Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel.InnerSingleton, Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel.InnerSingleton;
}
// ********** End Enum EPCGNaniteOptimizationLevel *************************************************

// ********** Begin Enum EPCGNaniteLODStrategy *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGNaniteLODStrategy;
static UEnum* EPCGNaniteLODStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteLODStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGNaniteLODStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGNaniteLODStrategy"));
	}
	return Z_Registration_Info_UEnum_EPCGNaniteLODStrategy.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGNaniteLODStrategy>()
{
	return EPCGNaniteLODStrategy_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Automatic.DisplayName", "Automatic" },
		{ "Automatic.Name", "EPCGNaniteLODStrategy::Automatic" },
		{ "BlueprintType", "true" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EPCGNaniteLODStrategy::Custom" },
		{ "DistanceBased.DisplayName", "Distance Based" },
		{ "DistanceBased.Name", "EPCGNaniteLODStrategy::DistanceBased" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EPCGNaniteLODStrategy::Hybrid" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
		{ "ScreenSize.DisplayName", "Screen Size" },
		{ "ScreenSize.Name", "EPCGNaniteLODStrategy::ScreenSize" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGNaniteLODStrategy::Automatic", (int64)EPCGNaniteLODStrategy::Automatic },
		{ "EPCGNaniteLODStrategy::DistanceBased", (int64)EPCGNaniteLODStrategy::DistanceBased },
		{ "EPCGNaniteLODStrategy::ScreenSize", (int64)EPCGNaniteLODStrategy::ScreenSize },
		{ "EPCGNaniteLODStrategy::Hybrid", (int64)EPCGNaniteLODStrategy::Hybrid },
		{ "EPCGNaniteLODStrategy::Custom", (int64)EPCGNaniteLODStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGNaniteLODStrategy",
	"EPCGNaniteLODStrategy",
	Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteLODStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGNaniteLODStrategy.InnerSingleton, Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGNaniteLODStrategy.InnerSingleton;
}
// ********** End Enum EPCGNaniteLODStrategy *******************************************************

// ********** Begin Enum EPCGNaniteClusteringMode **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGNaniteClusteringMode;
static UEnum* EPCGNaniteClusteringMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteClusteringMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGNaniteClusteringMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGNaniteClusteringMode"));
	}
	return Z_Registration_Info_UEnum_EPCGNaniteClusteringMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGNaniteClusteringMode>()
{
	return EPCGNaniteClusteringMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Default.DisplayName", "Default" },
		{ "Default.Name", "EPCGNaniteClusteringMode::Default" },
		{ "Material.DisplayName", "Material" },
		{ "Material.Name", "EPCGNaniteClusteringMode::Material" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
		{ "Performance.DisplayName", "Performance" },
		{ "Performance.Name", "EPCGNaniteClusteringMode::Performance" },
		{ "Quality.DisplayName", "Quality" },
		{ "Quality.Name", "EPCGNaniteClusteringMode::Quality" },
		{ "Spatial.DisplayName", "Spatial" },
		{ "Spatial.Name", "EPCGNaniteClusteringMode::Spatial" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGNaniteClusteringMode::Default", (int64)EPCGNaniteClusteringMode::Default },
		{ "EPCGNaniteClusteringMode::Spatial", (int64)EPCGNaniteClusteringMode::Spatial },
		{ "EPCGNaniteClusteringMode::Material", (int64)EPCGNaniteClusteringMode::Material },
		{ "EPCGNaniteClusteringMode::Performance", (int64)EPCGNaniteClusteringMode::Performance },
		{ "EPCGNaniteClusteringMode::Quality", (int64)EPCGNaniteClusteringMode::Quality },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGNaniteClusteringMode",
	"EPCGNaniteClusteringMode",
	Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteClusteringMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGNaniteClusteringMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGNaniteClusteringMode.InnerSingleton;
}
// ********** End Enum EPCGNaniteClusteringMode ****************************************************

// ********** Begin Enum EPCGNaniteCompressionLevel ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel;
static UEnum* EPCGNaniteCompressionLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGNaniteCompressionLevel"));
	}
	return Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGNaniteCompressionLevel>()
{
	return EPCGNaniteCompressionLevel_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EPCGNaniteCompressionLevel::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EPCGNaniteCompressionLevel::Low" },
		{ "Maximum.DisplayName", "Maximum" },
		{ "Maximum.Name", "EPCGNaniteCompressionLevel::Maximum" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EPCGNaniteCompressionLevel::Medium" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EPCGNaniteCompressionLevel::None" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGNaniteCompressionLevel::None", (int64)EPCGNaniteCompressionLevel::None },
		{ "EPCGNaniteCompressionLevel::Low", (int64)EPCGNaniteCompressionLevel::Low },
		{ "EPCGNaniteCompressionLevel::Medium", (int64)EPCGNaniteCompressionLevel::Medium },
		{ "EPCGNaniteCompressionLevel::High", (int64)EPCGNaniteCompressionLevel::High },
		{ "EPCGNaniteCompressionLevel::Maximum", (int64)EPCGNaniteCompressionLevel::Maximum },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGNaniteCompressionLevel",
	"EPCGNaniteCompressionLevel",
	Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel()
{
	if (!Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel.InnerSingleton, Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel.InnerSingleton;
}
// ********** End Enum EPCGNaniteCompressionLevel **************************************************

// ********** Begin ScriptStruct FPCGNaniteOptimizationConfig **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig;
class UScriptStruct* FPCGNaniteOptimizationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGNaniteOptimizationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for Nanite Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for Nanite Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationLevel_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Overall optimization level\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Overall optimization level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODStrategy_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD strategy for Nanite meshes\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD strategy for Nanite meshes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusteringMode_MetaData[] = {
		{ "Category", "Clustering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Clustering mode for geometry optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clustering mode for geometry optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionLevel_MetaData[] = {
		{ "Category", "Compression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Compression level for Nanite data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compression level for Nanite data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoConvertToNanite_MetaData[] = {
		{ "Category", "Automation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enable automatic Nanite conversion for PCG meshes\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable automatic Nanite conversion for PCG meshes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinTriangleCountForNanite_MetaData[] = {
		{ "Category", "Thresholds" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Minimum triangle count for Nanite conversion\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum triangle count for Nanite conversion" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTrianglesPerCluster_MetaData[] = {
		{ "Category", "Clustering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Maximum triangle count per cluster\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum triangle count per cluster" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetScreenSize_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target screen size for LOD transitions (in pixels)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target screen size for LOD transitions (in pixels)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFallbackMeshes_MetaData[] = {
		{ "Category", "Fallback" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enable fallback meshes for non-Nanite hardware\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable fallback meshes for non-Nanite hardware" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteMemoryBudgetMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory budget for Nanite streaming (in MB)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory budget for Nanite streaming (in MB)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUDrivenRendering_MetaData[] = {
		{ "Category", "GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enable GPU-driven rendering optimizations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable GPU-driven rendering optimizations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOcclusionCulling_MetaData[] = {
		{ "Category", "Culling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enable occlusion culling optimizations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable occlusion culling optimizations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceCullingThreshold_MetaData[] = {
		{ "Category", "Culling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance culling threshold (in UE units, 1 UU = 1 cm)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance culling threshold (in UE units, 1 UU = 1 cm)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODStrategy;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ClusteringMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ClusteringMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompressionLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CompressionLevel;
	static void NewProp_bAutoConvertToNanite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoConvertToNanite;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinTriangleCountForNanite;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxTrianglesPerCluster;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetScreenSize;
	static void NewProp_bEnableFallbackMeshes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFallbackMeshes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NaniteMemoryBudgetMB;
	static void NewProp_bEnableGPUDrivenRendering_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUDrivenRendering;
	static void NewProp_bEnableOcclusionCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusionCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceCullingThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGNaniteOptimizationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_OptimizationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_OptimizationLevel = { "OptimizationLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, OptimizationLevel), Z_Construct_UEnum_Aura_EPCGNaniteOptimizationLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationLevel_MetaData), NewProp_OptimizationLevel_MetaData) }; // 3386825555
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_LODStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_LODStrategy = { "LODStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, LODStrategy), Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODStrategy_MetaData), NewProp_LODStrategy_MetaData) }; // 192784107
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_ClusteringMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_ClusteringMode = { "ClusteringMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, ClusteringMode), Z_Construct_UEnum_Aura_EPCGNaniteClusteringMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusteringMode_MetaData), NewProp_ClusteringMode_MetaData) }; // 1923857438
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_CompressionLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_CompressionLevel = { "CompressionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, CompressionLevel), Z_Construct_UEnum_Aura_EPCGNaniteCompressionLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionLevel_MetaData), NewProp_CompressionLevel_MetaData) }; // 3064305711
void Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bAutoConvertToNanite_SetBit(void* Obj)
{
	((FPCGNaniteOptimizationConfig*)Obj)->bAutoConvertToNanite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bAutoConvertToNanite = { "bAutoConvertToNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGNaniteOptimizationConfig), &Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bAutoConvertToNanite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoConvertToNanite_MetaData), NewProp_bAutoConvertToNanite_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_MinTriangleCountForNanite = { "MinTriangleCountForNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, MinTriangleCountForNanite), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinTriangleCountForNanite_MetaData), NewProp_MinTriangleCountForNanite_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_MaxTrianglesPerCluster = { "MaxTrianglesPerCluster", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, MaxTrianglesPerCluster), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTrianglesPerCluster_MetaData), NewProp_MaxTrianglesPerCluster_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_TargetScreenSize = { "TargetScreenSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, TargetScreenSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetScreenSize_MetaData), NewProp_TargetScreenSize_MetaData) };
void Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableFallbackMeshes_SetBit(void* Obj)
{
	((FPCGNaniteOptimizationConfig*)Obj)->bEnableFallbackMeshes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableFallbackMeshes = { "bEnableFallbackMeshes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGNaniteOptimizationConfig), &Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableFallbackMeshes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFallbackMeshes_MetaData), NewProp_bEnableFallbackMeshes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_NaniteMemoryBudgetMB = { "NaniteMemoryBudgetMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, NaniteMemoryBudgetMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteMemoryBudgetMB_MetaData), NewProp_NaniteMemoryBudgetMB_MetaData) };
void Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableGPUDrivenRendering_SetBit(void* Obj)
{
	((FPCGNaniteOptimizationConfig*)Obj)->bEnableGPUDrivenRendering = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableGPUDrivenRendering = { "bEnableGPUDrivenRendering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGNaniteOptimizationConfig), &Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableGPUDrivenRendering_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUDrivenRendering_MetaData), NewProp_bEnableGPUDrivenRendering_MetaData) };
void Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableOcclusionCulling_SetBit(void* Obj)
{
	((FPCGNaniteOptimizationConfig*)Obj)->bEnableOcclusionCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableOcclusionCulling = { "bEnableOcclusionCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGNaniteOptimizationConfig), &Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableOcclusionCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOcclusionCulling_MetaData), NewProp_bEnableOcclusionCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_DistanceCullingThreshold = { "DistanceCullingThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteOptimizationConfig, DistanceCullingThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceCullingThreshold_MetaData), NewProp_DistanceCullingThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_OptimizationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_OptimizationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_LODStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_LODStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_ClusteringMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_ClusteringMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_CompressionLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_CompressionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bAutoConvertToNanite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_MinTriangleCountForNanite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_MaxTrianglesPerCluster,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_TargetScreenSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableFallbackMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_NaniteMemoryBudgetMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableGPUDrivenRendering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_bEnableOcclusionCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewProp_DistanceCullingThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGNaniteOptimizationConfig",
	Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::PropPointers),
	sizeof(FPCGNaniteOptimizationConfig),
	alignof(FPCGNaniteOptimizationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGNaniteOptimizationConfig ****************************************

// ********** Begin ScriptStruct FPCGNaniteMeshData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData;
class UScriptStruct* FPCGNaniteMeshData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGNaniteMeshData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGNaniteMeshData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalMesh_MetaData[] = {
		{ "Category", "Mesh" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Original static mesh reference\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Original static mesh reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteMesh_MetaData[] = {
		{ "Category", "Mesh" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Nanite-optimized mesh reference\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nanite-optimized mesh reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FallbackMesh_MetaData[] = {
		{ "Category", "Mesh" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fallback mesh for non-Nanite hardware\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fallback mesh for non-Nanite hardware" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalTriangleCount_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Triangle count of original mesh\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Triangle count of original mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteTriangleCount_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Triangle count of Nanite mesh\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Triangle count of Nanite mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteMemoryUsage_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory usage of Nanite mesh (in bytes)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory usage of Nanite mesh (in bytes)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionRatio_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Compression ratio achieved\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compression ratio achieved" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsNaniteSuitable_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Is this mesh suitable for Nanite?\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is this mesh suitable for Nanite?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsProcessed_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Has this mesh been processed?\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Has this mesh been processed?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingError_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Processing error message (if any)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processing error message (if any)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_OriginalMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_NaniteMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FallbackMesh;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OriginalTriangleCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NaniteTriangleCount;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_NaniteMemoryUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompressionRatio;
	static void NewProp_bIsNaniteSuitable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsNaniteSuitable;
	static void NewProp_bIsProcessed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsProcessed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProcessingError;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGNaniteMeshData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_OriginalMesh = { "OriginalMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, OriginalMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalMesh_MetaData), NewProp_OriginalMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_NaniteMesh = { "NaniteMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, NaniteMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteMesh_MetaData), NewProp_NaniteMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_FallbackMesh = { "FallbackMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, FallbackMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FallbackMesh_MetaData), NewProp_FallbackMesh_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_OriginalTriangleCount = { "OriginalTriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, OriginalTriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalTriangleCount_MetaData), NewProp_OriginalTriangleCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_NaniteTriangleCount = { "NaniteTriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, NaniteTriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteTriangleCount_MetaData), NewProp_NaniteTriangleCount_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_NaniteMemoryUsage = { "NaniteMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, NaniteMemoryUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteMemoryUsage_MetaData), NewProp_NaniteMemoryUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_CompressionRatio = { "CompressionRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, CompressionRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionRatio_MetaData), NewProp_CompressionRatio_MetaData) };
void Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsNaniteSuitable_SetBit(void* Obj)
{
	((FPCGNaniteMeshData*)Obj)->bIsNaniteSuitable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsNaniteSuitable = { "bIsNaniteSuitable", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGNaniteMeshData), &Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsNaniteSuitable_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsNaniteSuitable_MetaData), NewProp_bIsNaniteSuitable_MetaData) };
void Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsProcessed_SetBit(void* Obj)
{
	((FPCGNaniteMeshData*)Obj)->bIsProcessed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsProcessed = { "bIsProcessed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGNaniteMeshData), &Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsProcessed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsProcessed_MetaData), NewProp_bIsProcessed_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_ProcessingError = { "ProcessingError", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteMeshData, ProcessingError), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingError_MetaData), NewProp_ProcessingError_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_OriginalMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_NaniteMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_FallbackMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_OriginalTriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_NaniteTriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_NaniteMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_CompressionRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsNaniteSuitable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_bIsProcessed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewProp_ProcessingError,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGNaniteMeshData",
	Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::PropPointers),
	sizeof(FPCGNaniteMeshData),
	alignof(FPCGNaniteMeshData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGNaniteMeshData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData.InnerSingleton, Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData.InnerSingleton;
}
// ********** End ScriptStruct FPCGNaniteMeshData **************************************************

// ********** Begin ScriptStruct FPCGNaniteInstanceData ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData;
class UScriptStruct* FPCGNaniteInstanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGNaniteInstanceData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGNaniteInstanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransform_MetaData[] = {
		{ "Category", "Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance transform\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance transform" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshData_MetaData[] = {
		{ "Category", "Mesh" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh data reference\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh data reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialOverrides_MetaData[] = {
		{ "Category", "Material" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material overrides\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material overrides" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODBias_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD bias for this instance\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD bias for this instance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Culling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling distance override\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling distance override" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Visibility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Is this instance visible?\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is this instance visible?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPriority_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance priority for streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance priority for streaming" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransform;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshData;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MaterialOverrides_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MaterialOverrides;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODBias;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingPriority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGNaniteInstanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_InstanceTransform = { "InstanceTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteInstanceData, InstanceTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransform_MetaData), NewProp_InstanceTransform_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_MeshData = { "MeshData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteInstanceData, MeshData), Z_Construct_UScriptStruct_FPCGNaniteMeshData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshData_MetaData), NewProp_MeshData_MetaData) }; // 1470135679
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_MaterialOverrides_Inner = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_MaterialOverrides = { "MaterialOverrides", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteInstanceData, MaterialOverrides), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialOverrides_MetaData), NewProp_MaterialOverrides_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_LODBias = { "LODBias", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteInstanceData, LODBias), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODBias_MetaData), NewProp_LODBias_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteInstanceData, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
void Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FPCGNaniteInstanceData*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGNaniteInstanceData), &Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_StreamingPriority = { "StreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNaniteInstanceData, StreamingPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPriority_MetaData), NewProp_StreamingPriority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_InstanceTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_MeshData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_MaterialOverrides_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_MaterialOverrides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_LODBias,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewProp_StreamingPriority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGNaniteInstanceData",
	Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::PropPointers),
	sizeof(FPCGNaniteInstanceData),
	alignof(FPCGNaniteInstanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGNaniteInstanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData.InnerSingleton, Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData.InnerSingleton;
}
// ********** End ScriptStruct FPCGNaniteInstanceData **********************************************

// ********** Begin ScriptStruct FPCGNanitePerformanceStats ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats;
class UScriptStruct* FPCGNanitePerformanceStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGNanitePerformanceStats, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGNanitePerformanceStats"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalNaniteInstances_MetaData[] = {
		{ "Category", "Counts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Total number of Nanite instances\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total number of Nanite instances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibleNaniteInstances_MetaData[] = {
		{ "Category", "Counts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Number of visible Nanite instances\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of visible Nanite instances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CulledInstances_MetaData[] = {
		{ "Category", "Counts" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Number of culled instances\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of culled instances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrianglesRendered_MetaData[] = {
		{ "Category", "Triangles" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Total triangles rendered\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total triangles rendered" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrianglesCulled_MetaData[] = {
		{ "Category", "Triangles" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Total triangles culled\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total triangles culled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU memory usage (in MB)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU memory usage (in MB)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming memory usage (in MB)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming memory usage (in MB)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameTimeMS_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Average frame time for Nanite rendering (in ms)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Average frame time for Nanite rendering (in ms)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUUtilizationPercent_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU utilization percentage\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU utilization percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionEfficiency_MetaData[] = {
		{ "Category", "Efficiency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Compression efficiency\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compression efficiency" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalNaniteInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VisibleNaniteInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CulledInstances;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_TrianglesRendered;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_TrianglesCulled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameTimeMS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUUtilizationPercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompressionEfficiency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGNanitePerformanceStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_TotalNaniteInstances = { "TotalNaniteInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, TotalNaniteInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalNaniteInstances_MetaData), NewProp_TotalNaniteInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_VisibleNaniteInstances = { "VisibleNaniteInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, VisibleNaniteInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibleNaniteInstances_MetaData), NewProp_VisibleNaniteInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_CulledInstances = { "CulledInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, CulledInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CulledInstances_MetaData), NewProp_CulledInstances_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_TrianglesRendered = { "TrianglesRendered", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, TrianglesRendered), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrianglesRendered_MetaData), NewProp_TrianglesRendered_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_TrianglesCulled = { "TrianglesCulled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, TrianglesCulled), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrianglesCulled_MetaData), NewProp_TrianglesCulled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_GPUMemoryUsageMB = { "GPUMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, GPUMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUMemoryUsageMB_MetaData), NewProp_GPUMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_StreamingMemoryUsageMB = { "StreamingMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, StreamingMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingMemoryUsageMB_MetaData), NewProp_StreamingMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_AverageFrameTimeMS = { "AverageFrameTimeMS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, AverageFrameTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameTimeMS_MetaData), NewProp_AverageFrameTimeMS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_GPUUtilizationPercent = { "GPUUtilizationPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, GPUUtilizationPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUUtilizationPercent_MetaData), NewProp_GPUUtilizationPercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_CompressionEfficiency = { "CompressionEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGNanitePerformanceStats, CompressionEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionEfficiency_MetaData), NewProp_CompressionEfficiency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_TotalNaniteInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_VisibleNaniteInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_CulledInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_TrianglesRendered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_TrianglesCulled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_GPUMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_StreamingMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_AverageFrameTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_GPUUtilizationPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewProp_CompressionEfficiency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGNanitePerformanceStats",
	Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::PropPointers),
	sizeof(FPCGNanitePerformanceStats),
	alignof(FPCGNanitePerformanceStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGNanitePerformanceStats()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats.InnerSingleton, Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats.InnerSingleton;
}
// ********** End ScriptStruct FPCGNanitePerformanceStats ******************************************

// ********** Begin Class APCGNaniteOptimizer Function BatchConvertMeshesToNanite ******************
struct Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics
{
	struct PCGNaniteOptimizer_eventBatchConvertMeshesToNanite_Parms
	{
		TArray<UStaticMesh*> OriginalMeshes;
		TArray<FPCGNaniteMeshData> OutMeshDataArray;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Conversion" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalMeshes_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OriginalMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OriginalMeshes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutMeshDataArray_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutMeshDataArray;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OriginalMeshes_Inner = { "OriginalMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OriginalMeshes = { "OriginalMeshes", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventBatchConvertMeshesToNanite_Parms, OriginalMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalMeshes_MetaData), NewProp_OriginalMeshes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OutMeshDataArray_Inner = { "OutMeshDataArray", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGNaniteMeshData, METADATA_PARAMS(0, nullptr) }; // 1470135679
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OutMeshDataArray = { "OutMeshDataArray", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventBatchConvertMeshesToNanite_Parms, OutMeshDataArray), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1470135679
void Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventBatchConvertMeshesToNanite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventBatchConvertMeshesToNanite_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OriginalMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OriginalMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OutMeshDataArray_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_OutMeshDataArray,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "BatchConvertMeshesToNanite", Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::PCGNaniteOptimizer_eventBatchConvertMeshesToNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::PCGNaniteOptimizer_eventBatchConvertMeshesToNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execBatchConvertMeshesToNanite)
{
	P_GET_TARRAY_REF(UStaticMesh*,Z_Param_Out_OriginalMeshes);
	P_GET_TARRAY_REF(FPCGNaniteMeshData,Z_Param_Out_OutMeshDataArray);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BatchConvertMeshesToNanite(Z_Param_Out_OriginalMeshes,Z_Param_Out_OutMeshDataArray);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function BatchConvertMeshesToNanite ********************

// ********** Begin Class APCGNaniteOptimizer Function CalculateOptimalLODBias *********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics
{
	struct PCGNaniteOptimizer_eventCalculateOptimalLODBias_Parms
	{
		FVector ViewerLocation;
		FTransform InstanceTransform;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Management" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransform;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventCalculateOptimalLODBias_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::NewProp_InstanceTransform = { "InstanceTransform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventCalculateOptimalLODBias_Parms, InstanceTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransform_MetaData), NewProp_InstanceTransform_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventCalculateOptimalLODBias_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::NewProp_InstanceTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "CalculateOptimalLODBias", Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::PCGNaniteOptimizer_eventCalculateOptimalLODBias_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::PCGNaniteOptimizer_eventCalculateOptimalLODBias_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execCalculateOptimalLODBias)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_InstanceTransform);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateOptimalLODBias(Z_Param_Out_ViewerLocation,Z_Param_Out_InstanceTransform);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function CalculateOptimalLODBias ***********************

// ********** Begin Class APCGNaniteOptimizer Function ClearAllNaniteInstances *********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_ClearAllNaniteInstances_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instance Management" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_ClearAllNaniteInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "ClearAllNaniteInstances", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ClearAllNaniteInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_ClearAllNaniteInstances_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_ClearAllNaniteInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_ClearAllNaniteInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execClearAllNaniteInstances)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllNaniteInstances();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function ClearAllNaniteInstances ***********************

// ********** Begin Class APCGNaniteOptimizer Function ConvertMeshToNanite *************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics
{
	struct PCGNaniteOptimizer_eventConvertMeshToNanite_Parms
	{
		UStaticMesh* OriginalMesh;
		FPCGNaniteMeshData OutMeshData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Conversion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Mesh Conversion Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Mesh Conversion Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OriginalMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutMeshData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_OriginalMesh = { "OriginalMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventConvertMeshToNanite_Parms, OriginalMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_OutMeshData = { "OutMeshData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventConvertMeshToNanite_Parms, OutMeshData), Z_Construct_UScriptStruct_FPCGNaniteMeshData, METADATA_PARAMS(0, nullptr) }; // 1470135679
void Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventConvertMeshToNanite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventConvertMeshToNanite_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_OriginalMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_OutMeshData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "ConvertMeshToNanite", Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::PCGNaniteOptimizer_eventConvertMeshToNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::PCGNaniteOptimizer_eventConvertMeshToNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execConvertMeshToNanite)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_OriginalMesh);
	P_GET_STRUCT_REF(FPCGNaniteMeshData,Z_Param_Out_OutMeshData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConvertMeshToNanite(Z_Param_OriginalMesh,Z_Param_Out_OutMeshData);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function ConvertMeshToNanite ***************************

// ********** Begin Class APCGNaniteOptimizer Function EnablePerformanceMonitoring *****************
struct Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics
{
	struct PCGNaniteOptimizer_eventEnablePerformanceMonitoring_Parms
	{
		bool bEnable;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventEnablePerformanceMonitoring_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventEnablePerformanceMonitoring_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventEnablePerformanceMonitoring_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventEnablePerformanceMonitoring_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_bEnable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "EnablePerformanceMonitoring", Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::PCGNaniteOptimizer_eventEnablePerformanceMonitoring_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::PCGNaniteOptimizer_eventEnablePerformanceMonitoring_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execEnablePerformanceMonitoring)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EnablePerformanceMonitoring(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function EnablePerformanceMonitoring *******************

// ********** Begin Class APCGNaniteOptimizer Function ExportNaniteOptimizationReport **************
struct Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics
{
	struct PCGNaniteOptimizer_eventExportNaniteOptimizationReport_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventExportNaniteOptimizationReport_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventExportNaniteOptimizationReport_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventExportNaniteOptimizationReport_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "ExportNaniteOptimizationReport", Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::PCGNaniteOptimizer_eventExportNaniteOptimizationReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::PCGNaniteOptimizer_eventExportNaniteOptimizationReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execExportNaniteOptimizationReport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExportNaniteOptimizationReport(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function ExportNaniteOptimizationReport ****************

// ********** Begin Class APCGNaniteOptimizer Function GetCompressionEfficiency ********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics
{
	struct PCGNaniteOptimizer_eventGetCompressionEfficiency_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetCompressionEfficiency_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetCompressionEfficiency", Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::PCGNaniteOptimizer_eventGetCompressionEfficiency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::PCGNaniteOptimizer_eventGetCompressionEfficiency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetCompressionEfficiency)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCompressionEfficiency();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetCompressionEfficiency **********************

// ********** Begin Class APCGNaniteOptimizer Function GetGPUMemoryUsage ***************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics
{
	struct PCGNaniteOptimizer_eventGetGPUMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetGPUMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetGPUMemoryUsage", Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::PCGNaniteOptimizer_eventGetGPUMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::PCGNaniteOptimizer_eventGetGPUMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetGPUMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetGPUMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetGPUMemoryUsage *****************************

// ********** Begin Class APCGNaniteOptimizer Function GetNaniteInstanceCount **********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics
{
	struct PCGNaniteOptimizer_eventGetNaniteInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instance Management" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetNaniteInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetNaniteInstanceCount", Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::PCGNaniteOptimizer_eventGetNaniteInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::PCGNaniteOptimizer_eventGetNaniteInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetNaniteInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetNaniteInstanceCount();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetNaniteInstanceCount ************************

// ********** Begin Class APCGNaniteOptimizer Function GetNaniteInstances **************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics
{
	struct PCGNaniteOptimizer_eventGetNaniteInstances_Parms
	{
		TArray<FPCGNaniteInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instance Management" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGNaniteInstanceData, METADATA_PARAMS(0, nullptr) }; // 4189694740
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetNaniteInstances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4189694740
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetNaniteInstances", Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::PCGNaniteOptimizer_eventGetNaniteInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::PCGNaniteOptimizer_eventGetNaniteInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetNaniteInstances)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGNaniteInstanceData>*)Z_Param__Result=P_THIS->GetNaniteInstances();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetNaniteInstances ****************************

// ********** Begin Class APCGNaniteOptimizer Function GetNaniteSystemInfo *************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics
{
	struct PCGNaniteOptimizer_eventGetNaniteSystemInfo_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetNaniteSystemInfo_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetNaniteSystemInfo", Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::PCGNaniteOptimizer_eventGetNaniteSystemInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::PCGNaniteOptimizer_eventGetNaniteSystemInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetNaniteSystemInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetNaniteSystemInfo();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetNaniteSystemInfo ***************************

// ********** Begin Class APCGNaniteOptimizer Function GetOptimizationConfig ***********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics
{
	struct PCGNaniteOptimizer_eventGetOptimizationConfig_Parms
	{
		FPCGNaniteOptimizationConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nanite" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetOptimizationConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig, METADATA_PARAMS(0, nullptr) }; // 3674028056
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetOptimizationConfig", Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::PCGNaniteOptimizer_eventGetOptimizationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::PCGNaniteOptimizer_eventGetOptimizationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetOptimizationConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGNaniteOptimizationConfig*)Z_Param__Result=P_THIS->GetOptimizationConfig();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetOptimizationConfig *************************

// ********** Begin Class APCGNaniteOptimizer Function GetPerformanceStats *************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics
{
	struct PCGNaniteOptimizer_eventGetPerformanceStats_Parms
	{
		FPCGNanitePerformanceStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Performance Monitoring ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Performance Monitoring ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetPerformanceStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGNanitePerformanceStats, METADATA_PARAMS(0, nullptr) }; // 1030788720
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetPerformanceStats", Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::PCGNaniteOptimizer_eventGetPerformanceStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::PCGNaniteOptimizer_eventGetPerformanceStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetPerformanceStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGNanitePerformanceStats*)Z_Param__Result=P_THIS->GetPerformanceStats();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetPerformanceStats ***************************

// ********** Begin Class APCGNaniteOptimizer Function GetVisibleInstances *************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics
{
	struct PCGNaniteOptimizer_eventGetVisibleInstances_Parms
	{
		FVector ViewerLocation;
		float ViewDistance;
		TArray<FPCGNaniteInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ViewDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetVisibleInstances_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ViewDistance = { "ViewDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetVisibleInstances_Parms, ViewDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGNaniteInstanceData, METADATA_PARAMS(0, nullptr) }; // 4189694740
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventGetVisibleInstances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4189694740
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ViewDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "GetVisibleInstances", Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::PCGNaniteOptimizer_eventGetVisibleInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::PCGNaniteOptimizer_eventGetVisibleInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execGetVisibleInstances)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ViewDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGNaniteInstanceData>*)Z_Param__Result=P_THIS->GetVisibleInstances(Z_Param_Out_ViewerLocation,Z_Param_ViewDistance);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function GetVisibleInstances ***************************

// ********** Begin Class APCGNaniteOptimizer Function InitializeNaniteOptimizer *******************
struct Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics
{
	struct PCGNaniteOptimizer_eventInitializeNaniteOptimizer_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nanite" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Core Nanite Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Core Nanite Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventInitializeNaniteOptimizer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventInitializeNaniteOptimizer_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "InitializeNaniteOptimizer", Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::PCGNaniteOptimizer_eventInitializeNaniteOptimizer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::PCGNaniteOptimizer_eventInitializeNaniteOptimizer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execInitializeNaniteOptimizer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeNaniteOptimizer();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function InitializeNaniteOptimizer *********************

// ********** Begin Class APCGNaniteOptimizer Function IntegrateWithPCGComponent *******************
struct Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics
{
	struct PCGNaniteOptimizer_eventIntegrateWithPCGComponent_Parms
	{
		UPCGComponent* PCGComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventIntegrateWithPCGComponent_Parms, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
void Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventIntegrateWithPCGComponent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventIntegrateWithPCGComponent_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "IntegrateWithPCGComponent", Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::PCGNaniteOptimizer_eventIntegrateWithPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::PCGNaniteOptimizer_eventIntegrateWithPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execIntegrateWithPCGComponent)
{
	P_GET_OBJECT(UPCGComponent,Z_Param_PCGComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithPCGComponent(Z_Param_PCGComponent);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function IntegrateWithPCGComponent *********************

// ********** Begin Class APCGNaniteOptimizer Function IsMeshNaniteEnabled *************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics
{
	struct PCGNaniteOptimizer_eventIsMeshNaniteEnabled_Parms
	{
		UStaticMesh* Mesh;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventIsMeshNaniteEnabled_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventIsMeshNaniteEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventIsMeshNaniteEnabled_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "IsMeshNaniteEnabled", Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::PCGNaniteOptimizer_eventIsMeshNaniteEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::PCGNaniteOptimizer_eventIsMeshNaniteEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execIsMeshNaniteEnabled)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMeshNaniteEnabled(Z_Param_Mesh);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function IsMeshNaniteEnabled ***************************

// ********** Begin Class APCGNaniteOptimizer Function IsNaniteSupported ***************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics
{
	struct PCGNaniteOptimizer_eventIsNaniteSupported_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Utility Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Utility Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventIsNaniteSupported_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventIsNaniteSupported_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "IsNaniteSupported", Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::PCGNaniteOptimizer_eventIsNaniteSupported_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::PCGNaniteOptimizer_eventIsNaniteSupported_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execIsNaniteSupported)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsNaniteSupported();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function IsNaniteSupported *****************************

// ********** Begin Class APCGNaniteOptimizer Function OnMeshConversionComplete ********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics
{
	struct PCGNaniteOptimizer_eventOnMeshConversionComplete_Parms
	{
		UStaticMesh* OriginalMesh;
		UStaticMesh* NaniteMesh;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Event Handlers ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Event Handlers ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OriginalMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NaniteMesh;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::NewProp_OriginalMesh = { "OriginalMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOnMeshConversionComplete_Parms, OriginalMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::NewProp_NaniteMesh = { "NaniteMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOnMeshConversionComplete_Parms, NaniteMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::NewProp_OriginalMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::NewProp_NaniteMesh,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "OnMeshConversionComplete", Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::PCGNaniteOptimizer_eventOnMeshConversionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::PCGNaniteOptimizer_eventOnMeshConversionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execOnMeshConversionComplete)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_OriginalMesh);
	P_GET_OBJECT(UStaticMesh,Z_Param_NaniteMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnMeshConversionComplete(Z_Param_OriginalMesh,Z_Param_NaniteMesh);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function OnMeshConversionComplete **********************

// ********** Begin Class APCGNaniteOptimizer Function OnNaniteStreamingUpdate *********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics
{
	struct PCGNaniteOptimizer_eventOnNaniteStreamingUpdate_Parms
	{
		FVector StreamingCenter;
		float StreamingRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::NewProp_StreamingCenter = { "StreamingCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOnNaniteStreamingUpdate_Parms, StreamingCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingCenter_MetaData), NewProp_StreamingCenter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::NewProp_StreamingRadius = { "StreamingRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOnNaniteStreamingUpdate_Parms, StreamingRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::NewProp_StreamingCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::NewProp_StreamingRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "OnNaniteStreamingUpdate", Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::PCGNaniteOptimizer_eventOnNaniteStreamingUpdate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::PCGNaniteOptimizer_eventOnNaniteStreamingUpdate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execOnNaniteStreamingUpdate)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StreamingCenter);
	P_GET_PROPERTY(FFloatProperty,Z_Param_StreamingRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnNaniteStreamingUpdate(Z_Param_Out_StreamingCenter,Z_Param_StreamingRadius);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function OnNaniteStreamingUpdate ***********************

// ********** Begin Class APCGNaniteOptimizer Function OnPerformanceThresholdExceeded **************
struct Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics
{
	struct PCGNaniteOptimizer_eventOnPerformanceThresholdExceeded_Parms
	{
		float CurrentUsage;
		float ThresholdUsage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ThresholdUsage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::NewProp_CurrentUsage = { "CurrentUsage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOnPerformanceThresholdExceeded_Parms, CurrentUsage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::NewProp_ThresholdUsage = { "ThresholdUsage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOnPerformanceThresholdExceeded_Parms, ThresholdUsage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::NewProp_CurrentUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::NewProp_ThresholdUsage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "OnPerformanceThresholdExceeded", Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::PCGNaniteOptimizer_eventOnPerformanceThresholdExceeded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::PCGNaniteOptimizer_eventOnPerformanceThresholdExceeded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execOnPerformanceThresholdExceeded)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurrentUsage);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ThresholdUsage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPerformanceThresholdExceeded(Z_Param_CurrentUsage,Z_Param_ThresholdUsage);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function OnPerformanceThresholdExceeded ****************

// ********** Begin Class APCGNaniteOptimizer Function OptimizeMemoryUsage *************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMemoryUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "OptimizeMemoryUsage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMemoryUsage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execOptimizeMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function OptimizeMemoryUsage ***************************

// ********** Begin Class APCGNaniteOptimizer Function OptimizeMeshForNanite ***********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics
{
	struct PCGNaniteOptimizer_eventOptimizeMeshForNanite_Parms
	{
		UStaticMesh* Mesh;
		FPCGNaniteOptimizationConfig Config;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Conversion" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOptimizeMeshForNanite_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventOptimizeMeshForNanite_Parms, Config), Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 3674028056
void Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventOptimizeMeshForNanite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventOptimizeMeshForNanite_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "OptimizeMeshForNanite", Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::PCGNaniteOptimizer_eventOptimizeMeshForNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::PCGNaniteOptimizer_eventOptimizeMeshForNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execOptimizeMeshForNanite)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_STRUCT_REF(FPCGNaniteOptimizationConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeMeshForNanite(Z_Param_Mesh,Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function OptimizeMeshForNanite *************************

// ********** Begin Class APCGNaniteOptimizer Function ProcessPCGGeneratedMeshes *******************
struct Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics
{
	struct PCGNaniteOptimizer_eventProcessPCGGeneratedMeshes_Parms
	{
		TArray<UStaticMeshComponent*> MeshComponents;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponents_MetaData[] = {
		{ "EditInline", "true" },
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshComponents;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_MeshComponents_Inner = { "MeshComponents", nullptr, (EPropertyFlags)0x0000000000080000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_MeshComponents = { "MeshComponents", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventProcessPCGGeneratedMeshes_Parms, MeshComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponents_MetaData), NewProp_MeshComponents_MetaData) };
void Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventProcessPCGGeneratedMeshes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventProcessPCGGeneratedMeshes_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_MeshComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_MeshComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "ProcessPCGGeneratedMeshes", Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::PCGNaniteOptimizer_eventProcessPCGGeneratedMeshes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::PCGNaniteOptimizer_eventProcessPCGGeneratedMeshes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execProcessPCGGeneratedMeshes)
{
	P_GET_TARRAY_REF(UStaticMeshComponent*,Z_Param_Out_MeshComponents);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ProcessPCGGeneratedMeshes(Z_Param_Out_MeshComponents);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function ProcessPCGGeneratedMeshes *********************

// ********** Begin Class APCGNaniteOptimizer Function RegisterNaniteInstance **********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics
{
	struct PCGNaniteOptimizer_eventRegisterNaniteInstance_Parms
	{
		FPCGNaniteInstanceData InstanceData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instance Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Instance Management ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Instance Management ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::NewProp_InstanceData = { "InstanceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventRegisterNaniteInstance_Parms, InstanceData), Z_Construct_UScriptStruct_FPCGNaniteInstanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceData_MetaData), NewProp_InstanceData_MetaData) }; // 4189694740
void Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventRegisterNaniteInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventRegisterNaniteInstance_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::NewProp_InstanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "RegisterNaniteInstance", Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::PCGNaniteOptimizer_eventRegisterNaniteInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::PCGNaniteOptimizer_eventRegisterNaniteInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execRegisterNaniteInstance)
{
	P_GET_STRUCT_REF(FPCGNaniteInstanceData,Z_Param_Out_InstanceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterNaniteInstance(Z_Param_Out_InstanceData);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function RegisterNaniteInstance ************************

// ********** Begin Class APCGNaniteOptimizer Function ResetPerformanceStats ***********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_ResetPerformanceStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_ResetPerformanceStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "ResetPerformanceStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ResetPerformanceStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_ResetPerformanceStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_ResetPerformanceStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_ResetPerformanceStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execResetPerformanceStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetPerformanceStats();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function ResetPerformanceStats *************************

// ********** Begin Class APCGNaniteOptimizer Function SetCullingParameters ************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics
{
	struct PCGNaniteOptimizer_eventSetCullingParameters_Parms
	{
		float DistanceThreshold;
		bool bEnableOcclusion;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Culling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Culling and Visibility ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Culling and Visibility ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceThreshold;
	static void NewProp_bEnableOcclusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusion;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_DistanceThreshold = { "DistanceThreshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetCullingParameters_Parms, DistanceThreshold), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_bEnableOcclusion_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventSetCullingParameters_Parms*)Obj)->bEnableOcclusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_bEnableOcclusion = { "bEnableOcclusion", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventSetCullingParameters_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_bEnableOcclusion_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventSetCullingParameters_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventSetCullingParameters_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_DistanceThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_bEnableOcclusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "SetCullingParameters", Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::PCGNaniteOptimizer_eventSetCullingParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::PCGNaniteOptimizer_eventSetCullingParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execSetCullingParameters)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DistanceThreshold);
	P_GET_UBOOL(Z_Param_bEnableOcclusion);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetCullingParameters(Z_Param_DistanceThreshold,Z_Param_bEnableOcclusion);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function SetCullingParameters **************************

// ********** Begin Class APCGNaniteOptimizer Function SetInstanceLODBias **************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics
{
	struct PCGNaniteOptimizer_eventSetInstanceLODBias_Parms
	{
		FTransform InstanceTransform;
		float LODBias;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Management" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransform;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODBias;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_InstanceTransform = { "InstanceTransform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetInstanceLODBias_Parms, InstanceTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransform_MetaData), NewProp_InstanceTransform_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_LODBias = { "LODBias", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetInstanceLODBias_Parms, LODBias), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventSetInstanceLODBias_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventSetInstanceLODBias_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_InstanceTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_LODBias,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "SetInstanceLODBias", Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::PCGNaniteOptimizer_eventSetInstanceLODBias_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::PCGNaniteOptimizer_eventSetInstanceLODBias_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execSetInstanceLODBias)
{
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_InstanceTransform);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LODBias);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetInstanceLODBias(Z_Param_Out_InstanceTransform,Z_Param_LODBias);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function SetInstanceLODBias ****************************

// ********** Begin Class APCGNaniteOptimizer Function SetLODStrategy ******************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics
{
	struct PCGNaniteOptimizer_eventSetLODStrategy_Parms
	{
		EPCGNaniteLODStrategy NewStrategy;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === LOD Management ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== LOD Management ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewStrategy;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_NewStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_NewStrategy = { "NewStrategy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetLODStrategy_Parms, NewStrategy), Z_Construct_UEnum_Aura_EPCGNaniteLODStrategy, METADATA_PARAMS(0, nullptr) }; // 192784107
void Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventSetLODStrategy_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventSetLODStrategy_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_NewStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_NewStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "SetLODStrategy", Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::PCGNaniteOptimizer_eventSetLODStrategy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::PCGNaniteOptimizer_eventSetLODStrategy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execSetLODStrategy)
{
	P_GET_ENUM(EPCGNaniteLODStrategy,Z_Param_NewStrategy);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetLODStrategy(EPCGNaniteLODStrategy(Z_Param_NewStrategy));
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function SetLODStrategy ********************************

// ********** Begin Class APCGNaniteOptimizer Function SetMemoryBudget *****************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics
{
	struct PCGNaniteOptimizer_eventSetMemoryBudget_Parms
	{
		float MemoryBudgetMB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Memory Management ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Memory Management ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryBudgetMB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::NewProp_MemoryBudgetMB = { "MemoryBudgetMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetMemoryBudget_Parms, MemoryBudgetMB), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventSetMemoryBudget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventSetMemoryBudget_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::NewProp_MemoryBudgetMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "SetMemoryBudget", Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::PCGNaniteOptimizer_eventSetMemoryBudget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::PCGNaniteOptimizer_eventSetMemoryBudget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execSetMemoryBudget)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_MemoryBudgetMB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetMemoryBudget(Z_Param_MemoryBudgetMB);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function SetMemoryBudget *******************************

// ********** Begin Class APCGNaniteOptimizer Function SetOptimizationConfig ***********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics
{
	struct PCGNaniteOptimizer_eventSetOptimizationConfig_Parms
	{
		FPCGNaniteOptimizationConfig NewConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nanite" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetOptimizationConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 3674028056
void Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventSetOptimizationConfig_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventSetOptimizationConfig_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::NewProp_NewConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "SetOptimizationConfig", Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::PCGNaniteOptimizer_eventSetOptimizationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::PCGNaniteOptimizer_eventSetOptimizationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execSetOptimizationConfig)
{
	P_GET_STRUCT_REF(FPCGNaniteOptimizationConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetOptimizationConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function SetOptimizationConfig *************************

// ********** Begin Class APCGNaniteOptimizer Function SetProceduralMapGenerator *******************
struct Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics
{
	struct PCGNaniteOptimizer_eventSetProceduralMapGenerator_Parms
	{
		AProceduralMapGenerator* Generator;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Integration Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Integration Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Generator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::NewProp_Generator = { "Generator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetProceduralMapGenerator_Parms, Generator), Z_Construct_UClass_AProceduralMapGenerator_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::NewProp_Generator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "SetProceduralMapGenerator", Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::PCGNaniteOptimizer_eventSetProceduralMapGenerator_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::PCGNaniteOptimizer_eventSetProceduralMapGenerator_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execSetProceduralMapGenerator)
{
	P_GET_OBJECT(AProceduralMapGenerator,Z_Param_Generator);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetProceduralMapGenerator(Z_Param_Generator);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function SetProceduralMapGenerator *********************

// ********** Begin Class APCGNaniteOptimizer Function SetWorldPartitionManager ********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics
{
	struct PCGNaniteOptimizer_eventSetWorldPartitionManager_Parms
	{
		APCGWorldPartitionManager* Manager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Manager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::NewProp_Manager = { "Manager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventSetWorldPartitionManager_Parms, Manager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::NewProp_Manager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "SetWorldPartitionManager", Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::PCGNaniteOptimizer_eventSetWorldPartitionManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::PCGNaniteOptimizer_eventSetWorldPartitionManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execSetWorldPartitionManager)
{
	P_GET_OBJECT(APCGWorldPartitionManager,Z_Param_Manager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWorldPartitionManager(Z_Param_Manager);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function SetWorldPartitionManager **********************

// ********** Begin Class APCGNaniteOptimizer Function ShutdownNaniteOptimizer *********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_ShutdownNaniteOptimizer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Nanite" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_ShutdownNaniteOptimizer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "ShutdownNaniteOptimizer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ShutdownNaniteOptimizer_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_ShutdownNaniteOptimizer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_ShutdownNaniteOptimizer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_ShutdownNaniteOptimizer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execShutdownNaniteOptimizer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownNaniteOptimizer();
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function ShutdownNaniteOptimizer ***********************

// ********** Begin Class APCGNaniteOptimizer Function StreamNaniteData ****************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics
{
	struct PCGNaniteOptimizer_eventStreamNaniteData_Parms
	{
		FVector StreamingCenter;
		float StreamingRadius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingRadius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_StreamingCenter = { "StreamingCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventStreamNaniteData_Parms, StreamingCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingCenter_MetaData), NewProp_StreamingCenter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_StreamingRadius = { "StreamingRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventStreamNaniteData_Parms, StreamingRadius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventStreamNaniteData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventStreamNaniteData_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_StreamingCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_StreamingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "StreamNaniteData", Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::PCGNaniteOptimizer_eventStreamNaniteData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::PCGNaniteOptimizer_eventStreamNaniteData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execStreamNaniteData)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StreamingCenter);
	P_GET_PROPERTY(FFloatProperty,Z_Param_StreamingRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StreamNaniteData(Z_Param_Out_StreamingCenter,Z_Param_StreamingRadius);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function StreamNaniteData ******************************

// ********** Begin Class APCGNaniteOptimizer Function UnloadDistantNaniteData *********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics
{
	struct PCGNaniteOptimizer_eventUnloadDistantNaniteData_Parms
	{
		FVector ViewerLocation;
		float UnloadDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUnloadDistantNaniteData_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::NewProp_UnloadDistance = { "UnloadDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUnloadDistantNaniteData_Parms, UnloadDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::NewProp_UnloadDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "UnloadDistantNaniteData", Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::PCGNaniteOptimizer_eventUnloadDistantNaniteData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::PCGNaniteOptimizer_eventUnloadDistantNaniteData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execUnloadDistantNaniteData)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_UnloadDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnloadDistantNaniteData(Z_Param_Out_ViewerLocation,Z_Param_UnloadDistance);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function UnloadDistantNaniteData ***********************

// ********** Begin Class APCGNaniteOptimizer Function UnregisterNaniteInstance ********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics
{
	struct PCGNaniteOptimizer_eventUnregisterNaniteInstance_Parms
	{
		FTransform InstanceTransform;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Instance Management" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransform;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::NewProp_InstanceTransform = { "InstanceTransform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUnregisterNaniteInstance_Parms, InstanceTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransform_MetaData), NewProp_InstanceTransform_MetaData) };
void Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventUnregisterNaniteInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventUnregisterNaniteInstance_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::NewProp_InstanceTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "UnregisterNaniteInstance", Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::PCGNaniteOptimizer_eventUnregisterNaniteInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::PCGNaniteOptimizer_eventUnregisterNaniteInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execUnregisterNaniteInstance)
{
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_InstanceTransform);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterNaniteInstance(Z_Param_Out_InstanceTransform);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function UnregisterNaniteInstance **********************

// ********** Begin Class APCGNaniteOptimizer Function UpdateInstanceVisibility ********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics
{
	struct PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms
	{
		FTransform InstanceTransform;
		bool bIsVisible;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransform;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_InstanceTransform = { "InstanceTransform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms, InstanceTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransform_MetaData), NewProp_InstanceTransform_MetaData) };
void Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_InstanceTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "UpdateInstanceVisibility", Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::PCGNaniteOptimizer_eventUpdateInstanceVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execUpdateInstanceVisibility)
{
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_InstanceTransform);
	P_GET_UBOOL(Z_Param_bIsVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateInstanceVisibility(Z_Param_Out_InstanceTransform,Z_Param_bIsVisible);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function UpdateInstanceVisibility **********************

// ********** Begin Class APCGNaniteOptimizer Function UpdateLODParameters *************************
struct Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics
{
	struct PCGNaniteOptimizer_eventUpdateLODParameters_Parms
	{
		float TargetScreenSize;
		float LODBias;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Management" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetScreenSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODBias;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_TargetScreenSize = { "TargetScreenSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUpdateLODParameters_Parms, TargetScreenSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_LODBias = { "LODBias", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUpdateLODParameters_Parms, LODBias), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventUpdateLODParameters_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventUpdateLODParameters_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_TargetScreenSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_LODBias,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "UpdateLODParameters", Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::PCGNaniteOptimizer_eventUpdateLODParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::PCGNaniteOptimizer_eventUpdateLODParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execUpdateLODParameters)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TargetScreenSize);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LODBias);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateLODParameters(Z_Param_TargetScreenSize,Z_Param_LODBias);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function UpdateLODParameters ***************************

// ********** Begin Class APCGNaniteOptimizer Function UpdateVisibilityFromViewpoint ***************
struct Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics
{
	struct PCGNaniteOptimizer_eventUpdateVisibilityFromViewpoint_Parms
	{
		FVector ViewerLocation;
		FVector ViewDirection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewDirection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUpdateVisibilityFromViewpoint_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::NewProp_ViewDirection = { "ViewDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventUpdateVisibilityFromViewpoint_Parms, ViewDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewDirection_MetaData), NewProp_ViewDirection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::NewProp_ViewDirection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "UpdateVisibilityFromViewpoint", Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::PCGNaniteOptimizer_eventUpdateVisibilityFromViewpoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::PCGNaniteOptimizer_eventUpdateVisibilityFromViewpoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execUpdateVisibilityFromViewpoint)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateVisibilityFromViewpoint(Z_Param_Out_ViewerLocation,Z_Param_Out_ViewDirection);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function UpdateVisibilityFromViewpoint *****************

// ********** Begin Class APCGNaniteOptimizer Function ValidateMeshForNanite ***********************
struct Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics
{
	struct PCGNaniteOptimizer_eventValidateMeshForNanite_Parms
	{
		UStaticMesh* Mesh;
		FString OutValidationResult;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Conversion" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutValidationResult;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventValidateMeshForNanite_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_OutValidationResult = { "OutValidationResult", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGNaniteOptimizer_eventValidateMeshForNanite_Parms, OutValidationResult), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGNaniteOptimizer_eventValidateMeshForNanite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGNaniteOptimizer_eventValidateMeshForNanite_Parms), &Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_OutValidationResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGNaniteOptimizer, nullptr, "ValidateMeshForNanite", Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::PCGNaniteOptimizer_eventValidateMeshForNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::PCGNaniteOptimizer_eventValidateMeshForNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGNaniteOptimizer::execValidateMeshForNanite)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_OutValidationResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateMeshForNanite(Z_Param_Mesh,Z_Param_Out_OutValidationResult);
	P_NATIVE_END;
}
// ********** End Class APCGNaniteOptimizer Function ValidateMeshForNanite *************************

// ********** Begin Class APCGNaniteOptimizer ******************************************************
void APCGNaniteOptimizer::StaticRegisterNativesAPCGNaniteOptimizer()
{
	UClass* Class = APCGNaniteOptimizer::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BatchConvertMeshesToNanite", &APCGNaniteOptimizer::execBatchConvertMeshesToNanite },
		{ "CalculateOptimalLODBias", &APCGNaniteOptimizer::execCalculateOptimalLODBias },
		{ "ClearAllNaniteInstances", &APCGNaniteOptimizer::execClearAllNaniteInstances },
		{ "ConvertMeshToNanite", &APCGNaniteOptimizer::execConvertMeshToNanite },
		{ "EnablePerformanceMonitoring", &APCGNaniteOptimizer::execEnablePerformanceMonitoring },
		{ "ExportNaniteOptimizationReport", &APCGNaniteOptimizer::execExportNaniteOptimizationReport },
		{ "GetCompressionEfficiency", &APCGNaniteOptimizer::execGetCompressionEfficiency },
		{ "GetGPUMemoryUsage", &APCGNaniteOptimizer::execGetGPUMemoryUsage },
		{ "GetNaniteInstanceCount", &APCGNaniteOptimizer::execGetNaniteInstanceCount },
		{ "GetNaniteInstances", &APCGNaniteOptimizer::execGetNaniteInstances },
		{ "GetNaniteSystemInfo", &APCGNaniteOptimizer::execGetNaniteSystemInfo },
		{ "GetOptimizationConfig", &APCGNaniteOptimizer::execGetOptimizationConfig },
		{ "GetPerformanceStats", &APCGNaniteOptimizer::execGetPerformanceStats },
		{ "GetVisibleInstances", &APCGNaniteOptimizer::execGetVisibleInstances },
		{ "InitializeNaniteOptimizer", &APCGNaniteOptimizer::execInitializeNaniteOptimizer },
		{ "IntegrateWithPCGComponent", &APCGNaniteOptimizer::execIntegrateWithPCGComponent },
		{ "IsMeshNaniteEnabled", &APCGNaniteOptimizer::execIsMeshNaniteEnabled },
		{ "IsNaniteSupported", &APCGNaniteOptimizer::execIsNaniteSupported },
		{ "OnMeshConversionComplete", &APCGNaniteOptimizer::execOnMeshConversionComplete },
		{ "OnNaniteStreamingUpdate", &APCGNaniteOptimizer::execOnNaniteStreamingUpdate },
		{ "OnPerformanceThresholdExceeded", &APCGNaniteOptimizer::execOnPerformanceThresholdExceeded },
		{ "OptimizeMemoryUsage", &APCGNaniteOptimizer::execOptimizeMemoryUsage },
		{ "OptimizeMeshForNanite", &APCGNaniteOptimizer::execOptimizeMeshForNanite },
		{ "ProcessPCGGeneratedMeshes", &APCGNaniteOptimizer::execProcessPCGGeneratedMeshes },
		{ "RegisterNaniteInstance", &APCGNaniteOptimizer::execRegisterNaniteInstance },
		{ "ResetPerformanceStats", &APCGNaniteOptimizer::execResetPerformanceStats },
		{ "SetCullingParameters", &APCGNaniteOptimizer::execSetCullingParameters },
		{ "SetInstanceLODBias", &APCGNaniteOptimizer::execSetInstanceLODBias },
		{ "SetLODStrategy", &APCGNaniteOptimizer::execSetLODStrategy },
		{ "SetMemoryBudget", &APCGNaniteOptimizer::execSetMemoryBudget },
		{ "SetOptimizationConfig", &APCGNaniteOptimizer::execSetOptimizationConfig },
		{ "SetProceduralMapGenerator", &APCGNaniteOptimizer::execSetProceduralMapGenerator },
		{ "SetWorldPartitionManager", &APCGNaniteOptimizer::execSetWorldPartitionManager },
		{ "ShutdownNaniteOptimizer", &APCGNaniteOptimizer::execShutdownNaniteOptimizer },
		{ "StreamNaniteData", &APCGNaniteOptimizer::execStreamNaniteData },
		{ "UnloadDistantNaniteData", &APCGNaniteOptimizer::execUnloadDistantNaniteData },
		{ "UnregisterNaniteInstance", &APCGNaniteOptimizer::execUnregisterNaniteInstance },
		{ "UpdateInstanceVisibility", &APCGNaniteOptimizer::execUpdateInstanceVisibility },
		{ "UpdateLODParameters", &APCGNaniteOptimizer::execUpdateLODParameters },
		{ "UpdateVisibilityFromViewpoint", &APCGNaniteOptimizer::execUpdateVisibilityFromViewpoint },
		{ "ValidateMeshForNanite", &APCGNaniteOptimizer::execValidateMeshForNanite },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_APCGNaniteOptimizer;
UClass* APCGNaniteOptimizer::GetPrivateStaticClass()
{
	using TClass = APCGNaniteOptimizer;
	if (!Z_Registration_Info_UClass_APCGNaniteOptimizer.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGNaniteOptimizer"),
			Z_Registration_Info_UClass_APCGNaniteOptimizer.InnerSingleton,
			StaticRegisterNativesAPCGNaniteOptimizer,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_APCGNaniteOptimizer.InnerSingleton;
}
UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister()
{
	return APCGNaniteOptimizer::GetPrivateStaticClass();
}
struct Z_Construct_UClass_APCGNaniteOptimizer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * APCGNaniteOptimizer\n * \n * Advanced Nanite optimization system for PCG-generated content.\n * Provides automatic conversion, optimization, and management of static meshes\n * for Nanite virtualized geometry in UE5.6.\n * \n * Features:\n * - Automatic Nanite mesh conversion and optimization\n * - Intelligent LOD strategy management\n * - GPU-driven rendering optimizations\n * - Memory-efficient streaming and caching\n * - Performance monitoring and analytics\n * - Fallback mesh support for non-Nanite hardware\n * - Integration with World Partition and PCG systems\n * - Real-time optimization parameter tuning\n */" },
#endif
		{ "IncludePath", "APCGNaniteOptimizer.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "APCGNaniteOptimizer\n\nAdvanced Nanite optimization system for PCG-generated content.\nProvides automatic conversion, optimization, and management of static meshes\nfor Nanite virtualized geometry in UE5.6.\n\nFeatures:\n- Automatic Nanite mesh conversion and optimization\n- Intelligent LOD strategy management\n- GPU-driven rendering optimizations\n- Memory-efficient streaming and caching\n- Performance monitoring and analytics\n- Fallback mesh support for non-Nanite hardware\n- Integration with World Partition and PCG systems\n- Real-time optimization parameter tuning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Core Properties ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Core Properties ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsOptimizing_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessedMeshes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Mesh Data Storage ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Mesh Data Storage ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteInstances_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceStats_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Performance Tracking ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Performance Tracking ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPerformanceMonitoringEnabled_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralMapGenerator_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Integration References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Integration References ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntegratedPCGComponents_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGNaniteOptimizer.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OptimizationConfig;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bIsOptimizing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOptimizing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProcessedMeshes_ValueProp;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ProcessedMeshes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ProcessedMeshes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NaniteInstances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NaniteInstances;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceStats;
	static void NewProp_bPerformanceMonitoringEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPerformanceMonitoringEnabled;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ProceduralMapGenerator;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_IntegratedPCGComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_IntegratedPCGComponents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_BatchConvertMeshesToNanite, "BatchConvertMeshesToNanite" }, // 1205933246
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_CalculateOptimalLODBias, "CalculateOptimalLODBias" }, // 614424
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_ClearAllNaniteInstances, "ClearAllNaniteInstances" }, // 2466531624
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_ConvertMeshToNanite, "ConvertMeshToNanite" }, // 2871059620
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_EnablePerformanceMonitoring, "EnablePerformanceMonitoring" }, // 4088261644
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_ExportNaniteOptimizationReport, "ExportNaniteOptimizationReport" }, // 3672649086
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetCompressionEfficiency, "GetCompressionEfficiency" }, // 1084967772
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetGPUMemoryUsage, "GetGPUMemoryUsage" }, // 3397441715
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstanceCount, "GetNaniteInstanceCount" }, // 4265798816
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteInstances, "GetNaniteInstances" }, // 1924122194
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetNaniteSystemInfo, "GetNaniteSystemInfo" }, // 1991311980
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetOptimizationConfig, "GetOptimizationConfig" }, // 52318043
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetPerformanceStats, "GetPerformanceStats" }, // 3849361074
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_GetVisibleInstances, "GetVisibleInstances" }, // 2797328290
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_InitializeNaniteOptimizer, "InitializeNaniteOptimizer" }, // 1123473097
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_IntegrateWithPCGComponent, "IntegrateWithPCGComponent" }, // 427348270
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_IsMeshNaniteEnabled, "IsMeshNaniteEnabled" }, // 75941318
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_IsNaniteSupported, "IsNaniteSupported" }, // 1379755895
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_OnMeshConversionComplete, "OnMeshConversionComplete" }, // 3817119682
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_OnNaniteStreamingUpdate, "OnNaniteStreamingUpdate" }, // 3390294574
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_OnPerformanceThresholdExceeded, "OnPerformanceThresholdExceeded" }, // 3969511805
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMemoryUsage, "OptimizeMemoryUsage" }, // 3390625711
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_OptimizeMeshForNanite, "OptimizeMeshForNanite" }, // 246448458
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_ProcessPCGGeneratedMeshes, "ProcessPCGGeneratedMeshes" }, // 2186192767
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_RegisterNaniteInstance, "RegisterNaniteInstance" }, // 2041283897
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_ResetPerformanceStats, "ResetPerformanceStats" }, // 2837571083
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_SetCullingParameters, "SetCullingParameters" }, // 3364707957
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_SetInstanceLODBias, "SetInstanceLODBias" }, // 497839194
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_SetLODStrategy, "SetLODStrategy" }, // 278157287
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_SetMemoryBudget, "SetMemoryBudget" }, // 3774854031
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_SetOptimizationConfig, "SetOptimizationConfig" }, // 3046978390
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_SetProceduralMapGenerator, "SetProceduralMapGenerator" }, // 2223287766
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_SetWorldPartitionManager, "SetWorldPartitionManager" }, // 1690776541
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_ShutdownNaniteOptimizer, "ShutdownNaniteOptimizer" }, // 678044363
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_StreamNaniteData, "StreamNaniteData" }, // 1797409298
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_UnloadDistantNaniteData, "UnloadDistantNaniteData" }, // 664036018
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_UnregisterNaniteInstance, "UnregisterNaniteInstance" }, // 109483085
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_UpdateInstanceVisibility, "UpdateInstanceVisibility" }, // 2309586079
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_UpdateLODParameters, "UpdateLODParameters" }, // 2521339746
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_UpdateVisibilityFromViewpoint, "UpdateVisibilityFromViewpoint" }, // 3373412558
		{ &Z_Construct_UFunction_APCGNaniteOptimizer_ValidateMeshForNanite, "ValidateMeshForNanite" }, // 3420773716
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APCGNaniteOptimizer>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_OptimizationConfig = { "OptimizationConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGNaniteOptimizer, OptimizationConfig), Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationConfig_MetaData), NewProp_OptimizationConfig_MetaData) }; // 3674028056
void Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((APCGNaniteOptimizer*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGNaniteOptimizer), &Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsOptimizing_SetBit(void* Obj)
{
	((APCGNaniteOptimizer*)Obj)->bIsOptimizing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsOptimizing = { "bIsOptimizing", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGNaniteOptimizer), &Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsOptimizing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsOptimizing_MetaData), NewProp_bIsOptimizing_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProcessedMeshes_ValueProp = { "ProcessedMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGNaniteMeshData, METADATA_PARAMS(0, nullptr) }; // 1470135679
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProcessedMeshes_Key_KeyProp = { "ProcessedMeshes_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProcessedMeshes = { "ProcessedMeshes", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGNaniteOptimizer, ProcessedMeshes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessedMeshes_MetaData), NewProp_ProcessedMeshes_MetaData) }; // 1470135679
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_NaniteInstances_Inner = { "NaniteInstances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGNaniteInstanceData, METADATA_PARAMS(0, nullptr) }; // 4189694740
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_NaniteInstances = { "NaniteInstances", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGNaniteOptimizer, NaniteInstances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteInstances_MetaData), NewProp_NaniteInstances_MetaData) }; // 4189694740
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_PerformanceStats = { "PerformanceStats", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGNaniteOptimizer, PerformanceStats), Z_Construct_UScriptStruct_FPCGNanitePerformanceStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceStats_MetaData), NewProp_PerformanceStats_MetaData) }; // 1030788720
void Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bPerformanceMonitoringEnabled_SetBit(void* Obj)
{
	((APCGNaniteOptimizer*)Obj)->bPerformanceMonitoringEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bPerformanceMonitoringEnabled = { "bPerformanceMonitoringEnabled", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGNaniteOptimizer), &Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bPerformanceMonitoringEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPerformanceMonitoringEnabled_MetaData), NewProp_bPerformanceMonitoringEnabled_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProceduralMapGenerator = { "ProceduralMapGenerator", nullptr, (EPropertyFlags)0x0024080000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGNaniteOptimizer, ProceduralMapGenerator), Z_Construct_UClass_AProceduralMapGenerator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralMapGenerator_MetaData), NewProp_ProceduralMapGenerator_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0024080000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGNaniteOptimizer, WorldPartitionManager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionManager_MetaData), NewProp_WorldPartitionManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_IntegratedPCGComponents_Inner = { "IntegratedPCGComponents", nullptr, (EPropertyFlags)0x0004000000080008, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_IntegratedPCGComponents = { "IntegratedPCGComponents", nullptr, (EPropertyFlags)0x0024088000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGNaniteOptimizer, IntegratedPCGComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntegratedPCGComponents_MetaData), NewProp_IntegratedPCGComponents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APCGNaniteOptimizer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_OptimizationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bIsOptimizing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProcessedMeshes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProcessedMeshes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProcessedMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_NaniteInstances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_NaniteInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_PerformanceStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_bPerformanceMonitoringEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_ProceduralMapGenerator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_WorldPartitionManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_IntegratedPCGComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGNaniteOptimizer_Statics::NewProp_IntegratedPCGComponents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGNaniteOptimizer_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APCGNaniteOptimizer_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGNaniteOptimizer_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_APCGNaniteOptimizer_Statics::ClassParams = {
	&APCGNaniteOptimizer::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APCGNaniteOptimizer_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APCGNaniteOptimizer_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APCGNaniteOptimizer_Statics::Class_MetaDataParams), Z_Construct_UClass_APCGNaniteOptimizer_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APCGNaniteOptimizer()
{
	if (!Z_Registration_Info_UClass_APCGNaniteOptimizer.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APCGNaniteOptimizer.OuterSingleton, Z_Construct_UClass_APCGNaniteOptimizer_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APCGNaniteOptimizer.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APCGNaniteOptimizer);
APCGNaniteOptimizer::~APCGNaniteOptimizer() {}
// ********** End Class APCGNaniteOptimizer ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGNaniteOptimizationLevel_StaticEnum, TEXT("EPCGNaniteOptimizationLevel"), &Z_Registration_Info_UEnum_EPCGNaniteOptimizationLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3386825555U) },
		{ EPCGNaniteLODStrategy_StaticEnum, TEXT("EPCGNaniteLODStrategy"), &Z_Registration_Info_UEnum_EPCGNaniteLODStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 192784107U) },
		{ EPCGNaniteClusteringMode_StaticEnum, TEXT("EPCGNaniteClusteringMode"), &Z_Registration_Info_UEnum_EPCGNaniteClusteringMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1923857438U) },
		{ EPCGNaniteCompressionLevel_StaticEnum, TEXT("EPCGNaniteCompressionLevel"), &Z_Registration_Info_UEnum_EPCGNaniteCompressionLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3064305711U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGNaniteOptimizationConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics::NewStructOps, TEXT("PCGNaniteOptimizationConfig"), &Z_Registration_Info_UScriptStruct_FPCGNaniteOptimizationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGNaniteOptimizationConfig), 3674028056U) },
		{ FPCGNaniteMeshData::StaticStruct, Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics::NewStructOps, TEXT("PCGNaniteMeshData"), &Z_Registration_Info_UScriptStruct_FPCGNaniteMeshData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGNaniteMeshData), 1470135679U) },
		{ FPCGNaniteInstanceData::StaticStruct, Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics::NewStructOps, TEXT("PCGNaniteInstanceData"), &Z_Registration_Info_UScriptStruct_FPCGNaniteInstanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGNaniteInstanceData), 4189694740U) },
		{ FPCGNanitePerformanceStats::StaticStruct, Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics::NewStructOps, TEXT("PCGNanitePerformanceStats"), &Z_Registration_Info_UScriptStruct_FPCGNanitePerformanceStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGNanitePerformanceStats), 1030788720U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APCGNaniteOptimizer, APCGNaniteOptimizer::StaticClass, TEXT("APCGNaniteOptimizer"), &Z_Registration_Info_UClass_APCGNaniteOptimizer, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APCGNaniteOptimizer), 3174289543U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_905705867(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
