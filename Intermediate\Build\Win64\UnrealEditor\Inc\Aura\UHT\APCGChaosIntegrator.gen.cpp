// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "APCGChaosIntegrator.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAPCGChaosIntegrator() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGCacheManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGChaosIntegrator();
AURA_API UClass* Z_Construct_UClass_APCGChaosIntegrator_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsBodyType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsEventType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsOptimization();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsQuality();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsBodyData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsConstraintData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsEventData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
PHYSICSCORE_API UClass* Z_Construct_UClass_UPhysicalMaterial_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGPhysicsSimulationMode *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode;
static UEnum* EPCGPhysicsSimulationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGPhysicsSimulationMode"));
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsSimulationMode>()
{
	return EPCGPhysicsSimulationMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics simulation modes\n" },
#endif
		{ "Dynamic.DisplayName", "Dynamic" },
		{ "Dynamic.Name", "EPCGPhysicsSimulationMode::Dynamic" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EPCGPhysicsSimulationMode::Hybrid" },
		{ "Kinematic.DisplayName", "Kinematic" },
		{ "Kinematic.Name", "EPCGPhysicsSimulationMode::Kinematic" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "EPCGPhysicsSimulationMode::Procedural" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EPCGPhysicsSimulationMode::Static" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics simulation modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGPhysicsSimulationMode::Static", (int64)EPCGPhysicsSimulationMode::Static },
		{ "EPCGPhysicsSimulationMode::Kinematic", (int64)EPCGPhysicsSimulationMode::Kinematic },
		{ "EPCGPhysicsSimulationMode::Dynamic", (int64)EPCGPhysicsSimulationMode::Dynamic },
		{ "EPCGPhysicsSimulationMode::Procedural", (int64)EPCGPhysicsSimulationMode::Procedural },
		{ "EPCGPhysicsSimulationMode::Hybrid", (int64)EPCGPhysicsSimulationMode::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGPhysicsSimulationMode",
	"EPCGPhysicsSimulationMode",
	Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode.InnerSingleton;
}
// ********** End Enum EPCGPhysicsSimulationMode ***************************************************

// ********** Begin Enum EPCGPhysicsBodyType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGPhysicsBodyType;
static UEnum* EPCGPhysicsBodyType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsBodyType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGPhysicsBodyType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGPhysicsBodyType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGPhysicsBodyType"));
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsBodyType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsBodyType>()
{
	return EPCGPhysicsBodyType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGPhysicsBodyType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EPCGPhysicsBodyType::Box" },
		{ "Capsule.DisplayName", "Capsule" },
		{ "Capsule.Name", "EPCGPhysicsBodyType::Capsule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics body types\n" },
#endif
		{ "Convex.DisplayName", "Convex" },
		{ "Convex.Name", "EPCGPhysicsBodyType::Convex" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EPCGPhysicsBodyType::Custom" },
		{ "Heightfield.DisplayName", "Heightfield" },
		{ "Heightfield.Name", "EPCGPhysicsBodyType::Heightfield" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
		{ "Sphere.DisplayName", "Sphere" },
		{ "Sphere.Name", "EPCGPhysicsBodyType::Sphere" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics body types" },
#endif
		{ "TriangleMesh.DisplayName", "Triangle Mesh" },
		{ "TriangleMesh.Name", "EPCGPhysicsBodyType::TriangleMesh" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGPhysicsBodyType::Box", (int64)EPCGPhysicsBodyType::Box },
		{ "EPCGPhysicsBodyType::Sphere", (int64)EPCGPhysicsBodyType::Sphere },
		{ "EPCGPhysicsBodyType::Capsule", (int64)EPCGPhysicsBodyType::Capsule },
		{ "EPCGPhysicsBodyType::Convex", (int64)EPCGPhysicsBodyType::Convex },
		{ "EPCGPhysicsBodyType::TriangleMesh", (int64)EPCGPhysicsBodyType::TriangleMesh },
		{ "EPCGPhysicsBodyType::Heightfield", (int64)EPCGPhysicsBodyType::Heightfield },
		{ "EPCGPhysicsBodyType::Custom", (int64)EPCGPhysicsBodyType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGPhysicsBodyType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGPhysicsBodyType",
	"EPCGPhysicsBodyType",
	Z_Construct_UEnum_Aura_EPCGPhysicsBodyType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsBodyType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsBodyType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGPhysicsBodyType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsBodyType()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsBodyType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGPhysicsBodyType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGPhysicsBodyType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsBodyType.InnerSingleton;
}
// ********** End Enum EPCGPhysicsBodyType *********************************************************

// ********** Begin Enum EPCGPhysicsConstraintType *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGPhysicsConstraintType;
static UEnum* EPCGPhysicsConstraintType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsConstraintType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGPhysicsConstraintType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGPhysicsConstraintType"));
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsConstraintType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsConstraintType>()
{
	return EPCGPhysicsConstraintType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics constraint types\n" },
#endif
		{ "Distance.DisplayName", "Distance" },
		{ "Distance.Name", "EPCGPhysicsConstraintType::Distance" },
		{ "Fixed.DisplayName", "Fixed" },
		{ "Fixed.Name", "EPCGPhysicsConstraintType::Fixed" },
		{ "Hinge.DisplayName", "Hinge" },
		{ "Hinge.Name", "EPCGPhysicsConstraintType::Hinge" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
		{ "Prismatic.DisplayName", "Prismatic" },
		{ "Prismatic.Name", "EPCGPhysicsConstraintType::Prismatic" },
		{ "Spherical.DisplayName", "Spherical" },
		{ "Spherical.Name", "EPCGPhysicsConstraintType::Spherical" },
		{ "Spring.DisplayName", "Spring" },
		{ "Spring.Name", "EPCGPhysicsConstraintType::Spring" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics constraint types" },
#endif
		{ "Universal.DisplayName", "Universal" },
		{ "Universal.Name", "EPCGPhysicsConstraintType::Universal" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGPhysicsConstraintType::Fixed", (int64)EPCGPhysicsConstraintType::Fixed },
		{ "EPCGPhysicsConstraintType::Hinge", (int64)EPCGPhysicsConstraintType::Hinge },
		{ "EPCGPhysicsConstraintType::Prismatic", (int64)EPCGPhysicsConstraintType::Prismatic },
		{ "EPCGPhysicsConstraintType::Spherical", (int64)EPCGPhysicsConstraintType::Spherical },
		{ "EPCGPhysicsConstraintType::Universal", (int64)EPCGPhysicsConstraintType::Universal },
		{ "EPCGPhysicsConstraintType::Distance", (int64)EPCGPhysicsConstraintType::Distance },
		{ "EPCGPhysicsConstraintType::Spring", (int64)EPCGPhysicsConstraintType::Spring },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGPhysicsConstraintType",
	"EPCGPhysicsConstraintType",
	Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsConstraintType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGPhysicsConstraintType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsConstraintType.InnerSingleton;
}
// ********** End Enum EPCGPhysicsConstraintType ***************************************************

// ********** Begin Enum EPCGPhysicsQuality ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGPhysicsQuality;
static UEnum* EPCGPhysicsQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGPhysicsQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGPhysicsQuality, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGPhysicsQuality"));
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsQuality.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsQuality>()
{
	return EPCGPhysicsQuality_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGPhysicsQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EPCGPhysicsQuality::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics quality levels\n" },
#endif
		{ "High.DisplayName", "High" },
		{ "High.Name", "EPCGPhysicsQuality::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EPCGPhysicsQuality::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EPCGPhysicsQuality::Medium" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics quality levels" },
#endif
		{ "Ultra.DisplayName", "Ultra" },
		{ "Ultra.Name", "EPCGPhysicsQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGPhysicsQuality::Low", (int64)EPCGPhysicsQuality::Low },
		{ "EPCGPhysicsQuality::Medium", (int64)EPCGPhysicsQuality::Medium },
		{ "EPCGPhysicsQuality::High", (int64)EPCGPhysicsQuality::High },
		{ "EPCGPhysicsQuality::Ultra", (int64)EPCGPhysicsQuality::Ultra },
		{ "EPCGPhysicsQuality::Adaptive", (int64)EPCGPhysicsQuality::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGPhysicsQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGPhysicsQuality",
	"EPCGPhysicsQuality",
	Z_Construct_UEnum_Aura_EPCGPhysicsQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGPhysicsQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsQuality()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGPhysicsQuality.InnerSingleton, Z_Construct_UEnum_Aura_EPCGPhysicsQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsQuality.InnerSingleton;
}
// ********** End Enum EPCGPhysicsQuality **********************************************************

// ********** Begin Enum EPCGPhysicsOptimization ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGPhysicsOptimization;
static UEnum* EPCGPhysicsOptimization_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsOptimization.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGPhysicsOptimization.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGPhysicsOptimization, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGPhysicsOptimization"));
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsOptimization.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsOptimization>()
{
	return EPCGPhysicsOptimization_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGPhysicsOptimization_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Clustering.DisplayName", "Clustering" },
		{ "Clustering.Name", "EPCGPhysicsOptimization::Clustering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics optimization strategies\n" },
#endif
		{ "Culling.DisplayName", "Culling" },
		{ "Culling.Name", "EPCGPhysicsOptimization::Culling" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EPCGPhysicsOptimization::Hybrid" },
		{ "Instancing.DisplayName", "Instancing" },
		{ "Instancing.Name", "EPCGPhysicsOptimization::Instancing" },
		{ "LOD.DisplayName", "Level of Detail" },
		{ "LOD.Name", "EPCGPhysicsOptimization::LOD" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EPCGPhysicsOptimization::None" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EPCGPhysicsOptimization::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics optimization strategies" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGPhysicsOptimization::None", (int64)EPCGPhysicsOptimization::None },
		{ "EPCGPhysicsOptimization::LOD", (int64)EPCGPhysicsOptimization::LOD },
		{ "EPCGPhysicsOptimization::Culling", (int64)EPCGPhysicsOptimization::Culling },
		{ "EPCGPhysicsOptimization::Clustering", (int64)EPCGPhysicsOptimization::Clustering },
		{ "EPCGPhysicsOptimization::Instancing", (int64)EPCGPhysicsOptimization::Instancing },
		{ "EPCGPhysicsOptimization::Streaming", (int64)EPCGPhysicsOptimization::Streaming },
		{ "EPCGPhysicsOptimization::Hybrid", (int64)EPCGPhysicsOptimization::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGPhysicsOptimization_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGPhysicsOptimization",
	"EPCGPhysicsOptimization",
	Z_Construct_UEnum_Aura_EPCGPhysicsOptimization_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsOptimization_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsOptimization_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGPhysicsOptimization_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsOptimization()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsOptimization.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGPhysicsOptimization.InnerSingleton, Z_Construct_UEnum_Aura_EPCGPhysicsOptimization_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsOptimization.InnerSingleton;
}
// ********** End Enum EPCGPhysicsOptimization *****************************************************

// ********** Begin Enum EPCGPhysicsEventType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGPhysicsEventType;
static UEnum* EPCGPhysicsEventType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsEventType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGPhysicsEventType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGPhysicsEventType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGPhysicsEventType"));
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsEventType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsEventType>()
{
	return EPCGPhysicsEventType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGPhysicsEventType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Break.DisplayName", "Break" },
		{ "Break.Name", "EPCGPhysicsEventType::Break" },
		{ "Collision.DisplayName", "Collision" },
		{ "Collision.Name", "EPCGPhysicsEventType::Collision" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics event types\n" },
#endif
		{ "Constraint.DisplayName", "Constraint" },
		{ "Constraint.Name", "EPCGPhysicsEventType::Constraint" },
		{ "Destruction.DisplayName", "Destruction" },
		{ "Destruction.Name", "EPCGPhysicsEventType::Destruction" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
		{ "Overlap.DisplayName", "Overlap" },
		{ "Overlap.Name", "EPCGPhysicsEventType::Overlap" },
		{ "Sleep.DisplayName", "Sleep" },
		{ "Sleep.Name", "EPCGPhysicsEventType::Sleep" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics event types" },
#endif
		{ "Wake.DisplayName", "Wake" },
		{ "Wake.Name", "EPCGPhysicsEventType::Wake" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGPhysicsEventType::Collision", (int64)EPCGPhysicsEventType::Collision },
		{ "EPCGPhysicsEventType::Overlap", (int64)EPCGPhysicsEventType::Overlap },
		{ "EPCGPhysicsEventType::Break", (int64)EPCGPhysicsEventType::Break },
		{ "EPCGPhysicsEventType::Sleep", (int64)EPCGPhysicsEventType::Sleep },
		{ "EPCGPhysicsEventType::Wake", (int64)EPCGPhysicsEventType::Wake },
		{ "EPCGPhysicsEventType::Constraint", (int64)EPCGPhysicsEventType::Constraint },
		{ "EPCGPhysicsEventType::Destruction", (int64)EPCGPhysicsEventType::Destruction },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGPhysicsEventType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGPhysicsEventType",
	"EPCGPhysicsEventType",
	Z_Construct_UEnum_Aura_EPCGPhysicsEventType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsEventType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGPhysicsEventType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGPhysicsEventType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGPhysicsEventType()
{
	if (!Z_Registration_Info_UEnum_EPCGPhysicsEventType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGPhysicsEventType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGPhysicsEventType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGPhysicsEventType.InnerSingleton;
}
// ********** End Enum EPCGPhysicsEventType ********************************************************

// ********** Begin ScriptStruct FPCGPhysicsConfig *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig;
class UScriptStruct* FPCGPhysicsConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGPhysicsConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGPhysicsConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics configuration structure\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics configuration structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationMode_MetaData[] = {
		{ "Category", "Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simulation settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simulation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityLevel_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeStep_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSubSteps_MetaData[] = {
		{ "Category", "Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 60 FPS\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "60 FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDeltaTime_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolverIterations_MetaData[] = {
		{ "Category", "Solver" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Solver settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Solver settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VelocityIterations_MetaData[] = {
		{ "Category", "Solver" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectionIterations_MetaData[] = {
		{ "Category", "Solver" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMargin_MetaData[] = {
		{ "Category", "Solver" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationStrategy_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Optimization settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncPhysics_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePhysicsLOD_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePhysicsCulling_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveRigidBodies_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveConstraints_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SleepThreshold_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinearDamping_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularDamping_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProceduralPhysics_MetaData[] = {
		{ "Category", "Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Procedural settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateCollision_MetaData[] = {
		{ "Category", "Procedural" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseComplexAsSimple_MetaData[] = {
		{ "Category", "Procedural" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionComplexity_MetaData[] = {
		{ "Category", "Procedural" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SimulationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SimulationMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeStep;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSubSteps;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDeltaTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SolverIterations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VelocityIterations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ProjectionIterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionMargin;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationStrategy;
	static void NewProp_bEnableAsyncPhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncPhysics;
	static void NewProp_bEnablePhysicsLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePhysicsLOD;
	static void NewProp_bEnablePhysicsCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePhysicsCulling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveRigidBodies;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveConstraints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SleepThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LinearDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngularDamping;
	static void NewProp_bEnableProceduralPhysics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProceduralPhysics;
	static void NewProp_bAutoGenerateCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateCollision;
	static void NewProp_bUseComplexAsSimple_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseComplexAsSimple;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionComplexity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGPhysicsConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SimulationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SimulationMode = { "SimulationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, SimulationMode), Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationMode_MetaData), NewProp_SimulationMode_MetaData) }; // 832010709
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_QualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, QualityLevel), Z_Construct_UEnum_Aura_EPCGPhysicsQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityLevel_MetaData), NewProp_QualityLevel_MetaData) }; // 3982702251
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_TimeStep = { "TimeStep", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, TimeStep), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeStep_MetaData), NewProp_TimeStep_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxSubSteps = { "MaxSubSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, MaxSubSteps), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSubSteps_MetaData), NewProp_MaxSubSteps_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxDeltaTime = { "MaxDeltaTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, MaxDeltaTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDeltaTime_MetaData), NewProp_MaxDeltaTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SolverIterations = { "SolverIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, SolverIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolverIterations_MetaData), NewProp_SolverIterations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_VelocityIterations = { "VelocityIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, VelocityIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VelocityIterations_MetaData), NewProp_VelocityIterations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_ProjectionIterations = { "ProjectionIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, ProjectionIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectionIterations_MetaData), NewProp_ProjectionIterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_CollisionMargin = { "CollisionMargin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, CollisionMargin), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMargin_MetaData), NewProp_CollisionMargin_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_OptimizationStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_OptimizationStrategy = { "OptimizationStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, OptimizationStrategy), Z_Construct_UEnum_Aura_EPCGPhysicsOptimization, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationStrategy_MetaData), NewProp_OptimizationStrategy_MetaData) }; // 1974953359
void Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableAsyncPhysics_SetBit(void* Obj)
{
	((FPCGPhysicsConfig*)Obj)->bEnableAsyncPhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableAsyncPhysics = { "bEnableAsyncPhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConfig), &Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableAsyncPhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncPhysics_MetaData), NewProp_bEnableAsyncPhysics_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsLOD_SetBit(void* Obj)
{
	((FPCGPhysicsConfig*)Obj)->bEnablePhysicsLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsLOD = { "bEnablePhysicsLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConfig), &Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePhysicsLOD_MetaData), NewProp_bEnablePhysicsLOD_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsCulling_SetBit(void* Obj)
{
	((FPCGPhysicsConfig*)Obj)->bEnablePhysicsCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsCulling = { "bEnablePhysicsCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConfig), &Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePhysicsCulling_MetaData), NewProp_bEnablePhysicsCulling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxActiveRigidBodies = { "MaxActiveRigidBodies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, MaxActiveRigidBodies), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveRigidBodies_MetaData), NewProp_MaxActiveRigidBodies_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxActiveConstraints = { "MaxActiveConstraints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, MaxActiveConstraints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveConstraints_MetaData), NewProp_MaxActiveConstraints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SleepThreshold = { "SleepThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, SleepThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SleepThreshold_MetaData), NewProp_SleepThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_LinearDamping = { "LinearDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, LinearDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinearDamping_MetaData), NewProp_LinearDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_AngularDamping = { "AngularDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, AngularDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularDamping_MetaData), NewProp_AngularDamping_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableProceduralPhysics_SetBit(void* Obj)
{
	((FPCGPhysicsConfig*)Obj)->bEnableProceduralPhysics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableProceduralPhysics = { "bEnableProceduralPhysics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConfig), &Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableProceduralPhysics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProceduralPhysics_MetaData), NewProp_bEnableProceduralPhysics_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bAutoGenerateCollision_SetBit(void* Obj)
{
	((FPCGPhysicsConfig*)Obj)->bAutoGenerateCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bAutoGenerateCollision = { "bAutoGenerateCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConfig), &Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bAutoGenerateCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateCollision_MetaData), NewProp_bAutoGenerateCollision_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bUseComplexAsSimple_SetBit(void* Obj)
{
	((FPCGPhysicsConfig*)Obj)->bUseComplexAsSimple = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bUseComplexAsSimple = { "bUseComplexAsSimple", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConfig), &Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bUseComplexAsSimple_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseComplexAsSimple_MetaData), NewProp_bUseComplexAsSimple_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_CollisionComplexity = { "CollisionComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConfig, CollisionComplexity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionComplexity_MetaData), NewProp_CollisionComplexity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SimulationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SimulationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_QualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_QualityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_TimeStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxSubSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxDeltaTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SolverIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_VelocityIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_ProjectionIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_CollisionMargin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_OptimizationStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_OptimizationStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableAsyncPhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnablePhysicsCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxActiveRigidBodies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_MaxActiveConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_SleepThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_LinearDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_AngularDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bEnableProceduralPhysics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bAutoGenerateCollision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_bUseComplexAsSimple,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewProp_CollisionComplexity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGPhysicsConfig",
	Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::PropPointers),
	sizeof(FPCGPhysicsConfig),
	alignof(FPCGPhysicsConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGPhysicsConfig ***************************************************

// ********** Begin ScriptStruct FPCGPhysicsBodyData ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData;
class UScriptStruct* FPCGPhysicsBodyData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGPhysicsBodyData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGPhysicsBodyData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics body data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics body data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "Category", "Body" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Body identification\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Body identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyType_MetaData[] = {
		{ "Category", "Body" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationMode_MetaData[] = {
		{ "Category", "Body" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mass_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physical properties\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physical properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Friction_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Restitution_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinearDamping_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularDamping_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dimensions_MetaData[] = {
		{ "Category", "Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Geometry data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Geometry data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transform data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transform data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinearVelocity_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularVelocity_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAwake_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsKinematic_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicalMaterial_MetaData[] = {
		{ "Category", "Material" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material reference\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevel_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BodyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BodyType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SimulationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SimulationMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Friction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Restitution;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LinearDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngularDamping;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Dimensions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LinearVelocity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AngularVelocity;
	static void NewProp_bIsAwake_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAwake;
	static void NewProp_bIsKinematic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsKinematic;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PhysicalMaterial;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGPhysicsBodyData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_BodyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_BodyType = { "BodyType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, BodyType), Z_Construct_UEnum_Aura_EPCGPhysicsBodyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyType_MetaData), NewProp_BodyType_MetaData) }; // 4074164504
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_SimulationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_SimulationMode = { "SimulationMode", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, SimulationMode), Z_Construct_UEnum_Aura_EPCGPhysicsSimulationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationMode_MetaData), NewProp_SimulationMode_MetaData) }; // 832010709
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Mass = { "Mass", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Mass), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mass_MetaData), NewProp_Mass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Friction = { "Friction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Friction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Friction_MetaData), NewProp_Friction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Restitution = { "Restitution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Restitution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Restitution_MetaData), NewProp_Restitution_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LinearDamping = { "LinearDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, LinearDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinearDamping_MetaData), NewProp_LinearDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_AngularDamping = { "AngularDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, AngularDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularDamping_MetaData), NewProp_AngularDamping_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Dimensions = { "Dimensions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Dimensions), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dimensions_MetaData), NewProp_Dimensions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LinearVelocity = { "LinearVelocity", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, LinearVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinearVelocity_MetaData), NewProp_LinearVelocity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_AngularVelocity = { "AngularVelocity", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, AngularVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularVelocity_MetaData), NewProp_AngularVelocity_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsAwake_SetBit(void* Obj)
{
	((FPCGPhysicsBodyData*)Obj)->bIsAwake = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsAwake = { "bIsAwake", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsBodyData), &Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsAwake_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAwake_MetaData), NewProp_bIsAwake_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsKinematic_SetBit(void* Obj)
{
	((FPCGPhysicsBodyData*)Obj)->bIsKinematic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsKinematic = { "bIsKinematic", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsBodyData), &Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsKinematic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsKinematic_MetaData), NewProp_bIsKinematic_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_PhysicalMaterial = { "PhysicalMaterial", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, PhysicalMaterial), Z_Construct_UClass_UPhysicalMaterial_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicalMaterial_MetaData), NewProp_PhysicalMaterial_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, LODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevel_MetaData), NewProp_LODLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LODDistance = { "LODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsBodyData, LODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance_MetaData), NewProp_LODDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_BodyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_BodyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_SimulationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_SimulationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Mass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Friction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Restitution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LinearDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_AngularDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Dimensions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LinearVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_AngularVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsAwake,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_bIsKinematic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_PhysicalMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewProp_LODDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGPhysicsBodyData",
	Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::PropPointers),
	sizeof(FPCGPhysicsBodyData),
	alignof(FPCGPhysicsBodyData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsBodyData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData.InnerSingleton, Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData.InnerSingleton;
}
// ********** End ScriptStruct FPCGPhysicsBodyData *************************************************

// ********** Begin ScriptStruct FPCGPhysicsConstraintData *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData;
class UScriptStruct* FPCGPhysicsConstraintData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGPhysicsConstraintData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGPhysicsConstraintData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics constraint data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics constraint data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintID_MetaData[] = {
		{ "Category", "Constraint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Constraint identification\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Constraint identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintType_MetaData[] = {
		{ "Category", "Constraint" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyA_ID_MetaData[] = {
		{ "Category", "Bodies" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Connected bodies\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Connected bodies" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyB_ID_MetaData[] = {
		{ "Category", "Bodies" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameA_MetaData[] = {
		{ "Category", "Frames" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Constraint frames\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Constraint frames" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameB_MetaData[] = {
		{ "Category", "Frames" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLinearXLocked_MetaData[] = {
		{ "Category", "Linear" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Linear limits\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Linear limits" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLinearYLocked_MetaData[] = {
		{ "Category", "Linear" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLinearZLocked_MetaData[] = {
		{ "Category", "Linear" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LinearLimit_MetaData[] = {
		{ "Category", "Linear" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAngularSwing1Locked_MetaData[] = {
		{ "Category", "Angular" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Angular limits\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Angular limits" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAngularSwing2Locked_MetaData[] = {
		{ "Category", "Angular" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAngularTwistLocked_MetaData[] = {
		{ "Category", "Angular" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularLimit_MetaData[] = {
		{ "Category", "Angular" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpringStiffness_MetaData[] = {
		{ "Category", "Spring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spring settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spring settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpringDamping_MetaData[] = {
		{ "Category", "Spring" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpringRestLength_MetaData[] = {
		{ "Category", "Spring" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBreaking_MetaData[] = {
		{ "Category", "Break" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Break settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Break settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BreakForce_MetaData[] = {
		{ "Category", "Break" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BreakTorque_MetaData[] = {
		{ "Category", "Break" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConstraintID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ConstraintType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ConstraintType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyA_ID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyB_ID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FrameA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FrameB;
	static void NewProp_bLinearXLocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLinearXLocked;
	static void NewProp_bLinearYLocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLinearYLocked;
	static void NewProp_bLinearZLocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLinearZLocked;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LinearLimit;
	static void NewProp_bAngularSwing1Locked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAngularSwing1Locked;
	static void NewProp_bAngularSwing2Locked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAngularSwing2Locked;
	static void NewProp_bAngularTwistLocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAngularTwistLocked;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngularLimit;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpringStiffness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpringDamping;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpringRestLength;
	static void NewProp_bEnableBreaking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBreaking;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BreakForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BreakTorque;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGPhysicsConstraintData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_ConstraintID = { "ConstraintID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, ConstraintID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintID_MetaData), NewProp_ConstraintID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_ConstraintType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_ConstraintType = { "ConstraintType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, ConstraintType), Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintType_MetaData), NewProp_ConstraintType_MetaData) }; // 2789340363
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BodyA_ID = { "BodyA_ID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, BodyA_ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyA_ID_MetaData), NewProp_BodyA_ID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BodyB_ID = { "BodyB_ID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, BodyB_ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyB_ID_MetaData), NewProp_BodyB_ID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_FrameA = { "FrameA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, FrameA), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameA_MetaData), NewProp_FrameA_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_FrameB = { "FrameB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, FrameB), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameB_MetaData), NewProp_FrameB_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearXLocked_SetBit(void* Obj)
{
	((FPCGPhysicsConstraintData*)Obj)->bLinearXLocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearXLocked = { "bLinearXLocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConstraintData), &Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearXLocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLinearXLocked_MetaData), NewProp_bLinearXLocked_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearYLocked_SetBit(void* Obj)
{
	((FPCGPhysicsConstraintData*)Obj)->bLinearYLocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearYLocked = { "bLinearYLocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConstraintData), &Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearYLocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLinearYLocked_MetaData), NewProp_bLinearYLocked_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearZLocked_SetBit(void* Obj)
{
	((FPCGPhysicsConstraintData*)Obj)->bLinearZLocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearZLocked = { "bLinearZLocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConstraintData), &Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearZLocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLinearZLocked_MetaData), NewProp_bLinearZLocked_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_LinearLimit = { "LinearLimit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, LinearLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LinearLimit_MetaData), NewProp_LinearLimit_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing1Locked_SetBit(void* Obj)
{
	((FPCGPhysicsConstraintData*)Obj)->bAngularSwing1Locked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing1Locked = { "bAngularSwing1Locked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConstraintData), &Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing1Locked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAngularSwing1Locked_MetaData), NewProp_bAngularSwing1Locked_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing2Locked_SetBit(void* Obj)
{
	((FPCGPhysicsConstraintData*)Obj)->bAngularSwing2Locked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing2Locked = { "bAngularSwing2Locked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConstraintData), &Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing2Locked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAngularSwing2Locked_MetaData), NewProp_bAngularSwing2Locked_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularTwistLocked_SetBit(void* Obj)
{
	((FPCGPhysicsConstraintData*)Obj)->bAngularTwistLocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularTwistLocked = { "bAngularTwistLocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConstraintData), &Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularTwistLocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAngularTwistLocked_MetaData), NewProp_bAngularTwistLocked_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_AngularLimit = { "AngularLimit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, AngularLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularLimit_MetaData), NewProp_AngularLimit_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_SpringStiffness = { "SpringStiffness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, SpringStiffness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpringStiffness_MetaData), NewProp_SpringStiffness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_SpringDamping = { "SpringDamping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, SpringDamping), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpringDamping_MetaData), NewProp_SpringDamping_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_SpringRestLength = { "SpringRestLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, SpringRestLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpringRestLength_MetaData), NewProp_SpringRestLength_MetaData) };
void Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bEnableBreaking_SetBit(void* Obj)
{
	((FPCGPhysicsConstraintData*)Obj)->bEnableBreaking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bEnableBreaking = { "bEnableBreaking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGPhysicsConstraintData), &Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bEnableBreaking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBreaking_MetaData), NewProp_bEnableBreaking_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BreakForce = { "BreakForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, BreakForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BreakForce_MetaData), NewProp_BreakForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BreakTorque = { "BreakTorque", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsConstraintData, BreakTorque), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BreakTorque_MetaData), NewProp_BreakTorque_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_ConstraintID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_ConstraintType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_ConstraintType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BodyA_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BodyB_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_FrameA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_FrameB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearXLocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearYLocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bLinearZLocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_LinearLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing1Locked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularSwing2Locked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bAngularTwistLocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_AngularLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_SpringStiffness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_SpringDamping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_SpringRestLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_bEnableBreaking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BreakForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewProp_BreakTorque,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGPhysicsConstraintData",
	Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::PropPointers),
	sizeof(FPCGPhysicsConstraintData),
	alignof(FPCGPhysicsConstraintData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsConstraintData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData.InnerSingleton, Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData.InnerSingleton;
}
// ********** End ScriptStruct FPCGPhysicsConstraintData *******************************************

// ********** Begin ScriptStruct FPCGPhysicsPerformanceStats ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats;
class UScriptStruct* FPCGPhysicsPerformanceStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGPhysicsPerformanceStats"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics performance statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics performance statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SimulationTimeMs_MetaData[] = {
		{ "Category", "Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simulation statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simulation statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameTime_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveRigidBodies_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SleepingRigidBodies_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveConstraints_MetaData[] = {
		{ "Category", "Simulation" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionPairs_MetaData[] = {
		{ "Category", "Collision" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContactPoints_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDetectionTime_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolverTime_MetaData[] = {
		{ "Category", "Solver" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Solver statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Solver statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolverIterationsUsed_MetaData[] = {
		{ "Category", "Solver" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintSolverTime_MetaData[] = {
		{ "Category", "Solver" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsUtilization_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActualFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SimulationTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveRigidBodies;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SleepingRigidBodies;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveConstraints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CollisionPairs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ContactPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionDetectionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SolverTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SolverIterationsUsed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConstraintSolverTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PhysicsMemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CollisionMemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ConstraintMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhysicsUtilization;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActualFrameRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGPhysicsPerformanceStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SimulationTimeMs = { "SimulationTimeMs", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, SimulationTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SimulationTimeMs_MetaData), NewProp_SimulationTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_AverageFrameTime = { "AverageFrameTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, AverageFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameTime_MetaData), NewProp_AverageFrameTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ActiveRigidBodies = { "ActiveRigidBodies", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, ActiveRigidBodies), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveRigidBodies_MetaData), NewProp_ActiveRigidBodies_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SleepingRigidBodies = { "SleepingRigidBodies", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, SleepingRigidBodies), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SleepingRigidBodies_MetaData), NewProp_SleepingRigidBodies_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ActiveConstraints = { "ActiveConstraints", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, ActiveConstraints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveConstraints_MetaData), NewProp_ActiveConstraints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_CollisionPairs = { "CollisionPairs", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, CollisionPairs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionPairs_MetaData), NewProp_CollisionPairs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ContactPoints = { "ContactPoints", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, ContactPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContactPoints_MetaData), NewProp_ContactPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_CollisionDetectionTime = { "CollisionDetectionTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, CollisionDetectionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDetectionTime_MetaData), NewProp_CollisionDetectionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SolverTime = { "SolverTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, SolverTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolverTime_MetaData), NewProp_SolverTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SolverIterationsUsed = { "SolverIterationsUsed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, SolverIterationsUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolverIterationsUsed_MetaData), NewProp_SolverIterationsUsed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ConstraintSolverTime = { "ConstraintSolverTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, ConstraintSolverTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintSolverTime_MetaData), NewProp_ConstraintSolverTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_PhysicsMemoryUsageMB = { "PhysicsMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, PhysicsMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsMemoryUsageMB_MetaData), NewProp_PhysicsMemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_CollisionMemoryUsageMB = { "CollisionMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, CollisionMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionMemoryUsageMB_MetaData), NewProp_CollisionMemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ConstraintMemoryUsageMB = { "ConstraintMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, ConstraintMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintMemoryUsageMB_MetaData), NewProp_ConstraintMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_PhysicsUtilization = { "PhysicsUtilization", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, PhysicsUtilization), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsUtilization_MetaData), NewProp_PhysicsUtilization_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_TargetFrameRate = { "TargetFrameRate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, TargetFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetFrameRate_MetaData), NewProp_TargetFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ActualFrameRate = { "ActualFrameRate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsPerformanceStats, ActualFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActualFrameRate_MetaData), NewProp_ActualFrameRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SimulationTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_AverageFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ActiveRigidBodies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SleepingRigidBodies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ActiveConstraints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_CollisionPairs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ContactPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_CollisionDetectionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SolverTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_SolverIterationsUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ConstraintSolverTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_PhysicsMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_CollisionMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ConstraintMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_PhysicsUtilization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_TargetFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewProp_ActualFrameRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGPhysicsPerformanceStats",
	Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::PropPointers),
	sizeof(FPCGPhysicsPerformanceStats),
	alignof(FPCGPhysicsPerformanceStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats.InnerSingleton, Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats.InnerSingleton;
}
// ********** End ScriptStruct FPCGPhysicsPerformanceStats *****************************************

// ********** Begin ScriptStruct FPCGPhysicsEventData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData;
class UScriptStruct* FPCGPhysicsEventData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGPhysicsEventData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGPhysicsEventData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics event data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics event data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyA_ID_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyB_ID_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContactPoint_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContactNormal_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpulseStrength_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTime_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EventType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyA_ID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyB_ID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ContactPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ContactNormal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ImpulseStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EventTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGPhysicsEventData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_EventType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsEventData, EventType), Z_Construct_UEnum_Aura_EPCGPhysicsEventType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) }; // 3334143049
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_BodyA_ID = { "BodyA_ID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsEventData, BodyA_ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyA_ID_MetaData), NewProp_BodyA_ID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_BodyB_ID = { "BodyB_ID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsEventData, BodyB_ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyB_ID_MetaData), NewProp_BodyB_ID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_ContactPoint = { "ContactPoint", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsEventData, ContactPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContactPoint_MetaData), NewProp_ContactPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_ContactNormal = { "ContactNormal", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsEventData, ContactNormal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContactNormal_MetaData), NewProp_ContactNormal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_ImpulseStrength = { "ImpulseStrength", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsEventData, ImpulseStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpulseStrength_MetaData), NewProp_ImpulseStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_EventTime = { "EventTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPhysicsEventData, EventTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTime_MetaData), NewProp_EventTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_EventType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_BodyA_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_BodyB_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_ContactPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_ContactNormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_ImpulseStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewProp_EventTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGPhysicsEventData",
	Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::PropPointers),
	sizeof(FPCGPhysicsEventData),
	alignof(FPCGPhysicsEventData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGPhysicsEventData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData.InnerSingleton, Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData.InnerSingleton;
}
// ********** End ScriptStruct FPCGPhysicsEventData ************************************************

// ********** Begin Delegate FOnPhysicsBodyCreated *************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPhysicsBodyCreated_Parms
	{
		FPCGPhysicsBodyData BodyData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate declarations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate declarations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BodyData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::NewProp_BodyData = { "BodyData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhysicsBodyCreated_Parms, BodyData), Z_Construct_UScriptStruct_FPCGPhysicsBodyData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyData_MetaData), NewProp_BodyData_MetaData) }; // 2034192839
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::NewProp_BodyData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPhysicsBodyCreated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsBodyCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsBodyCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhysicsBodyCreated_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsBodyCreated, FPCGPhysicsBodyData const& BodyData)
{
	struct _Script_Aura_eventOnPhysicsBodyCreated_Parms
	{
		FPCGPhysicsBodyData BodyData;
	};
	_Script_Aura_eventOnPhysicsBodyCreated_Parms Parms;
	Parms.BodyData=BodyData;
	OnPhysicsBodyCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhysicsBodyCreated ***************************************************

// ********** Begin Delegate FOnPhysicsBodyDestroyed ***********************************************
struct Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPhysicsBodyDestroyed_Parms
	{
		FString BodyID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhysicsBodyDestroyed_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::NewProp_BodyID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPhysicsBodyDestroyed__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsBodyDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsBodyDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhysicsBodyDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsBodyDestroyed, const FString& BodyID)
{
	struct _Script_Aura_eventOnPhysicsBodyDestroyed_Parms
	{
		FString BodyID;
	};
	_Script_Aura_eventOnPhysicsBodyDestroyed_Parms Parms;
	Parms.BodyID=BodyID;
	OnPhysicsBodyDestroyed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhysicsBodyDestroyed *************************************************

// ********** Begin Delegate FOnPhysicsConstraintCreated *******************************************
struct Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPhysicsConstraintCreated_Parms
	{
		FPCGPhysicsConstraintData ConstraintData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConstraintData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::NewProp_ConstraintData = { "ConstraintData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhysicsConstraintCreated_Parms, ConstraintData), Z_Construct_UScriptStruct_FPCGPhysicsConstraintData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintData_MetaData), NewProp_ConstraintData_MetaData) }; // 3852657120
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::NewProp_ConstraintData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPhysicsConstraintCreated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsConstraintCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsConstraintCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhysicsConstraintCreated_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsConstraintCreated, FPCGPhysicsConstraintData const& ConstraintData)
{
	struct _Script_Aura_eventOnPhysicsConstraintCreated_Parms
	{
		FPCGPhysicsConstraintData ConstraintData;
	};
	_Script_Aura_eventOnPhysicsConstraintCreated_Parms Parms;
	Parms.ConstraintData=ConstraintData;
	OnPhysicsConstraintCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhysicsConstraintCreated *********************************************

// ********** Begin Delegate FOnPhysicsConstraintBroken ********************************************
struct Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPhysicsConstraintBroken_Parms
	{
		FString ConstraintID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConstraintID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::NewProp_ConstraintID = { "ConstraintID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhysicsConstraintBroken_Parms, ConstraintID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintID_MetaData), NewProp_ConstraintID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::NewProp_ConstraintID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPhysicsConstraintBroken__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsConstraintBroken_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsConstraintBroken_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhysicsConstraintBroken_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsConstraintBroken, const FString& ConstraintID)
{
	struct _Script_Aura_eventOnPhysicsConstraintBroken_Parms
	{
		FString ConstraintID;
	};
	_Script_Aura_eventOnPhysicsConstraintBroken_Parms Parms;
	Parms.ConstraintID=ConstraintID;
	OnPhysicsConstraintBroken.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhysicsConstraintBroken **********************************************

// ********** Begin Delegate FOnPhysicsEvent *******************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPhysicsEvent_Parms
	{
		FPCGPhysicsEventData EventData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::NewProp_EventData = { "EventData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhysicsEvent_Parms, EventData), Z_Construct_UScriptStruct_FPCGPhysicsEventData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventData_MetaData), NewProp_EventData_MetaData) }; // 1932252492
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::NewProp_EventData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPhysicsEvent__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhysicsEvent_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsEvent, FPCGPhysicsEventData const& EventData)
{
	struct _Script_Aura_eventOnPhysicsEvent_Parms
	{
		FPCGPhysicsEventData EventData;
	};
	_Script_Aura_eventOnPhysicsEvent_Parms Parms;
	Parms.EventData=EventData;
	OnPhysicsEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhysicsEvent *********************************************************

// ********** Begin Delegate FOnPhysicsPerformanceUpdated ******************************************
struct Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPhysicsPerformanceUpdated_Parms
	{
		FPCGPhysicsPerformanceStats Stats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Stats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::NewProp_Stats = { "Stats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhysicsPerformanceUpdated_Parms, Stats), Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Stats_MetaData), NewProp_Stats_MetaData) }; // 3406102424
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::NewProp_Stats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPhysicsPerformanceUpdated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsPerformanceUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::_Script_Aura_eventOnPhysicsPerformanceUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhysicsPerformanceUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsPerformanceUpdated, FPCGPhysicsPerformanceStats const& Stats)
{
	struct _Script_Aura_eventOnPhysicsPerformanceUpdated_Parms
	{
		FPCGPhysicsPerformanceStats Stats;
	};
	_Script_Aura_eventOnPhysicsPerformanceUpdated_Parms Parms;
	Parms.Stats=Stats;
	OnPhysicsPerformanceUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhysicsPerformanceUpdated ********************************************

// ********** Begin Class APCGChaosIntegrator Function AddAngularImpulseToBody *********************
struct Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics
{
	struct PCGChaosIntegrator_eventAddAngularImpulseToBody_Parms
	{
		FString BodyID;
		FVector AngularImpulse;
		bool bVelChange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Forces" },
		{ "CPP_Default_bVelChange", "false" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularImpulse_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AngularImpulse;
	static void NewProp_bVelChange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVelChange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddAngularImpulseToBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_AngularImpulse = { "AngularImpulse", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddAngularImpulseToBody_Parms, AngularImpulse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularImpulse_MetaData), NewProp_AngularImpulse_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_bVelChange_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventAddAngularImpulseToBody_Parms*)Obj)->bVelChange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_bVelChange = { "bVelChange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventAddAngularImpulseToBody_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_bVelChange_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_AngularImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::NewProp_bVelChange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "AddAngularImpulseToBody", Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::PCGChaosIntegrator_eventAddAngularImpulseToBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::PCGChaosIntegrator_eventAddAngularImpulseToBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execAddAngularImpulseToBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_AngularImpulse);
	P_GET_UBOOL(Z_Param_bVelChange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddAngularImpulseToBody(Z_Param_BodyID,Z_Param_Out_AngularImpulse,Z_Param_bVelChange);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function AddAngularImpulseToBody ***********************

// ********** Begin Class APCGChaosIntegrator Function AddForceAtLocation **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics
{
	struct PCGChaosIntegrator_eventAddForceAtLocation_Parms
	{
		FString BodyID;
		FVector Force;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Forces" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Force_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Force;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddForceAtLocation_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddForceAtLocation_Parms, Force), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Force_MetaData), NewProp_Force_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddForceAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "AddForceAtLocation", Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::PCGChaosIntegrator_eventAddForceAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::PCGChaosIntegrator_eventAddForceAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execAddForceAtLocation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Force);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddForceAtLocation(Z_Param_BodyID,Z_Param_Out_Force,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function AddForceAtLocation ****************************

// ********** Begin Class APCGChaosIntegrator Function AddForceToBody ******************************
struct Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics
{
	struct PCGChaosIntegrator_eventAddForceToBody_Parms
	{
		FString BodyID;
		FVector Force;
		bool bAccelChange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Forces" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics Body Forces\n" },
#endif
		{ "CPP_Default_bAccelChange", "false" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Body Forces" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Force_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Force;
	static void NewProp_bAccelChange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAccelChange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddForceToBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddForceToBody_Parms, Force), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Force_MetaData), NewProp_Force_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_bAccelChange_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventAddForceToBody_Parms*)Obj)->bAccelChange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_bAccelChange = { "bAccelChange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventAddForceToBody_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_bAccelChange_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::NewProp_bAccelChange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "AddForceToBody", Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::PCGChaosIntegrator_eventAddForceToBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::PCGChaosIntegrator_eventAddForceToBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execAddForceToBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Force);
	P_GET_UBOOL(Z_Param_bAccelChange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddForceToBody(Z_Param_BodyID,Z_Param_Out_Force,Z_Param_bAccelChange);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function AddForceToBody ********************************

// ********** Begin Class APCGChaosIntegrator Function AddImpulseAtLocation ************************
struct Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics
{
	struct PCGChaosIntegrator_eventAddImpulseAtLocation_Parms
	{
		FString BodyID;
		FVector Impulse;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Forces" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Impulse_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Impulse;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddImpulseAtLocation_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::NewProp_Impulse = { "Impulse", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddImpulseAtLocation_Parms, Impulse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Impulse_MetaData), NewProp_Impulse_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddImpulseAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::NewProp_Impulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "AddImpulseAtLocation", Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::PCGChaosIntegrator_eventAddImpulseAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::PCGChaosIntegrator_eventAddImpulseAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execAddImpulseAtLocation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Impulse);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddImpulseAtLocation(Z_Param_BodyID,Z_Param_Out_Impulse,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function AddImpulseAtLocation **************************

// ********** Begin Class APCGChaosIntegrator Function AddImpulseToBody ****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics
{
	struct PCGChaosIntegrator_eventAddImpulseToBody_Parms
	{
		FString BodyID;
		FVector Impulse;
		bool bVelChange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Forces" },
		{ "CPP_Default_bVelChange", "false" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Impulse_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Impulse;
	static void NewProp_bVelChange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVelChange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddImpulseToBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_Impulse = { "Impulse", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddImpulseToBody_Parms, Impulse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Impulse_MetaData), NewProp_Impulse_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_bVelChange_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventAddImpulseToBody_Parms*)Obj)->bVelChange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_bVelChange = { "bVelChange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventAddImpulseToBody_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_bVelChange_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_Impulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::NewProp_bVelChange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "AddImpulseToBody", Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::PCGChaosIntegrator_eventAddImpulseToBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::PCGChaosIntegrator_eventAddImpulseToBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execAddImpulseToBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Impulse);
	P_GET_UBOOL(Z_Param_bVelChange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddImpulseToBody(Z_Param_BodyID,Z_Param_Out_Impulse,Z_Param_bVelChange);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function AddImpulseToBody ******************************

// ********** Begin Class APCGChaosIntegrator Function AddTorqueToBody *****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics
{
	struct PCGChaosIntegrator_eventAddTorqueToBody_Parms
	{
		FString BodyID;
		FVector Torque;
		bool bAccelChange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Forces" },
		{ "CPP_Default_bAccelChange", "false" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Torque_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Torque;
	static void NewProp_bAccelChange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAccelChange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddTorqueToBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_Torque = { "Torque", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAddTorqueToBody_Parms, Torque), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Torque_MetaData), NewProp_Torque_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_bAccelChange_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventAddTorqueToBody_Parms*)Obj)->bAccelChange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_bAccelChange = { "bAccelChange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventAddTorqueToBody_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_bAccelChange_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_Torque,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::NewProp_bAccelChange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "AddTorqueToBody", Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::PCGChaosIntegrator_eventAddTorqueToBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::PCGChaosIntegrator_eventAddTorqueToBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execAddTorqueToBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Torque);
	P_GET_UBOOL(Z_Param_bAccelChange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddTorqueToBody(Z_Param_BodyID,Z_Param_Out_Torque,Z_Param_bAccelChange);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function AddTorqueToBody *******************************

// ********** Begin Class APCGChaosIntegrator Function AreOverlapping ******************************
struct Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics
{
	struct PCGChaosIntegrator_eventAreOverlapping_Parms
	{
		FString BodyA_ID;
		FString BodyB_ID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyA_ID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyB_ID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyA_ID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyB_ID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_BodyA_ID = { "BodyA_ID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAreOverlapping_Parms, BodyA_ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyA_ID_MetaData), NewProp_BodyA_ID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_BodyB_ID = { "BodyB_ID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventAreOverlapping_Parms, BodyB_ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyB_ID_MetaData), NewProp_BodyB_ID_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventAreOverlapping_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventAreOverlapping_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_BodyA_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_BodyB_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "AreOverlapping", Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::PCGChaosIntegrator_eventAreOverlapping_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::PCGChaosIntegrator_eventAreOverlapping_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execAreOverlapping)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyA_ID);
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyB_ID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AreOverlapping(Z_Param_BodyA_ID,Z_Param_BodyB_ID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function AreOverlapping ********************************

// ********** Begin Class APCGChaosIntegrator Function BoxTrace ************************************
struct Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics
{
	struct PCGChaosIntegrator_eventBoxTrace_Parms
	{
		FVector Start;
		FVector End;
		FVector HalfExtents;
		TArray<FString> HitBodyIDs;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HalfExtents_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HalfExtents;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HitBodyIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HitBodyIDs;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventBoxTrace_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventBoxTrace_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_HalfExtents = { "HalfExtents", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventBoxTrace_Parms, HalfExtents), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HalfExtents_MetaData), NewProp_HalfExtents_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_HitBodyIDs_Inner = { "HitBodyIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_HitBodyIDs = { "HitBodyIDs", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventBoxTrace_Parms, HitBodyIDs), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventBoxTrace_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventBoxTrace_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_HalfExtents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_HitBodyIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_HitBodyIDs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "BoxTrace", Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::PCGChaosIntegrator_eventBoxTrace_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::PCGChaosIntegrator_eventBoxTrace_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execBoxTrace)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_HalfExtents);
	P_GET_TARRAY_REF(FString,Z_Param_Out_HitBodyIDs);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BoxTrace(Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Out_HalfExtents,Z_Param_Out_HitBodyIDs);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function BoxTrace **************************************

// ********** Begin Class APCGChaosIntegrator Function CreatePhysicsBody ***************************
struct Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics
{
	struct PCGChaosIntegrator_eventCreatePhysicsBody_Parms
	{
		FPCGPhysicsBodyData BodyData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Bodies" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics Body Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Body Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BodyData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::NewProp_BodyData = { "BodyData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventCreatePhysicsBody_Parms, BodyData), Z_Construct_UScriptStruct_FPCGPhysicsBodyData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyData_MetaData), NewProp_BodyData_MetaData) }; // 2034192839
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventCreatePhysicsBody_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::NewProp_BodyData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "CreatePhysicsBody", Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::PCGChaosIntegrator_eventCreatePhysicsBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::PCGChaosIntegrator_eventCreatePhysicsBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execCreatePhysicsBody)
{
	P_GET_STRUCT_REF(FPCGPhysicsBodyData,Z_Param_Out_BodyData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreatePhysicsBody(Z_Param_Out_BodyData);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function CreatePhysicsBody *****************************

// ********** Begin Class APCGChaosIntegrator Function CreatePhysicsConstraint *********************
struct Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics
{
	struct PCGChaosIntegrator_eventCreatePhysicsConstraint_Parms
	{
		FPCGPhysicsConstraintData ConstraintData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Constraints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Constraint Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Constraint Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConstraintData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::NewProp_ConstraintData = { "ConstraintData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventCreatePhysicsConstraint_Parms, ConstraintData), Z_Construct_UScriptStruct_FPCGPhysicsConstraintData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintData_MetaData), NewProp_ConstraintData_MetaData) }; // 3852657120
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventCreatePhysicsConstraint_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::NewProp_ConstraintData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "CreatePhysicsConstraint", Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::PCGChaosIntegrator_eventCreatePhysicsConstraint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::PCGChaosIntegrator_eventCreatePhysicsConstraint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execCreatePhysicsConstraint)
{
	P_GET_STRUCT_REF(FPCGPhysicsConstraintData,Z_Param_Out_ConstraintData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreatePhysicsConstraint(Z_Param_Out_ConstraintData);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function CreatePhysicsConstraint ***********************

// ********** Begin Class APCGChaosIntegrator Function CreatePhysicsIsland *************************
struct Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics
{
	struct PCGChaosIntegrator_eventCreatePhysicsIsland_Parms
	{
		TArray<FString> BodyIDs;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyIDs_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BodyIDs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::NewProp_BodyIDs_Inner = { "BodyIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::NewProp_BodyIDs = { "BodyIDs", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventCreatePhysicsIsland_Parms, BodyIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyIDs_MetaData), NewProp_BodyIDs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::NewProp_BodyIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::NewProp_BodyIDs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "CreatePhysicsIsland", Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::PCGChaosIntegrator_eventCreatePhysicsIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::PCGChaosIntegrator_eventCreatePhysicsIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execCreatePhysicsIsland)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_BodyIDs);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreatePhysicsIsland(Z_Param_Out_BodyIDs);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function CreatePhysicsIsland ***************************

// ********** Begin Class APCGChaosIntegrator Function DestroyPhysicsBody **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics
{
	struct PCGChaosIntegrator_eventDestroyPhysicsBody_Parms
	{
		FString BodyID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Bodies" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventDestroyPhysicsBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventDestroyPhysicsBody_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventDestroyPhysicsBody_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "DestroyPhysicsBody", Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::PCGChaosIntegrator_eventDestroyPhysicsBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::PCGChaosIntegrator_eventDestroyPhysicsBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execDestroyPhysicsBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DestroyPhysicsBody(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function DestroyPhysicsBody ****************************

// ********** Begin Class APCGChaosIntegrator Function DestroyPhysicsConstraint ********************
struct Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics
{
	struct PCGChaosIntegrator_eventDestroyPhysicsConstraint_Parms
	{
		FString ConstraintID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Constraints" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConstraintID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::NewProp_ConstraintID = { "ConstraintID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventDestroyPhysicsConstraint_Parms, ConstraintID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintID_MetaData), NewProp_ConstraintID_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventDestroyPhysicsConstraint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventDestroyPhysicsConstraint_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::NewProp_ConstraintID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "DestroyPhysicsConstraint", Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::PCGChaosIntegrator_eventDestroyPhysicsConstraint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::PCGChaosIntegrator_eventDestroyPhysicsConstraint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execDestroyPhysicsConstraint)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ConstraintID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DestroyPhysicsConstraint(Z_Param_ConstraintID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function DestroyPhysicsConstraint **********************

// ********** Begin Class APCGChaosIntegrator Function DestroyPhysicsIsland ************************
struct Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics
{
	struct PCGChaosIntegrator_eventDestroyPhysicsIsland_Parms
	{
		TArray<FString> BodyIDs;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyIDs_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BodyIDs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::NewProp_BodyIDs_Inner = { "BodyIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::NewProp_BodyIDs = { "BodyIDs", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventDestroyPhysicsIsland_Parms, BodyIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyIDs_MetaData), NewProp_BodyIDs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::NewProp_BodyIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::NewProp_BodyIDs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "DestroyPhysicsIsland", Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::PCGChaosIntegrator_eventDestroyPhysicsIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::PCGChaosIntegrator_eventDestroyPhysicsIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execDestroyPhysicsIsland)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_BodyIDs);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DestroyPhysicsIsland(Z_Param_Out_BodyIDs);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function DestroyPhysicsIsland **************************

// ********** Begin Class APCGChaosIntegrator Function EnableAsyncPhysics **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics
{
	struct PCGChaosIntegrator_eventEnableAsyncPhysics_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced Features\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced Features" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventEnableAsyncPhysics_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventEnableAsyncPhysics_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "EnableAsyncPhysics", Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::PCGChaosIntegrator_eventEnableAsyncPhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::PCGChaosIntegrator_eventEnableAsyncPhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execEnableAsyncPhysics)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableAsyncPhysics(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function EnableAsyncPhysics ****************************

// ********** Begin Class APCGChaosIntegrator Function EnablePhysics *******************************
struct Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics
{
	struct PCGChaosIntegrator_eventEnablePhysics_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Management" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventEnablePhysics_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventEnablePhysics_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "EnablePhysics", Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::PCGChaosIntegrator_eventEnablePhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::PCGChaosIntegrator_eventEnablePhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execEnablePhysics)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnablePhysics(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function EnablePhysics *********************************

// ********** Begin Class APCGChaosIntegrator Function EnablePhysicsCulling ************************
struct Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics
{
	struct PCGChaosIntegrator_eventEnablePhysicsCulling_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventEnablePhysicsCulling_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventEnablePhysicsCulling_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "EnablePhysicsCulling", Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::PCGChaosIntegrator_eventEnablePhysicsCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::PCGChaosIntegrator_eventEnablePhysicsCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execEnablePhysicsCulling)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnablePhysicsCulling(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function EnablePhysicsCulling **************************

// ********** Begin Class APCGChaosIntegrator Function GeneratePhysicsBodiesFromMesh ***************
struct Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics
{
	struct PCGChaosIntegrator_eventGeneratePhysicsBodiesFromMesh_Parms
	{
		UStaticMesh* Mesh;
		FTransform Transform;
		int32 Count;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Procedural Generation\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural Generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transform;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodiesFromMesh_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_Transform = { "Transform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodiesFromMesh_Parms, Transform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transform_MetaData), NewProp_Transform_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodiesFromMesh_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodiesFromMesh_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_Transform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_Count,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GeneratePhysicsBodiesFromMesh", Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::PCGChaosIntegrator_eventGeneratePhysicsBodiesFromMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::PCGChaosIntegrator_eventGeneratePhysicsBodiesFromMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGeneratePhysicsBodiesFromMesh)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_Transform);
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GeneratePhysicsBodiesFromMesh(Z_Param_Mesh,Z_Param_Out_Transform,Z_Param_Count);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GeneratePhysicsBodiesFromMesh *****************

// ********** Begin Class APCGChaosIntegrator Function GeneratePhysicsBodyFromGeometry *************
struct Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics
{
	struct PCGChaosIntegrator_eventGeneratePhysicsBodyFromGeometry_Parms
	{
		EPCGPhysicsBodyType BodyType;
		FVector Dimensions;
		FTransform Transform;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dimensions_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BodyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BodyType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Dimensions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transform;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_BodyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_BodyType = { "BodyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodyFromGeometry_Parms, BodyType), Z_Construct_UEnum_Aura_EPCGPhysicsBodyType, METADATA_PARAMS(0, nullptr) }; // 4074164504
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_Dimensions = { "Dimensions", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodyFromGeometry_Parms, Dimensions), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dimensions_MetaData), NewProp_Dimensions_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_Transform = { "Transform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodyFromGeometry_Parms, Transform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transform_MetaData), NewProp_Transform_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsBodyFromGeometry_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_BodyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_BodyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_Dimensions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_Transform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GeneratePhysicsBodyFromGeometry", Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::PCGChaosIntegrator_eventGeneratePhysicsBodyFromGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::PCGChaosIntegrator_eventGeneratePhysicsBodyFromGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGeneratePhysicsBodyFromGeometry)
{
	P_GET_ENUM(EPCGPhysicsBodyType,Z_Param_BodyType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Dimensions);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_Transform);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GeneratePhysicsBodyFromGeometry(EPCGPhysicsBodyType(Z_Param_BodyType),Z_Param_Out_Dimensions,Z_Param_Out_Transform);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GeneratePhysicsBodyFromGeometry ***************

// ********** Begin Class APCGChaosIntegrator Function GeneratePhysicsChain ************************
struct Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics
{
	struct PCGChaosIntegrator_eventGeneratePhysicsChain_Parms
	{
		TArray<FVector> Points;
		EPCGPhysicsConstraintType ConstraintType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ConstraintType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ConstraintType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsChain_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ConstraintType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ConstraintType = { "ConstraintType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsChain_Parms, ConstraintType), Z_Construct_UEnum_Aura_EPCGPhysicsConstraintType, METADATA_PARAMS(0, nullptr) }; // 2789340363
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsChain_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ConstraintType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ConstraintType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GeneratePhysicsChain", Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::PCGChaosIntegrator_eventGeneratePhysicsChain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::PCGChaosIntegrator_eventGeneratePhysicsChain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGeneratePhysicsChain)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_GET_ENUM(EPCGPhysicsConstraintType,Z_Param_ConstraintType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GeneratePhysicsChain(Z_Param_Out_Points,EPCGPhysicsConstraintType(Z_Param_ConstraintType));
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GeneratePhysicsChain **************************

// ********** Begin Class APCGChaosIntegrator Function GeneratePhysicsCluster **********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics
{
	struct PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms
	{
		FVector Center;
		float Radius;
		int32 Count;
		EPCGPhysicsBodyType BodyType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BodyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BodyType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_BodyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_BodyType = { "BodyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms, BodyType), Z_Construct_UEnum_Aura_EPCGPhysicsBodyType, METADATA_PARAMS(0, nullptr) }; // 4074164504
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_Count,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_BodyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_BodyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GeneratePhysicsCluster", Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::PCGChaosIntegrator_eventGeneratePhysicsCluster_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGeneratePhysicsCluster)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_GET_ENUM(EPCGPhysicsBodyType,Z_Param_BodyType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GeneratePhysicsCluster(Z_Param_Out_Center,Z_Param_Radius,Z_Param_Count,EPCGPhysicsBodyType(Z_Param_BodyType));
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GeneratePhysicsCluster ************************

// ********** Begin Class APCGChaosIntegrator Function GeneratePhysicsFromPCGData ******************
struct Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics
{
	struct PCGChaosIntegrator_eventGeneratePhysicsFromPCGData_Parms
	{
		TArray<FTransform> Transforms;
		EPCGPhysicsBodyType BodyType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transforms_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transforms_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Transforms;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BodyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BodyType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_Transforms_Inner = { "Transforms", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_Transforms = { "Transforms", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsFromPCGData_Parms, Transforms), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transforms_MetaData), NewProp_Transforms_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_BodyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_BodyType = { "BodyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGeneratePhysicsFromPCGData_Parms, BodyType), Z_Construct_UEnum_Aura_EPCGPhysicsBodyType, METADATA_PARAMS(0, nullptr) }; // 4074164504
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_Transforms_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_Transforms,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_BodyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::NewProp_BodyType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GeneratePhysicsFromPCGData", Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::PCGChaosIntegrator_eventGeneratePhysicsFromPCGData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::PCGChaosIntegrator_eventGeneratePhysicsFromPCGData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGeneratePhysicsFromPCGData)
{
	P_GET_TARRAY_REF(FTransform,Z_Param_Out_Transforms);
	P_GET_ENUM(EPCGPhysicsBodyType,Z_Param_BodyType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GeneratePhysicsFromPCGData(Z_Param_Out_Transforms,EPCGPhysicsBodyType(Z_Param_BodyType));
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GeneratePhysicsFromPCGData ********************

// ********** Begin Class APCGChaosIntegrator Function GetAllPhysicsBodies *************************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics
{
	struct PCGChaosIntegrator_eventGetAllPhysicsBodies_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Bodies" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetAllPhysicsBodies_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetAllPhysicsBodies", Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::PCGChaosIntegrator_eventGetAllPhysicsBodies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::PCGChaosIntegrator_eventGetAllPhysicsBodies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetAllPhysicsBodies)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAllPhysicsBodies();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetAllPhysicsBodies ***************************

// ********** Begin Class APCGChaosIntegrator Function GetAllPhysicsConstraints ********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics
{
	struct PCGChaosIntegrator_eventGetAllPhysicsConstraints_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Constraints" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetAllPhysicsConstraints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetAllPhysicsConstraints", Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::PCGChaosIntegrator_eventGetAllPhysicsConstraints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::PCGChaosIntegrator_eventGetAllPhysicsConstraints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetAllPhysicsConstraints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAllPhysicsConstraints();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetAllPhysicsConstraints **********************

// ********** Begin Class APCGChaosIntegrator Function GetBodyAngularVelocity **********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics
{
	struct PCGChaosIntegrator_eventGetBodyAngularVelocity_Parms
	{
		FString BodyID;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetBodyAngularVelocity_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetBodyAngularVelocity_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetBodyAngularVelocity", Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::PCGChaosIntegrator_eventGetBodyAngularVelocity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::PCGChaosIntegrator_eventGetBodyAngularVelocity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetBodyAngularVelocity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetBodyAngularVelocity(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetBodyAngularVelocity ************************

// ********** Begin Class APCGChaosIntegrator Function GetBodyLinearVelocity ***********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics
{
	struct PCGChaosIntegrator_eventGetBodyLinearVelocity_Parms
	{
		FString BodyID;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics Body State\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Body State" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetBodyLinearVelocity_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetBodyLinearVelocity_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetBodyLinearVelocity", Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::PCGChaosIntegrator_eventGetBodyLinearVelocity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::PCGChaosIntegrator_eventGetBodyLinearVelocity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetBodyLinearVelocity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetBodyLinearVelocity(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetBodyLinearVelocity *************************

// ********** Begin Class APCGChaosIntegrator Function GetConstraintsForBody ***********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics
{
	struct PCGChaosIntegrator_eventGetConstraintsForBody_Parms
	{
		FString BodyID;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Constraints" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetConstraintsForBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetConstraintsForBody_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetConstraintsForBody", Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::PCGChaosIntegrator_eventGetConstraintsForBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::PCGChaosIntegrator_eventGetConstraintsForBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetConstraintsForBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetConstraintsForBody(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetConstraintsForBody *************************

// ********** Begin Class APCGChaosIntegrator Function GetGravity **********************************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics
{
	struct PCGChaosIntegrator_eventGetGravity_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetGravity_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetGravity", Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::PCGChaosIntegrator_eventGetGravity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::PCGChaosIntegrator_eventGetGravity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetGravity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetGravity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetGravity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetGravity();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetGravity ************************************

// ********** Begin Class APCGChaosIntegrator Function GetOverlappingBodies ************************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics
{
	struct PCGChaosIntegrator_eventGetOverlappingBodies_Parms
	{
		FString BodyID;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetOverlappingBodies_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetOverlappingBodies_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetOverlappingBodies", Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::PCGChaosIntegrator_eventGetOverlappingBodies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::PCGChaosIntegrator_eventGetOverlappingBodies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetOverlappingBodies)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetOverlappingBodies(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetOverlappingBodies **************************

// ********** Begin Class APCGChaosIntegrator Function GetPerformanceStatistics ********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics
{
	struct PCGChaosIntegrator_eventGetPerformanceStatistics_Parms
	{
		FPCGPhysicsPerformanceStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance and Optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance and Optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetPerformanceStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats, METADATA_PARAMS(0, nullptr) }; // 3406102424
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetPerformanceStatistics", Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::PCGChaosIntegrator_eventGetPerformanceStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::PCGChaosIntegrator_eventGetPerformanceStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetPerformanceStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGPhysicsPerformanceStats*)Z_Param__Result=P_THIS->GetPerformanceStatistics();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetPerformanceStatistics **********************

// ********** Begin Class APCGChaosIntegrator Function GetPhysicsBodiesByType **********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics
{
	struct PCGChaosIntegrator_eventGetPhysicsBodiesByType_Parms
	{
		EPCGPhysicsBodyType BodyType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Bodies" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BodyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BodyType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_BodyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_BodyType = { "BodyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetPhysicsBodiesByType_Parms, BodyType), Z_Construct_UEnum_Aura_EPCGPhysicsBodyType, METADATA_PARAMS(0, nullptr) }; // 4074164504
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetPhysicsBodiesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_BodyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_BodyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetPhysicsBodiesByType", Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::PCGChaosIntegrator_eventGetPhysicsBodiesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::PCGChaosIntegrator_eventGetPhysicsBodiesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetPhysicsBodiesByType)
{
	P_GET_ENUM(EPCGPhysicsBodyType,Z_Param_BodyType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPhysicsBodiesByType(EPCGPhysicsBodyType(Z_Param_BodyType));
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetPhysicsBodiesByType ************************

// ********** Begin Class APCGChaosIntegrator Function GetPhysicsBodyData **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics
{
	struct PCGChaosIntegrator_eventGetPhysicsBodyData_Parms
	{
		FString BodyID;
		FPCGPhysicsBodyData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Bodies" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetPhysicsBodyData_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetPhysicsBodyData_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGPhysicsBodyData, METADATA_PARAMS(0, nullptr) }; // 2034192839
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetPhysicsBodyData", Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::PCGChaosIntegrator_eventGetPhysicsBodyData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::PCGChaosIntegrator_eventGetPhysicsBodyData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetPhysicsBodyData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGPhysicsBodyData*)Z_Param__Result=P_THIS->GetPhysicsBodyData(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetPhysicsBodyData ****************************

// ********** Begin Class APCGChaosIntegrator Function GetPhysicsConstraintData ********************
struct Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics
{
	struct PCGChaosIntegrator_eventGetPhysicsConstraintData_Parms
	{
		FString ConstraintID;
		FPCGPhysicsConstraintData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Constraints" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConstraintID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::NewProp_ConstraintID = { "ConstraintID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetPhysicsConstraintData_Parms, ConstraintID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintID_MetaData), NewProp_ConstraintID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventGetPhysicsConstraintData_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGPhysicsConstraintData, METADATA_PARAMS(0, nullptr) }; // 3852657120
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::NewProp_ConstraintID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "GetPhysicsConstraintData", Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::PCGChaosIntegrator_eventGetPhysicsConstraintData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::PCGChaosIntegrator_eventGetPhysicsConstraintData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execGetPhysicsConstraintData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ConstraintID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGPhysicsConstraintData*)Z_Param__Result=P_THIS->GetPhysicsConstraintData(Z_Param_ConstraintID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function GetPhysicsConstraintData **********************

// ********** Begin Class APCGChaosIntegrator Function InitializePhysics ***************************
struct Z_Construct_UFunction_APCGChaosIntegrator_InitializePhysics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Initialization and Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialization and Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_InitializePhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "InitializePhysics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_InitializePhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_InitializePhysics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_InitializePhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_InitializePhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execInitializePhysics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePhysics();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function InitializePhysics *****************************

// ********** Begin Class APCGChaosIntegrator Function IntegrateWithCache **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics
{
	struct PCGChaosIntegrator_eventIntegrateWithCache_Parms
	{
		APCGCacheManager* CacheManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CacheManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::NewProp_CacheManager = { "CacheManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventIntegrateWithCache_Parms, CacheManager), Z_Construct_UClass_APCGCacheManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::NewProp_CacheManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "IntegrateWithCache", Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::PCGChaosIntegrator_eventIntegrateWithCache_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::PCGChaosIntegrator_eventIntegrateWithCache_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execIntegrateWithCache)
{
	P_GET_OBJECT(APCGCacheManager,Z_Param_CacheManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithCache(Z_Param_CacheManager);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function IntegrateWithCache ****************************

// ********** Begin Class APCGChaosIntegrator Function IntegrateWithLumen **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics
{
	struct PCGChaosIntegrator_eventIntegrateWithLumen_Parms
	{
		APCGLumenIntegrator* LumenIntegrator;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LumenIntegrator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::NewProp_LumenIntegrator = { "LumenIntegrator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventIntegrateWithLumen_Parms, LumenIntegrator), Z_Construct_UClass_APCGLumenIntegrator_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::NewProp_LumenIntegrator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "IntegrateWithLumen", Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::PCGChaosIntegrator_eventIntegrateWithLumen_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::PCGChaosIntegrator_eventIntegrateWithLumen_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execIntegrateWithLumen)
{
	P_GET_OBJECT(APCGLumenIntegrator,Z_Param_LumenIntegrator);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithLumen(Z_Param_LumenIntegrator);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function IntegrateWithLumen ****************************

// ********** Begin Class APCGChaosIntegrator Function IntegrateWithNanite *************************
struct Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics
{
	struct PCGChaosIntegrator_eventIntegrateWithNanite_Parms
	{
		APCGNaniteOptimizer* NaniteOptimizer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NaniteOptimizer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer = { "NaniteOptimizer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventIntegrateWithNanite_Parms, NaniteOptimizer), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "IntegrateWithNanite", Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::PCGChaosIntegrator_eventIntegrateWithNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::PCGChaosIntegrator_eventIntegrateWithNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execIntegrateWithNanite)
{
	P_GET_OBJECT(APCGNaniteOptimizer,Z_Param_NaniteOptimizer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithNanite(Z_Param_NaniteOptimizer);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function IntegrateWithNanite ***************************

// ********** Begin Class APCGChaosIntegrator Function IntegrateWithProfiler ***********************
struct Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics
{
	struct PCGChaosIntegrator_eventIntegrateWithProfiler_Parms
	{
		UPCGPerformanceProfiler* PerformanceProfiler;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PerformanceProfiler;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::NewProp_PerformanceProfiler = { "PerformanceProfiler", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventIntegrateWithProfiler_Parms, PerformanceProfiler), Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::NewProp_PerformanceProfiler,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "IntegrateWithProfiler", Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::PCGChaosIntegrator_eventIntegrateWithProfiler_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::PCGChaosIntegrator_eventIntegrateWithProfiler_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execIntegrateWithProfiler)
{
	P_GET_OBJECT(UPCGPerformanceProfiler,Z_Param_PerformanceProfiler);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithProfiler(Z_Param_PerformanceProfiler);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function IntegrateWithProfiler *************************

// ********** Begin Class APCGChaosIntegrator Function IntegrateWithStreaming **********************
struct Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics
{
	struct PCGChaosIntegrator_eventIntegrateWithStreaming_Parms
	{
		APCGStreamingManager* StreamingManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StreamingManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::NewProp_StreamingManager = { "StreamingManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventIntegrateWithStreaming_Parms, StreamingManager), Z_Construct_UClass_APCGStreamingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::NewProp_StreamingManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "IntegrateWithStreaming", Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::PCGChaosIntegrator_eventIntegrateWithStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::PCGChaosIntegrator_eventIntegrateWithStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execIntegrateWithStreaming)
{
	P_GET_OBJECT(APCGStreamingManager,Z_Param_StreamingManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithStreaming(Z_Param_StreamingManager);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function IntegrateWithStreaming ************************

// ********** Begin Class APCGChaosIntegrator Function IntegrateWithWorldPartition *****************
struct Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics
{
	struct PCGChaosIntegrator_eventIntegrateWithWorldPartition_Parms
	{
		APCGWorldPartitionManager* WorldPartitionManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with PCG Systems\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with PCG Systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventIntegrateWithWorldPartition_Parms, WorldPartitionManager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "IntegrateWithWorldPartition", Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::PCGChaosIntegrator_eventIntegrateWithWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::PCGChaosIntegrator_eventIntegrateWithWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execIntegrateWithWorldPartition)
{
	P_GET_OBJECT(APCGWorldPartitionManager,Z_Param_WorldPartitionManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithWorldPartition(Z_Param_WorldPartitionManager);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function IntegrateWithWorldPartition *******************

// ********** Begin Class APCGChaosIntegrator Function IsBodyAwake *********************************
struct Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics
{
	struct PCGChaosIntegrator_eventIsBodyAwake_Parms
	{
		FString BodyID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventIsBodyAwake_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventIsBodyAwake_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventIsBodyAwake_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "IsBodyAwake", Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::PCGChaosIntegrator_eventIsBodyAwake_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::PCGChaosIntegrator_eventIsBodyAwake_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execIsBodyAwake)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsBodyAwake(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function IsBodyAwake ***********************************

// ********** Begin Class APCGChaosIntegrator Function LineTrace ***********************************
struct Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics
{
	struct PCGChaosIntegrator_eventLineTrace_Parms
	{
		FVector Start;
		FVector End;
		FVector HitLocation;
		FString HitBodyID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision Detection\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision Detection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HitLocation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HitBodyID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventLineTrace_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventLineTrace_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_HitLocation = { "HitLocation", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventLineTrace_Parms, HitLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_HitBodyID = { "HitBodyID", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventLineTrace_Parms, HitBodyID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventLineTrace_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventLineTrace_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_HitLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_HitBodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "LineTrace", Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::PCGChaosIntegrator_eventLineTrace_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::PCGChaosIntegrator_eventLineTrace_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_LineTrace()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_LineTrace_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execLineTrace)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_HitLocation);
	P_GET_PROPERTY_REF(FStrProperty,Z_Param_Out_HitBodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LineTrace(Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Out_HitLocation,Z_Param_Out_HitBodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function LineTrace *************************************

// ********** Begin Class APCGChaosIntegrator Function OptimizePhysicsPerformance ******************
struct Z_Construct_UFunction_APCGChaosIntegrator_OptimizePhysicsPerformance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_OptimizePhysicsPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "OptimizePhysicsPerformance", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_OptimizePhysicsPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_OptimizePhysicsPerformance_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_OptimizePhysicsPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_OptimizePhysicsPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execOptimizePhysicsPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizePhysicsPerformance();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function OptimizePhysicsPerformance ********************

// ********** Begin Class APCGChaosIntegrator Function PauseSimulation *****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics
{
	struct PCGChaosIntegrator_eventPauseSimulation_Parms
	{
		bool bPause;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Management" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bPause_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPause;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::NewProp_bPause_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventPauseSimulation_Parms*)Obj)->bPause = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::NewProp_bPause = { "bPause", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventPauseSimulation_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::NewProp_bPause_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::NewProp_bPause,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "PauseSimulation", Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::PCGChaosIntegrator_eventPauseSimulation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::PCGChaosIntegrator_eventPauseSimulation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execPauseSimulation)
{
	P_GET_UBOOL(Z_Param_bPause);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseSimulation(Z_Param_bPause);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function PauseSimulation *******************************

// ********** Begin Class APCGChaosIntegrator Function PutBodyToSleep ******************************
struct Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics
{
	struct PCGChaosIntegrator_eventPutBodyToSleep_Parms
	{
		FString BodyID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventPutBodyToSleep_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::NewProp_BodyID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "PutBodyToSleep", Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::PCGChaosIntegrator_eventPutBodyToSleep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::PCGChaosIntegrator_eventPutBodyToSleep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execPutBodyToSleep)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PutBodyToSleep(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function PutBodyToSleep ********************************

// ********** Begin Class APCGChaosIntegrator Function ResetSimulation *****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_ResetSimulation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Management" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_ResetSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "ResetSimulation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_ResetSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_ResetSimulation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_ResetSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_ResetSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execResetSimulation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetSimulation();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function ResetSimulation *******************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyAngularDamping ***********************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics
{
	struct PCGChaosIntegrator_eventSetBodyAngularDamping_Parms
	{
		FString BodyID;
		float AngularDamping;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngularDamping;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyAngularDamping_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::NewProp_AngularDamping = { "AngularDamping", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyAngularDamping_Parms, AngularDamping), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::NewProp_AngularDamping,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyAngularDamping", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::PCGChaosIntegrator_eventSetBodyAngularDamping_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::PCGChaosIntegrator_eventSetBodyAngularDamping_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyAngularDamping)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AngularDamping);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyAngularDamping(Z_Param_BodyID,Z_Param_AngularDamping);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyAngularDamping *************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyAngularVelocity **********************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics
{
	struct PCGChaosIntegrator_eventSetBodyAngularVelocity_Parms
	{
		FString BodyID;
		FVector AngularVelocity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngularVelocity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AngularVelocity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyAngularVelocity_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::NewProp_AngularVelocity = { "AngularVelocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyAngularVelocity_Parms, AngularVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngularVelocity_MetaData), NewProp_AngularVelocity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::NewProp_AngularVelocity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyAngularVelocity", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::PCGChaosIntegrator_eventSetBodyAngularVelocity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::PCGChaosIntegrator_eventSetBodyAngularVelocity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyAngularVelocity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_AngularVelocity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyAngularVelocity(Z_Param_BodyID,Z_Param_Out_AngularVelocity);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyAngularVelocity ************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyDensity ******************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics
{
	struct PCGChaosIntegrator_eventSetBodyDensity_Parms
	{
		FString BodyID;
		float Density;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyDensity_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyDensity_Parms, Density), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::NewProp_Density,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyDensity", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::PCGChaosIntegrator_eventSetBodyDensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::PCGChaosIntegrator_eventSetBodyDensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyDensity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyDensity(Z_Param_BodyID,Z_Param_Density);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyDensity ********************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyFriction *****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics
{
	struct PCGChaosIntegrator_eventSetBodyFriction_Parms
	{
		FString BodyID;
		float Friction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Friction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyFriction_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::NewProp_Friction = { "Friction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyFriction_Parms, Friction), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::NewProp_Friction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyFriction", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::PCGChaosIntegrator_eventSetBodyFriction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::PCGChaosIntegrator_eventSetBodyFriction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyFriction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Friction);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyFriction(Z_Param_BodyID,Z_Param_Friction);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyFriction *******************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyKinematic ****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics
{
	struct PCGChaosIntegrator_eventSetBodyKinematic_Parms
	{
		FString BodyID;
		bool bKinematic;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static void NewProp_bKinematic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bKinematic;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyKinematic_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
void Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::NewProp_bKinematic_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventSetBodyKinematic_Parms*)Obj)->bKinematic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::NewProp_bKinematic = { "bKinematic", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventSetBodyKinematic_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::NewProp_bKinematic_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::NewProp_bKinematic,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyKinematic", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::PCGChaosIntegrator_eventSetBodyKinematic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::PCGChaosIntegrator_eventSetBodyKinematic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyKinematic)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_UBOOL(Z_Param_bKinematic);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyKinematic(Z_Param_BodyID,Z_Param_bKinematic);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyKinematic ******************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyLinearDamping ************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics
{
	struct PCGChaosIntegrator_eventSetBodyLinearDamping_Parms
	{
		FString BodyID;
		float LinearDamping;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LinearDamping;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyLinearDamping_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::NewProp_LinearDamping = { "LinearDamping", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyLinearDamping_Parms, LinearDamping), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::NewProp_LinearDamping,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyLinearDamping", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::PCGChaosIntegrator_eventSetBodyLinearDamping_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::PCGChaosIntegrator_eventSetBodyLinearDamping_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyLinearDamping)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LinearDamping);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyLinearDamping(Z_Param_BodyID,Z_Param_LinearDamping);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyLinearDamping **************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyLinearVelocity ***********************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics
{
	struct PCGChaosIntegrator_eventSetBodyLinearVelocity_Parms
	{
		FString BodyID;
		FVector Velocity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyLinearVelocity_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyLinearVelocity_Parms, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::NewProp_Velocity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyLinearVelocity", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::PCGChaosIntegrator_eventSetBodyLinearVelocity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::PCGChaosIntegrator_eventSetBodyLinearVelocity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyLinearVelocity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Velocity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyLinearVelocity(Z_Param_BodyID,Z_Param_Out_Velocity);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyLinearVelocity *************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyMass *********************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics
{
	struct PCGChaosIntegrator_eventSetBodyMass_Parms
	{
		FString BodyID;
		float Mass;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics Body Properties\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Body Properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Mass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyMass_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::NewProp_Mass = { "Mass", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyMass_Parms, Mass), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::NewProp_Mass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyMass", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::PCGChaosIntegrator_eventSetBodyMass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::PCGChaosIntegrator_eventSetBodyMass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyMass)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Mass);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyMass(Z_Param_BodyID,Z_Param_Mass);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyMass ***********************************

// ********** Begin Class APCGChaosIntegrator Function SetBodyRestitution **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics
{
	struct PCGChaosIntegrator_eventSetBodyRestitution_Parms
	{
		FString BodyID;
		float Restitution;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Properties" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Restitution;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyRestitution_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::NewProp_Restitution = { "Restitution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetBodyRestitution_Parms, Restitution), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::NewProp_Restitution,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetBodyRestitution", Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::PCGChaosIntegrator_eventSetBodyRestitution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::PCGChaosIntegrator_eventSetBodyRestitution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetBodyRestitution)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Restitution);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBodyRestitution(Z_Param_BodyID,Z_Param_Restitution);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetBodyRestitution ****************************

// ********** Begin Class APCGChaosIntegrator Function SetCullingDistance **************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics
{
	struct PCGChaosIntegrator_eventSetCullingDistance_Parms
	{
		float Distance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetCullingDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::NewProp_Distance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetCullingDistance", Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::PCGChaosIntegrator_eventSetCullingDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::PCGChaosIntegrator_eventSetCullingDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetCullingDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCullingDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetCullingDistance ****************************

// ********** Begin Class APCGChaosIntegrator Function SetGravity **********************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics
{
	struct PCGChaosIntegrator_eventSetGravity_Parms
	{
		FVector Gravity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Gravity_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Gravity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::NewProp_Gravity = { "Gravity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetGravity_Parms, Gravity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Gravity_MetaData), NewProp_Gravity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::NewProp_Gravity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetGravity", Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::PCGChaosIntegrator_eventSetGravity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::PCGChaosIntegrator_eventSetGravity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetGravity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetGravity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetGravity)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Gravity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGravity(Z_Param_Out_Gravity);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetGravity ************************************

// ********** Begin Class APCGChaosIntegrator Function SetPhysicsLOD *******************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics
{
	struct PCGChaosIntegrator_eventSetPhysicsLOD_Parms
	{
		FString BodyID;
		int32 LODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetPhysicsLOD_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetPhysicsLOD_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetPhysicsLOD", Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::PCGChaosIntegrator_eventSetPhysicsLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::PCGChaosIntegrator_eventSetPhysicsLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetPhysicsLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPhysicsLOD(Z_Param_BodyID,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetPhysicsLOD *********************************

// ********** Begin Class APCGChaosIntegrator Function SetPhysicsQuality ***************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics
{
	struct PCGChaosIntegrator_eventSetPhysicsQuality_Parms
	{
		EPCGPhysicsQuality Quality;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSetPhysicsQuality_Parms, Quality), Z_Construct_UEnum_Aura_EPCGPhysicsQuality, METADATA_PARAMS(0, nullptr) }; // 3982702251
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::NewProp_Quality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SetPhysicsQuality", Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::PCGChaosIntegrator_eventSetPhysicsQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::PCGChaosIntegrator_eventSetPhysicsQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSetPhysicsQuality)
{
	P_GET_ENUM(EPCGPhysicsQuality,Z_Param_Quality);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPhysicsQuality(EPCGPhysicsQuality(Z_Param_Quality));
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SetPhysicsQuality *****************************

// ********** Begin Class APCGChaosIntegrator Function ShutdownPhysics *****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_ShutdownPhysics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Management" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_ShutdownPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "ShutdownPhysics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_ShutdownPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_ShutdownPhysics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_ShutdownPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_ShutdownPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execShutdownPhysics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownPhysics();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function ShutdownPhysics *******************************

// ********** Begin Class APCGChaosIntegrator Function SphereTrace *********************************
struct Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics
{
	struct PCGChaosIntegrator_eventSphereTrace_Parms
	{
		FVector Start;
		FVector End;
		float Radius;
		TArray<FString> HitBodyIDs;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HitBodyIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HitBodyIDs;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSphereTrace_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSphereTrace_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSphereTrace_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_HitBodyIDs_Inner = { "HitBodyIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_HitBodyIDs = { "HitBodyIDs", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventSphereTrace_Parms, HitBodyIDs), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventSphereTrace_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventSphereTrace_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_HitBodyIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_HitBodyIDs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "SphereTrace", Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::PCGChaosIntegrator_eventSphereTrace_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::PCGChaosIntegrator_eventSphereTrace_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execSphereTrace)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_TARRAY_REF(FString,Z_Param_Out_HitBodyIDs);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SphereTrace(Z_Param_Out_Start,Z_Param_Out_End,Z_Param_Radius,Z_Param_Out_HitBodyIDs);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function SphereTrace ***********************************

// ********** Begin Class APCGChaosIntegrator Function StartSimulation *****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_StartSimulation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Management" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_StartSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "StartSimulation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_StartSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_StartSimulation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_StartSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_StartSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execStartSimulation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartSimulation();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function StartSimulation *******************************

// ********** Begin Class APCGChaosIntegrator Function StopSimulation ******************************
struct Z_Construct_UFunction_APCGChaosIntegrator_StopSimulation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Management" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_StopSimulation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "StopSimulation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_StopSimulation_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_StopSimulation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_StopSimulation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_StopSimulation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execStopSimulation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopSimulation();
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function StopSimulation ********************************

// ********** Begin Class APCGChaosIntegrator Function UpdatePhysicsBody ***************************
struct Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics
{
	struct PCGChaosIntegrator_eventUpdatePhysicsBody_Parms
	{
		FString BodyID;
		FPCGPhysicsBodyData NewBodyData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Bodies" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewBodyData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewBodyData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventUpdatePhysicsBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_NewBodyData = { "NewBodyData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventUpdatePhysicsBody_Parms, NewBodyData), Z_Construct_UScriptStruct_FPCGPhysicsBodyData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewBodyData_MetaData), NewProp_NewBodyData_MetaData) }; // 2034192839
void Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventUpdatePhysicsBody_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventUpdatePhysicsBody_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_BodyID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_NewBodyData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "UpdatePhysicsBody", Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::PCGChaosIntegrator_eventUpdatePhysicsBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::PCGChaosIntegrator_eventUpdatePhysicsBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execUpdatePhysicsBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_GET_STRUCT_REF(FPCGPhysicsBodyData,Z_Param_Out_NewBodyData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdatePhysicsBody(Z_Param_BodyID,Z_Param_Out_NewBodyData);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function UpdatePhysicsBody *****************************

// ********** Begin Class APCGChaosIntegrator Function UpdatePhysicsConstraint *********************
struct Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics
{
	struct PCGChaosIntegrator_eventUpdatePhysicsConstraint_Parms
	{
		FString ConstraintID;
		FPCGPhysicsConstraintData NewConstraintData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics Constraints" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstraintID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConstraintData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConstraintID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConstraintData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_ConstraintID = { "ConstraintID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventUpdatePhysicsConstraint_Parms, ConstraintID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstraintID_MetaData), NewProp_ConstraintID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_NewConstraintData = { "NewConstraintData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventUpdatePhysicsConstraint_Parms, NewConstraintData), Z_Construct_UScriptStruct_FPCGPhysicsConstraintData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConstraintData_MetaData), NewProp_NewConstraintData_MetaData) }; // 3852657120
void Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGChaosIntegrator_eventUpdatePhysicsConstraint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGChaosIntegrator_eventUpdatePhysicsConstraint_Parms), &Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_ConstraintID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_NewConstraintData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "UpdatePhysicsConstraint", Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::PCGChaosIntegrator_eventUpdatePhysicsConstraint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::PCGChaosIntegrator_eventUpdatePhysicsConstraint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execUpdatePhysicsConstraint)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ConstraintID);
	P_GET_STRUCT_REF(FPCGPhysicsConstraintData,Z_Param_Out_NewConstraintData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdatePhysicsConstraint(Z_Param_ConstraintID,Z_Param_Out_NewConstraintData);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function UpdatePhysicsConstraint ***********************

// ********** Begin Class APCGChaosIntegrator Function UpdatePhysicsLOD ****************************
struct Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics
{
	struct PCGChaosIntegrator_eventUpdatePhysicsLOD_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventUpdatePhysicsLOD_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "UpdatePhysicsLOD", Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::PCGChaosIntegrator_eventUpdatePhysicsLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::PCGChaosIntegrator_eventUpdatePhysicsLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execUpdatePhysicsLOD)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePhysicsLOD(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function UpdatePhysicsLOD ******************************

// ********** Begin Class APCGChaosIntegrator Function WakeBody ************************************
struct Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics
{
	struct PCGChaosIntegrator_eventWakeBody_Parms
	{
		FString BodyID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BodyID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BodyID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::NewProp_BodyID = { "BodyID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGChaosIntegrator_eventWakeBody_Parms, BodyID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BodyID_MetaData), NewProp_BodyID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::NewProp_BodyID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGChaosIntegrator, nullptr, "WakeBody", Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::PCGChaosIntegrator_eventWakeBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::PCGChaosIntegrator_eventWakeBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGChaosIntegrator_WakeBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGChaosIntegrator_WakeBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGChaosIntegrator::execWakeBody)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BodyID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->WakeBody(Z_Param_BodyID);
	P_NATIVE_END;
}
// ********** End Class APCGChaosIntegrator Function WakeBody **************************************

// ********** Begin Class APCGChaosIntegrator ******************************************************
void APCGChaosIntegrator::StaticRegisterNativesAPCGChaosIntegrator()
{
	UClass* Class = APCGChaosIntegrator::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddAngularImpulseToBody", &APCGChaosIntegrator::execAddAngularImpulseToBody },
		{ "AddForceAtLocation", &APCGChaosIntegrator::execAddForceAtLocation },
		{ "AddForceToBody", &APCGChaosIntegrator::execAddForceToBody },
		{ "AddImpulseAtLocation", &APCGChaosIntegrator::execAddImpulseAtLocation },
		{ "AddImpulseToBody", &APCGChaosIntegrator::execAddImpulseToBody },
		{ "AddTorqueToBody", &APCGChaosIntegrator::execAddTorqueToBody },
		{ "AreOverlapping", &APCGChaosIntegrator::execAreOverlapping },
		{ "BoxTrace", &APCGChaosIntegrator::execBoxTrace },
		{ "CreatePhysicsBody", &APCGChaosIntegrator::execCreatePhysicsBody },
		{ "CreatePhysicsConstraint", &APCGChaosIntegrator::execCreatePhysicsConstraint },
		{ "CreatePhysicsIsland", &APCGChaosIntegrator::execCreatePhysicsIsland },
		{ "DestroyPhysicsBody", &APCGChaosIntegrator::execDestroyPhysicsBody },
		{ "DestroyPhysicsConstraint", &APCGChaosIntegrator::execDestroyPhysicsConstraint },
		{ "DestroyPhysicsIsland", &APCGChaosIntegrator::execDestroyPhysicsIsland },
		{ "EnableAsyncPhysics", &APCGChaosIntegrator::execEnableAsyncPhysics },
		{ "EnablePhysics", &APCGChaosIntegrator::execEnablePhysics },
		{ "EnablePhysicsCulling", &APCGChaosIntegrator::execEnablePhysicsCulling },
		{ "GeneratePhysicsBodiesFromMesh", &APCGChaosIntegrator::execGeneratePhysicsBodiesFromMesh },
		{ "GeneratePhysicsBodyFromGeometry", &APCGChaosIntegrator::execGeneratePhysicsBodyFromGeometry },
		{ "GeneratePhysicsChain", &APCGChaosIntegrator::execGeneratePhysicsChain },
		{ "GeneratePhysicsCluster", &APCGChaosIntegrator::execGeneratePhysicsCluster },
		{ "GeneratePhysicsFromPCGData", &APCGChaosIntegrator::execGeneratePhysicsFromPCGData },
		{ "GetAllPhysicsBodies", &APCGChaosIntegrator::execGetAllPhysicsBodies },
		{ "GetAllPhysicsConstraints", &APCGChaosIntegrator::execGetAllPhysicsConstraints },
		{ "GetBodyAngularVelocity", &APCGChaosIntegrator::execGetBodyAngularVelocity },
		{ "GetBodyLinearVelocity", &APCGChaosIntegrator::execGetBodyLinearVelocity },
		{ "GetConstraintsForBody", &APCGChaosIntegrator::execGetConstraintsForBody },
		{ "GetGravity", &APCGChaosIntegrator::execGetGravity },
		{ "GetOverlappingBodies", &APCGChaosIntegrator::execGetOverlappingBodies },
		{ "GetPerformanceStatistics", &APCGChaosIntegrator::execGetPerformanceStatistics },
		{ "GetPhysicsBodiesByType", &APCGChaosIntegrator::execGetPhysicsBodiesByType },
		{ "GetPhysicsBodyData", &APCGChaosIntegrator::execGetPhysicsBodyData },
		{ "GetPhysicsConstraintData", &APCGChaosIntegrator::execGetPhysicsConstraintData },
		{ "InitializePhysics", &APCGChaosIntegrator::execInitializePhysics },
		{ "IntegrateWithCache", &APCGChaosIntegrator::execIntegrateWithCache },
		{ "IntegrateWithLumen", &APCGChaosIntegrator::execIntegrateWithLumen },
		{ "IntegrateWithNanite", &APCGChaosIntegrator::execIntegrateWithNanite },
		{ "IntegrateWithProfiler", &APCGChaosIntegrator::execIntegrateWithProfiler },
		{ "IntegrateWithStreaming", &APCGChaosIntegrator::execIntegrateWithStreaming },
		{ "IntegrateWithWorldPartition", &APCGChaosIntegrator::execIntegrateWithWorldPartition },
		{ "IsBodyAwake", &APCGChaosIntegrator::execIsBodyAwake },
		{ "LineTrace", &APCGChaosIntegrator::execLineTrace },
		{ "OptimizePhysicsPerformance", &APCGChaosIntegrator::execOptimizePhysicsPerformance },
		{ "PauseSimulation", &APCGChaosIntegrator::execPauseSimulation },
		{ "PutBodyToSleep", &APCGChaosIntegrator::execPutBodyToSleep },
		{ "ResetSimulation", &APCGChaosIntegrator::execResetSimulation },
		{ "SetBodyAngularDamping", &APCGChaosIntegrator::execSetBodyAngularDamping },
		{ "SetBodyAngularVelocity", &APCGChaosIntegrator::execSetBodyAngularVelocity },
		{ "SetBodyDensity", &APCGChaosIntegrator::execSetBodyDensity },
		{ "SetBodyFriction", &APCGChaosIntegrator::execSetBodyFriction },
		{ "SetBodyKinematic", &APCGChaosIntegrator::execSetBodyKinematic },
		{ "SetBodyLinearDamping", &APCGChaosIntegrator::execSetBodyLinearDamping },
		{ "SetBodyLinearVelocity", &APCGChaosIntegrator::execSetBodyLinearVelocity },
		{ "SetBodyMass", &APCGChaosIntegrator::execSetBodyMass },
		{ "SetBodyRestitution", &APCGChaosIntegrator::execSetBodyRestitution },
		{ "SetCullingDistance", &APCGChaosIntegrator::execSetCullingDistance },
		{ "SetGravity", &APCGChaosIntegrator::execSetGravity },
		{ "SetPhysicsLOD", &APCGChaosIntegrator::execSetPhysicsLOD },
		{ "SetPhysicsQuality", &APCGChaosIntegrator::execSetPhysicsQuality },
		{ "ShutdownPhysics", &APCGChaosIntegrator::execShutdownPhysics },
		{ "SphereTrace", &APCGChaosIntegrator::execSphereTrace },
		{ "StartSimulation", &APCGChaosIntegrator::execStartSimulation },
		{ "StopSimulation", &APCGChaosIntegrator::execStopSimulation },
		{ "UpdatePhysicsBody", &APCGChaosIntegrator::execUpdatePhysicsBody },
		{ "UpdatePhysicsConstraint", &APCGChaosIntegrator::execUpdatePhysicsConstraint },
		{ "UpdatePhysicsLOD", &APCGChaosIntegrator::execUpdatePhysicsLOD },
		{ "WakeBody", &APCGChaosIntegrator::execWakeBody },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_APCGChaosIntegrator;
UClass* APCGChaosIntegrator::GetPrivateStaticClass()
{
	using TClass = APCGChaosIntegrator;
	if (!Z_Registration_Info_UClass_APCGChaosIntegrator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGChaosIntegrator"),
			Z_Registration_Info_UClass_APCGChaosIntegrator.InnerSingleton,
			StaticRegisterNativesAPCGChaosIntegrator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_APCGChaosIntegrator.InnerSingleton;
}
UClass* Z_Construct_UClass_APCGChaosIntegrator_NoRegister()
{
	return APCGChaosIntegrator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_APCGChaosIntegrator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * APCGChaosIntegrator - Advanced Chaos Physics integration for PCG content\n * \n * Features:\n * - Procedural physics body generation\n * - Dynamic constraint creation\n * - Physics-based LOD system\n * - Async physics simulation\n * - Performance optimization\n * - Integration with other PCG systems\n * - Real-time physics events\n * - Advanced collision detection\n */" },
#endif
		{ "IncludePath", "APCGChaosIntegrator.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "APCGChaosIntegrator - Advanced Chaos Physics integration for PCG content\n\nFeatures:\n- Procedural physics body generation\n- Dynamic constraint creation\n- Physics-based LOD system\n- Async physics simulation\n- Performance optimization\n- Integration with other PCG systems\n- Real-time physics events\n- Advanced collision detection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsConfig_MetaData[] = {
		{ "Category", "Physics Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEnabled_MetaData[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSimulationRunning_MetaData[] = {
		{ "Category", "Physics State" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhysicsBodyCreated_MetaData[] = {
		{ "Category", "Physics Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event delegates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhysicsBodyDestroyed_MetaData[] = {
		{ "Category", "Physics Events" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhysicsConstraintCreated_MetaData[] = {
		{ "Category", "Physics Events" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhysicsConstraintBroken_MetaData[] = {
		{ "Category", "Physics Events" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhysicsEvent_MetaData[] = {
		{ "Category", "Physics Events" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhysicsPerformanceUpdated_MetaData[] = {
		{ "Category", "Physics Events" },
		{ "ModuleRelativePath", "Public/APCGChaosIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PhysicsConfig;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static void NewProp_bSimulationRunning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSimulationRunning;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhysicsBodyCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhysicsBodyDestroyed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhysicsConstraintCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhysicsConstraintBroken;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhysicsEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhysicsPerformanceUpdated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APCGChaosIntegrator_AddAngularImpulseToBody, "AddAngularImpulseToBody" }, // 737595785
		{ &Z_Construct_UFunction_APCGChaosIntegrator_AddForceAtLocation, "AddForceAtLocation" }, // 1830141100
		{ &Z_Construct_UFunction_APCGChaosIntegrator_AddForceToBody, "AddForceToBody" }, // 3844119352
		{ &Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseAtLocation, "AddImpulseAtLocation" }, // 2640749781
		{ &Z_Construct_UFunction_APCGChaosIntegrator_AddImpulseToBody, "AddImpulseToBody" }, // 261649594
		{ &Z_Construct_UFunction_APCGChaosIntegrator_AddTorqueToBody, "AddTorqueToBody" }, // 891835192
		{ &Z_Construct_UFunction_APCGChaosIntegrator_AreOverlapping, "AreOverlapping" }, // 2018359786
		{ &Z_Construct_UFunction_APCGChaosIntegrator_BoxTrace, "BoxTrace" }, // 2427880891
		{ &Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsBody, "CreatePhysicsBody" }, // 1849399681
		{ &Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsConstraint, "CreatePhysicsConstraint" }, // 1272090293
		{ &Z_Construct_UFunction_APCGChaosIntegrator_CreatePhysicsIsland, "CreatePhysicsIsland" }, // 2910482129
		{ &Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsBody, "DestroyPhysicsBody" }, // 1171332566
		{ &Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsConstraint, "DestroyPhysicsConstraint" }, // 354592325
		{ &Z_Construct_UFunction_APCGChaosIntegrator_DestroyPhysicsIsland, "DestroyPhysicsIsland" }, // 3014118229
		{ &Z_Construct_UFunction_APCGChaosIntegrator_EnableAsyncPhysics, "EnableAsyncPhysics" }, // 2394404759
		{ &Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysics, "EnablePhysics" }, // 3316240217
		{ &Z_Construct_UFunction_APCGChaosIntegrator_EnablePhysicsCulling, "EnablePhysicsCulling" }, // 853419148
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodiesFromMesh, "GeneratePhysicsBodiesFromMesh" }, // 3236541803
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsBodyFromGeometry, "GeneratePhysicsBodyFromGeometry" }, // 4262432615
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsChain, "GeneratePhysicsChain" }, // 1970866770
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsCluster, "GeneratePhysicsCluster" }, // 3795980055
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GeneratePhysicsFromPCGData, "GeneratePhysicsFromPCGData" }, // 2063519124
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsBodies, "GetAllPhysicsBodies" }, // 2872441449
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetAllPhysicsConstraints, "GetAllPhysicsConstraints" }, // 1931733152
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetBodyAngularVelocity, "GetBodyAngularVelocity" }, // 1163461942
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetBodyLinearVelocity, "GetBodyLinearVelocity" }, // 556691270
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetConstraintsForBody, "GetConstraintsForBody" }, // 3255355241
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetGravity, "GetGravity" }, // 1991606211
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetOverlappingBodies, "GetOverlappingBodies" }, // 509676088
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetPerformanceStatistics, "GetPerformanceStatistics" }, // 1859460629
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodiesByType, "GetPhysicsBodiesByType" }, // 2553770197
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsBodyData, "GetPhysicsBodyData" }, // 2991594364
		{ &Z_Construct_UFunction_APCGChaosIntegrator_GetPhysicsConstraintData, "GetPhysicsConstraintData" }, // 2760342060
		{ &Z_Construct_UFunction_APCGChaosIntegrator_InitializePhysics, "InitializePhysics" }, // 556738299
		{ &Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithCache, "IntegrateWithCache" }, // 468404077
		{ &Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithLumen, "IntegrateWithLumen" }, // 1707363323
		{ &Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithNanite, "IntegrateWithNanite" }, // 2658279849
		{ &Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithProfiler, "IntegrateWithProfiler" }, // 3235389949
		{ &Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithStreaming, "IntegrateWithStreaming" }, // 941366083
		{ &Z_Construct_UFunction_APCGChaosIntegrator_IntegrateWithWorldPartition, "IntegrateWithWorldPartition" }, // 2514384659
		{ &Z_Construct_UFunction_APCGChaosIntegrator_IsBodyAwake, "IsBodyAwake" }, // 152041136
		{ &Z_Construct_UFunction_APCGChaosIntegrator_LineTrace, "LineTrace" }, // 3781810930
		{ &Z_Construct_UFunction_APCGChaosIntegrator_OptimizePhysicsPerformance, "OptimizePhysicsPerformance" }, // 3101836157
		{ &Z_Construct_UFunction_APCGChaosIntegrator_PauseSimulation, "PauseSimulation" }, // 3571510883
		{ &Z_Construct_UFunction_APCGChaosIntegrator_PutBodyToSleep, "PutBodyToSleep" }, // 1653299932
		{ &Z_Construct_UFunction_APCGChaosIntegrator_ResetSimulation, "ResetSimulation" }, // 3297411939
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularDamping, "SetBodyAngularDamping" }, // 1457731041
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyAngularVelocity, "SetBodyAngularVelocity" }, // 386507931
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyDensity, "SetBodyDensity" }, // 205546252
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyFriction, "SetBodyFriction" }, // 4070702633
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyKinematic, "SetBodyKinematic" }, // 275096257
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearDamping, "SetBodyLinearDamping" }, // 2793284487
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyLinearVelocity, "SetBodyLinearVelocity" }, // 311685639
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyMass, "SetBodyMass" }, // 3618016313
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetBodyRestitution, "SetBodyRestitution" }, // 3300504301
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetCullingDistance, "SetCullingDistance" }, // 113229713
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetGravity, "SetGravity" }, // 2263901177
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsLOD, "SetPhysicsLOD" }, // 3441935773
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SetPhysicsQuality, "SetPhysicsQuality" }, // 1368497020
		{ &Z_Construct_UFunction_APCGChaosIntegrator_ShutdownPhysics, "ShutdownPhysics" }, // 644310120
		{ &Z_Construct_UFunction_APCGChaosIntegrator_SphereTrace, "SphereTrace" }, // 3171232584
		{ &Z_Construct_UFunction_APCGChaosIntegrator_StartSimulation, "StartSimulation" }, // 1788154819
		{ &Z_Construct_UFunction_APCGChaosIntegrator_StopSimulation, "StopSimulation" }, // 4059140036
		{ &Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsBody, "UpdatePhysicsBody" }, // 2060778580
		{ &Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsConstraint, "UpdatePhysicsConstraint" }, // 3368399527
		{ &Z_Construct_UFunction_APCGChaosIntegrator_UpdatePhysicsLOD, "UpdatePhysicsLOD" }, // 190135162
		{ &Z_Construct_UFunction_APCGChaosIntegrator_WakeBody, "WakeBody" }, // 402831533
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APCGChaosIntegrator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_PhysicsConfig = { "PhysicsConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGChaosIntegrator, PhysicsConfig), Z_Construct_UScriptStruct_FPCGPhysicsConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsConfig_MetaData), NewProp_PhysicsConfig_MetaData) }; // 403897429
void Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((APCGChaosIntegrator*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGChaosIntegrator), &Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((APCGChaosIntegrator*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGChaosIntegrator), &Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEnabled_MetaData), NewProp_bIsEnabled_MetaData) };
void Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bSimulationRunning_SetBit(void* Obj)
{
	((APCGChaosIntegrator*)Obj)->bSimulationRunning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bSimulationRunning = { "bSimulationRunning", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGChaosIntegrator), &Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bSimulationRunning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSimulationRunning_MetaData), NewProp_bSimulationRunning_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsBodyCreated = { "OnPhysicsBodyCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGChaosIntegrator, OnPhysicsBodyCreated), Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhysicsBodyCreated_MetaData), NewProp_OnPhysicsBodyCreated_MetaData) }; // 2526195795
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsBodyDestroyed = { "OnPhysicsBodyDestroyed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGChaosIntegrator, OnPhysicsBodyDestroyed), Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhysicsBodyDestroyed_MetaData), NewProp_OnPhysicsBodyDestroyed_MetaData) }; // 4228731620
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsConstraintCreated = { "OnPhysicsConstraintCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGChaosIntegrator, OnPhysicsConstraintCreated), Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhysicsConstraintCreated_MetaData), NewProp_OnPhysicsConstraintCreated_MetaData) }; // 3544946036
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsConstraintBroken = { "OnPhysicsConstraintBroken", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGChaosIntegrator, OnPhysicsConstraintBroken), Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhysicsConstraintBroken_MetaData), NewProp_OnPhysicsConstraintBroken_MetaData) }; // 2858514651
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsEvent = { "OnPhysicsEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGChaosIntegrator, OnPhysicsEvent), Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhysicsEvent_MetaData), NewProp_OnPhysicsEvent_MetaData) }; // 3401973506
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsPerformanceUpdated = { "OnPhysicsPerformanceUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGChaosIntegrator, OnPhysicsPerformanceUpdated), Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhysicsPerformanceUpdated_MetaData), NewProp_OnPhysicsPerformanceUpdated_MetaData) }; // 3150610619
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APCGChaosIntegrator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_PhysicsConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_bSimulationRunning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsBodyCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsBodyDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsConstraintCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsConstraintBroken,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGChaosIntegrator_Statics::NewProp_OnPhysicsPerformanceUpdated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGChaosIntegrator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APCGChaosIntegrator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGChaosIntegrator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_APCGChaosIntegrator_Statics::ClassParams = {
	&APCGChaosIntegrator::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APCGChaosIntegrator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APCGChaosIntegrator_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APCGChaosIntegrator_Statics::Class_MetaDataParams), Z_Construct_UClass_APCGChaosIntegrator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APCGChaosIntegrator()
{
	if (!Z_Registration_Info_UClass_APCGChaosIntegrator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APCGChaosIntegrator.OuterSingleton, Z_Construct_UClass_APCGChaosIntegrator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APCGChaosIntegrator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APCGChaosIntegrator);
APCGChaosIntegrator::~APCGChaosIntegrator() {}
// ********** End Class APCGChaosIntegrator ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGPhysicsSimulationMode_StaticEnum, TEXT("EPCGPhysicsSimulationMode"), &Z_Registration_Info_UEnum_EPCGPhysicsSimulationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 832010709U) },
		{ EPCGPhysicsBodyType_StaticEnum, TEXT("EPCGPhysicsBodyType"), &Z_Registration_Info_UEnum_EPCGPhysicsBodyType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4074164504U) },
		{ EPCGPhysicsConstraintType_StaticEnum, TEXT("EPCGPhysicsConstraintType"), &Z_Registration_Info_UEnum_EPCGPhysicsConstraintType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2789340363U) },
		{ EPCGPhysicsQuality_StaticEnum, TEXT("EPCGPhysicsQuality"), &Z_Registration_Info_UEnum_EPCGPhysicsQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3982702251U) },
		{ EPCGPhysicsOptimization_StaticEnum, TEXT("EPCGPhysicsOptimization"), &Z_Registration_Info_UEnum_EPCGPhysicsOptimization, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1974953359U) },
		{ EPCGPhysicsEventType_StaticEnum, TEXT("EPCGPhysicsEventType"), &Z_Registration_Info_UEnum_EPCGPhysicsEventType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3334143049U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGPhysicsConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics::NewStructOps, TEXT("PCGPhysicsConfig"), &Z_Registration_Info_UScriptStruct_FPCGPhysicsConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGPhysicsConfig), 403897429U) },
		{ FPCGPhysicsBodyData::StaticStruct, Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics::NewStructOps, TEXT("PCGPhysicsBodyData"), &Z_Registration_Info_UScriptStruct_FPCGPhysicsBodyData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGPhysicsBodyData), 2034192839U) },
		{ FPCGPhysicsConstraintData::StaticStruct, Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics::NewStructOps, TEXT("PCGPhysicsConstraintData"), &Z_Registration_Info_UScriptStruct_FPCGPhysicsConstraintData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGPhysicsConstraintData), 3852657120U) },
		{ FPCGPhysicsPerformanceStats::StaticStruct, Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics::NewStructOps, TEXT("PCGPhysicsPerformanceStats"), &Z_Registration_Info_UScriptStruct_FPCGPhysicsPerformanceStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGPhysicsPerformanceStats), 3406102424U) },
		{ FPCGPhysicsEventData::StaticStruct, Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics::NewStructOps, TEXT("PCGPhysicsEventData"), &Z_Registration_Info_UScriptStruct_FPCGPhysicsEventData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGPhysicsEventData), 1932252492U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APCGChaosIntegrator, APCGChaosIntegrator::StaticClass, TEXT("APCGChaosIntegrator"), &Z_Registration_Info_UClass_APCGChaosIntegrator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APCGChaosIntegrator), 261203968U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_192319300(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
