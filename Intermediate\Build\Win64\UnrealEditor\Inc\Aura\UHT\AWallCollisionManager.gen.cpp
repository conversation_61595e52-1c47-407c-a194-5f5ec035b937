// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AWallCollisionManager.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAWallCollisionManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_AWallCollisionManager();
AURA_API UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EWallMaterial();
AURA_API UEnum* Z_Construct_UEnum_Aura_EWallOrientation();
AURA_API UEnum* Z_Construct_UEnum_Aura_EWallType();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FGateData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPatrolArea();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FWallCollisionData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FWallMaterialConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FWallSection();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EWallType *****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWallType;
static UEnum* EWallType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWallType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWallType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EWallType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EWallType"));
	}
	return Z_Registration_Info_UEnum_EWallType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EWallType>()
{
	return EWallType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EWallType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para tipos de parede\n" },
#endif
		{ "Corner.DisplayName", "Corner Wall" },
		{ "Corner.Name", "EWallType::Corner" },
		{ "Curved.DisplayName", "Curved Wall" },
		{ "Curved.Name", "EWallType::Curved" },
		{ "Gate.DisplayName", "Gate Wall" },
		{ "Gate.Name", "EWallType::Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
		{ "Straight.DisplayName", "Straight Wall" },
		{ "Straight.Name", "EWallType::Straight" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para tipos de parede" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWallType::Straight", (int64)EWallType::Straight },
		{ "EWallType::Corner", (int64)EWallType::Corner },
		{ "EWallType::Gate", (int64)EWallType::Gate },
		{ "EWallType::Curved", (int64)EWallType::Curved },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EWallType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EWallType",
	"EWallType",
	Z_Construct_UEnum_Aura_EWallType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWallType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWallType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EWallType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EWallType()
{
	if (!Z_Registration_Info_UEnum_EWallType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWallType.InnerSingleton, Z_Construct_UEnum_Aura_EWallType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWallType.InnerSingleton;
}
// ********** End Enum EWallType *******************************************************************

// ********** Begin Enum EWallOrientation **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWallOrientation;
static UEnum* EWallOrientation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWallOrientation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWallOrientation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EWallOrientation, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EWallOrientation"));
	}
	return Z_Registration_Info_UEnum_EWallOrientation.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EWallOrientation>()
{
	return EWallOrientation_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EWallOrientation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum para orienta\xc3\xa7\xc3\xa3o da parede\n" },
#endif
		{ "East.DisplayName", "East" },
		{ "East.Name", "EWallOrientation::East" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
		{ "North.DisplayName", "North" },
		{ "North.Name", "EWallOrientation::North" },
		{ "NorthEast.DisplayName", "North East" },
		{ "NorthEast.Name", "EWallOrientation::NorthEast" },
		{ "NorthWest.DisplayName", "North West" },
		{ "NorthWest.Name", "EWallOrientation::NorthWest" },
		{ "South.DisplayName", "South" },
		{ "South.Name", "EWallOrientation::South" },
		{ "SouthEast.DisplayName", "South East" },
		{ "SouthEast.Name", "EWallOrientation::SouthEast" },
		{ "SouthWest.DisplayName", "South West" },
		{ "SouthWest.Name", "EWallOrientation::SouthWest" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para orienta\xc3\xa7\xc3\xa3o da parede" },
#endif
		{ "West.DisplayName", "West" },
		{ "West.Name", "EWallOrientation::West" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWallOrientation::North", (int64)EWallOrientation::North },
		{ "EWallOrientation::South", (int64)EWallOrientation::South },
		{ "EWallOrientation::East", (int64)EWallOrientation::East },
		{ "EWallOrientation::West", (int64)EWallOrientation::West },
		{ "EWallOrientation::NorthEast", (int64)EWallOrientation::NorthEast },
		{ "EWallOrientation::NorthWest", (int64)EWallOrientation::NorthWest },
		{ "EWallOrientation::SouthEast", (int64)EWallOrientation::SouthEast },
		{ "EWallOrientation::SouthWest", (int64)EWallOrientation::SouthWest },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EWallOrientation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EWallOrientation",
	"EWallOrientation",
	Z_Construct_UEnum_Aura_EWallOrientation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWallOrientation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWallOrientation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EWallOrientation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EWallOrientation()
{
	if (!Z_Registration_Info_UEnum_EWallOrientation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWallOrientation.InnerSingleton, Z_Construct_UEnum_Aura_EWallOrientation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWallOrientation.InnerSingleton;
}
// ********** End Enum EWallOrientation ************************************************************

// ********** Begin Enum EWallMaterial *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWallMaterial;
static UEnum* EWallMaterial_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWallMaterial.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWallMaterial.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EWallMaterial, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EWallMaterial"));
	}
	return Z_Registration_Info_UEnum_EWallMaterial.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EWallMaterial>()
{
	return EWallMaterial_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EWallMaterial_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum para material da parede\n" },
#endif
		{ "Crystal.DisplayName", "Crystal" },
		{ "Crystal.Name", "EWallMaterial::Crystal" },
		{ "Magical.DisplayName", "Magical" },
		{ "Magical.Name", "EWallMaterial::Magical" },
		{ "Metal.DisplayName", "Metal" },
		{ "Metal.Name", "EWallMaterial::Metal" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
		{ "Stone.DisplayName", "Stone" },
		{ "Stone.Name", "EWallMaterial::Stone" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para material da parede" },
#endif
		{ "Wood.DisplayName", "Wood" },
		{ "Wood.Name", "EWallMaterial::Wood" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWallMaterial::Stone", (int64)EWallMaterial::Stone },
		{ "EWallMaterial::Metal", (int64)EWallMaterial::Metal },
		{ "EWallMaterial::Wood", (int64)EWallMaterial::Wood },
		{ "EWallMaterial::Crystal", (int64)EWallMaterial::Crystal },
		{ "EWallMaterial::Magical", (int64)EWallMaterial::Magical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EWallMaterial_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EWallMaterial",
	"EWallMaterial",
	Z_Construct_UEnum_Aura_EWallMaterial_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWallMaterial_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWallMaterial_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EWallMaterial_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EWallMaterial()
{
	if (!Z_Registration_Info_UEnum_EWallMaterial.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWallMaterial.InnerSingleton, Z_Construct_UEnum_Aura_EWallMaterial_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWallMaterial.InnerSingleton;
}
// ********** End Enum EWallMaterial ***************************************************************

// ********** Begin ScriptStruct FWallSection ******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FWallSection;
class UScriptStruct* FWallSection::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FWallSection.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FWallSection.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWallSection, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("WallSection"));
	}
	return Z_Registration_Info_UScriptStruct_FWallSection.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FWallSection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para dados de uma se\xc3\xa7\xc3\xa3o de parede\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de uma se\xc3\xa7\xc3\xa3o de parede" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPosition_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPosition_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallType_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Orientation_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Thickness_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasGate_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GatePosition_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GateWidth_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SegmentPoints_MetaData[] = {
		{ "Category", "Wall Section" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WallType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WallType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Orientation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Thickness;
	static void NewProp_bHasGate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasGate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GatePosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GateWidth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SegmentPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SegmentPoints;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWallSection>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_StartPosition = { "StartPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, StartPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPosition_MetaData), NewProp_StartPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_EndPosition = { "EndPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, EndPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPosition_MetaData), NewProp_EndPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_WallType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_WallType = { "WallType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, WallType), Z_Construct_UEnum_Aura_EWallType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallType_MetaData), NewProp_WallType_MetaData) }; // 3601448334
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Orientation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, Orientation), Z_Construct_UEnum_Aura_EWallOrientation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Orientation_MetaData), NewProp_Orientation_MetaData) }; // 3305671181
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Thickness = { "Thickness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, Thickness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Thickness_MetaData), NewProp_Thickness_MetaData) };
void Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_bHasGate_SetBit(void* Obj)
{
	((FWallSection*)Obj)->bHasGate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_bHasGate = { "bHasGate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWallSection), &Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_bHasGate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasGate_MetaData), NewProp_bHasGate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_GatePosition = { "GatePosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, GatePosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GatePosition_MetaData), NewProp_GatePosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_GateWidth = { "GateWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, GateWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GateWidth_MetaData), NewProp_GateWidth_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_SegmentPoints_Inner = { "SegmentPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_SegmentPoints = { "SegmentPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallSection, SegmentPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SegmentPoints_MetaData), NewProp_SegmentPoints_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWallSection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_StartPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_EndPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_WallType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_WallType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Orientation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_Thickness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_bHasGate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_GatePosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_GateWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_SegmentPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallSection_Statics::NewProp_SegmentPoints,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallSection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWallSection_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"WallSection",
	Z_Construct_UScriptStruct_FWallSection_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallSection_Statics::PropPointers),
	sizeof(FWallSection),
	alignof(FWallSection),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallSection_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWallSection_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWallSection()
{
	if (!Z_Registration_Info_UScriptStruct_FWallSection.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FWallSection.InnerSingleton, Z_Construct_UScriptStruct_FWallSection_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FWallSection.InnerSingleton;
}
// ********** End ScriptStruct FWallSection ********************************************************

// ********** Begin ScriptStruct FWallCollisionData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FWallCollisionData;
class UScriptStruct* FWallCollisionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FWallCollisionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FWallCollisionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWallCollisionData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("WallCollisionData"));
	}
	return Z_Registration_Info_UScriptStruct_FWallCollisionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FWallCollisionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para dados de colis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de colis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionPoint_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionNormal_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionDistance_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollidingActor_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionTime_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactVelocity_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionNormal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionDistance;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollidingActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImpactVelocity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWallCollisionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionPoint = { "CollisionPoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallCollisionData, CollisionPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionPoint_MetaData), NewProp_CollisionPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionNormal = { "CollisionNormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallCollisionData, CollisionNormal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionNormal_MetaData), NewProp_CollisionNormal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionDistance = { "CollisionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallCollisionData, CollisionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionDistance_MetaData), NewProp_CollisionDistance_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollidingActor = { "CollidingActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallCollisionData, CollidingActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollidingActor_MetaData), NewProp_CollidingActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionTime = { "CollisionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallCollisionData, CollisionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionTime_MetaData), NewProp_CollisionTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_ImpactVelocity = { "ImpactVelocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallCollisionData, ImpactVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactVelocity_MetaData), NewProp_ImpactVelocity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWallCollisionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionNormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollidingActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_CollisionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewProp_ImpactVelocity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallCollisionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWallCollisionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"WallCollisionData",
	Z_Construct_UScriptStruct_FWallCollisionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallCollisionData_Statics::PropPointers),
	sizeof(FWallCollisionData),
	alignof(FWallCollisionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallCollisionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWallCollisionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWallCollisionData()
{
	if (!Z_Registration_Info_UScriptStruct_FWallCollisionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FWallCollisionData.InnerSingleton, Z_Construct_UScriptStruct_FWallCollisionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FWallCollisionData.InnerSingleton;
}
// ********** End ScriptStruct FWallCollisionData **************************************************

// ********** Begin ScriptStruct FWallMaterialConfig ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FWallMaterialConfig;
class UScriptStruct* FWallMaterialConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FWallMaterialConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FWallMaterialConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWallMaterialConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("WallMaterialConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FWallMaterialConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FWallMaterialConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para configura\xc3\xa7\xc3\xa3o de material\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de material" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialType_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Material_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Durability_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReflectionCoefficient_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDestructible_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmissiveColor_MetaData[] = {
		{ "Category", "Material" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MaterialType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MaterialType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Material;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Durability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReflectionCoefficient;
	static void NewProp_bIsDestructible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDestructible;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EmissiveColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWallMaterialConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_MaterialType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_MaterialType = { "MaterialType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallMaterialConfig, MaterialType), Z_Construct_UEnum_Aura_EWallMaterial, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialType_MetaData), NewProp_MaterialType_MetaData) }; // 2951854745
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_Material = { "Material", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallMaterialConfig, Material), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Material_MetaData), NewProp_Material_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_Durability = { "Durability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallMaterialConfig, Durability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Durability_MetaData), NewProp_Durability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_ReflectionCoefficient = { "ReflectionCoefficient", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallMaterialConfig, ReflectionCoefficient), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReflectionCoefficient_MetaData), NewProp_ReflectionCoefficient_MetaData) };
void Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_bIsDestructible_SetBit(void* Obj)
{
	((FWallMaterialConfig*)Obj)->bIsDestructible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_bIsDestructible = { "bIsDestructible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWallMaterialConfig), &Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_bIsDestructible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDestructible_MetaData), NewProp_bIsDestructible_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_EmissiveColor = { "EmissiveColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallMaterialConfig, EmissiveColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmissiveColor_MetaData), NewProp_EmissiveColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_MaterialType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_MaterialType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_Material,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_Durability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_ReflectionCoefficient,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_bIsDestructible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewProp_EmissiveColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"WallMaterialConfig",
	Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::PropPointers),
	sizeof(FWallMaterialConfig),
	alignof(FWallMaterialConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWallMaterialConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FWallMaterialConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FWallMaterialConfig.InnerSingleton, Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FWallMaterialConfig.InnerSingleton;
}
// ********** End ScriptStruct FWallMaterialConfig *************************************************

// ********** Begin ScriptStruct FGateData *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FGateData;
class UScriptStruct* FGateData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FGateData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FGateData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FGateData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("GateData"));
	}
	return Z_Registration_Info_UScriptStruct_FGateData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FGateData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para dados de port\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de port\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Orientation_MetaData[] = {
		{ "Category", "Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsOpen_MetaData[] = {
		{ "Category", "Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLocked_MetaData[] = {
		{ "Category", "Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AuthorizedActors_MetaData[] = {
		{ "Category", "Gate" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Orientation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static void NewProp_bIsOpen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOpen;
	static void NewProp_bIsLocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLocked;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AuthorizedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AuthorizedActors;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FGateData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGateData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Orientation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGateData, Orientation), Z_Construct_UEnum_Aura_EWallOrientation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Orientation_MetaData), NewProp_Orientation_MetaData) }; // 3305671181
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGateData, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGateData, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
void Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsOpen_SetBit(void* Obj)
{
	((FGateData*)Obj)->bIsOpen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsOpen = { "bIsOpen", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGateData), &Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsOpen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsOpen_MetaData), NewProp_bIsOpen_MetaData) };
void Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsLocked_SetBit(void* Obj)
{
	((FGateData*)Obj)->bIsLocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsLocked = { "bIsLocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGateData), &Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsLocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLocked_MetaData), NewProp_bIsLocked_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_AuthorizedActors_Inner = { "AuthorizedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FGateData_Statics::NewProp_AuthorizedActors = { "AuthorizedActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGateData, AuthorizedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AuthorizedActors_MetaData), NewProp_AuthorizedActors_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FGateData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Orientation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsOpen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_bIsLocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_AuthorizedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGateData_Statics::NewProp_AuthorizedActors,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGateData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FGateData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"GateData",
	Z_Construct_UScriptStruct_FGateData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGateData_Statics::PropPointers),
	sizeof(FGateData),
	alignof(FGateData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGateData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FGateData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FGateData()
{
	if (!Z_Registration_Info_UScriptStruct_FGateData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FGateData.InnerSingleton, Z_Construct_UScriptStruct_FGateData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FGateData.InnerSingleton;
}
// ********** End ScriptStruct FGateData ***********************************************************

// ********** Begin ScriptStruct FPatrolArea *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPatrolArea;
class UScriptStruct* FPatrolArea::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPatrolArea.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPatrolArea.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPatrolArea, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PatrolArea"));
	}
	return Z_Registration_Info_UScriptStruct_FPatrolArea.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPatrolArea_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para \xc3\xa1rea de patrulha\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para \xc3\xa1rea de patrulha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolPoints_MetaData[] = {
		{ "Category", "Patrol" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolRadius_MetaData[] = {
		{ "Category", "Patrol" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCircular_MetaData[] = {
		{ "Category", "Patrol" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolSpeed_MetaData[] = {
		{ "Category", "Patrol" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PatrolPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolRadius;
	static void NewProp_bIsCircular_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCircular;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPatrolArea>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolPoints_Inner = { "PatrolPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolPoints = { "PatrolPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPatrolArea, PatrolPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolPoints_MetaData), NewProp_PatrolPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolRadius = { "PatrolRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPatrolArea, PatrolRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolRadius_MetaData), NewProp_PatrolRadius_MetaData) };
void Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_bIsCircular_SetBit(void* Obj)
{
	((FPatrolArea*)Obj)->bIsCircular = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_bIsCircular = { "bIsCircular", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPatrolArea), &Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_bIsCircular_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCircular_MetaData), NewProp_bIsCircular_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolSpeed = { "PatrolSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPatrolArea, PatrolSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolSpeed_MetaData), NewProp_PatrolSpeed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPatrolArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_bIsCircular,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPatrolArea_Statics::NewProp_PatrolSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPatrolArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPatrolArea_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PatrolArea",
	Z_Construct_UScriptStruct_FPatrolArea_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPatrolArea_Statics::PropPointers),
	sizeof(FPatrolArea),
	alignof(FPatrolArea),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPatrolArea_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPatrolArea_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPatrolArea()
{
	if (!Z_Registration_Info_UScriptStruct_FPatrolArea.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPatrolArea.InnerSingleton, Z_Construct_UScriptStruct_FPatrolArea_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPatrolArea.InnerSingleton;
}
// ********** End ScriptStruct FPatrolArea *********************************************************

// ********** Begin Class AWallCollisionManager Function ApplyWallMaterial *************************
struct Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics
{
	struct WallCollisionManager_eventApplyWallMaterial_Parms
	{
		UStaticMeshComponent* WallMesh;
		FWallMaterialConfig InMaterialConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallMesh_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InMaterialConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WallMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InMaterialConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::NewProp_WallMesh = { "WallMesh", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventApplyWallMaterial_Parms, WallMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallMesh_MetaData), NewProp_WallMesh_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::NewProp_InMaterialConfig = { "InMaterialConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventApplyWallMaterial_Parms, InMaterialConfig), Z_Construct_UScriptStruct_FWallMaterialConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InMaterialConfig_MetaData), NewProp_InMaterialConfig_MetaData) }; // 3033661663
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::NewProp_WallMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::NewProp_InMaterialConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "ApplyWallMaterial", Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::WallCollisionManager_eventApplyWallMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::WallCollisionManager_eventApplyWallMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execApplyWallMaterial)
{
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_WallMesh);
	P_GET_STRUCT_REF(FWallMaterialConfig,Z_Param_Out_InMaterialConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyWallMaterial(Z_Param_WallMesh,Z_Param_Out_InMaterialConfig);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function ApplyWallMaterial ***************************

// ********** Begin Class AWallCollisionManager Function AuthorizeActorForGate *********************
struct Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics
{
	struct WallCollisionManager_eventAuthorizeActorForGate_Parms
	{
		AActor* Actor;
		int32 GateIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gates" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GateIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventAuthorizeActorForGate_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::NewProp_GateIndex = { "GateIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventAuthorizeActorForGate_Parms, GateIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::NewProp_GateIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "AuthorizeActorForGate", Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::WallCollisionManager_eventAuthorizeActorForGate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::WallCollisionManager_eventAuthorizeActorForGate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execAuthorizeActorForGate)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_PROPERTY(FIntProperty,Z_Param_GateIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AuthorizeActorForGate(Z_Param_Actor,Z_Param_GateIndex);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function AuthorizeActorForGate ***********************

// ********** Begin Class AWallCollisionManager Function CalculateAngleBetweenVectors **************
struct Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics
{
	struct WallCollisionManager_eventCalculateAngleBetweenVectors_Parms
	{
		FVector Vector1;
		FVector Vector2;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utils" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vector1_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vector2_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vector1;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vector2;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::NewProp_Vector1 = { "Vector1", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateAngleBetweenVectors_Parms, Vector1), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vector1_MetaData), NewProp_Vector1_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::NewProp_Vector2 = { "Vector2", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateAngleBetweenVectors_Parms, Vector2), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vector2_MetaData), NewProp_Vector2_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateAngleBetweenVectors_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::NewProp_Vector1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::NewProp_Vector2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CalculateAngleBetweenVectors", Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::WallCollisionManager_eventCalculateAngleBetweenVectors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::WallCollisionManager_eventCalculateAngleBetweenVectors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCalculateAngleBetweenVectors)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Vector1);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Vector2);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateAngleBetweenVectors(Z_Param_Out_Vector1,Z_Param_Out_Vector2);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CalculateAngleBetweenVectors ****************

// ********** Begin Class AWallCollisionManager Function CalculateWallBoundaries *******************
struct Z_Construct_UFunction_AWallCollisionManager_CalculateWallBoundaries_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de geometria das paredes\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de geometria das paredes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallBoundaries_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CalculateWallBoundaries", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateWallBoundaries_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CalculateWallBoundaries_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_CalculateWallBoundaries()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CalculateWallBoundaries_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCalculateWallBoundaries)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CalculateWallBoundaries();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CalculateWallBoundaries *********************

// ********** Begin Class AWallCollisionManager Function CalculateWallLength ***********************
struct Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics
{
	struct WallCollisionManager_eventCalculateWallLength_Parms
	{
		FWallSection WallSection;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallSection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WallSection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::NewProp_WallSection = { "WallSection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateWallLength_Parms, WallSection), Z_Construct_UScriptStruct_FWallSection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallSection_MetaData), NewProp_WallSection_MetaData) }; // 3342107562
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateWallLength_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::NewProp_WallSection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CalculateWallLength", Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::WallCollisionManager_eventCalculateWallLength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::WallCollisionManager_eventCalculateWallLength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCalculateWallLength)
{
	P_GET_STRUCT_REF(FWallSection,Z_Param_Out_WallSection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateWallLength(Z_Param_Out_WallSection);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CalculateWallLength *************************

// ********** Begin Class AWallCollisionManager Function CalculateWallNormal ***********************
struct Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics
{
	struct WallCollisionManager_eventCalculateWallNormal_Parms
	{
		FVector StartPos;
		FVector EndPos;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateWallNormal_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateWallNormal_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCalculateWallNormal_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CalculateWallNormal", Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::WallCollisionManager_eventCalculateWallNormal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::WallCollisionManager_eventCalculateWallNormal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCalculateWallNormal)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateWallNormal(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CalculateWallNormal *************************

// ********** Begin Class AWallCollisionManager Function CanActorPassThroughGate *******************
struct Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics
{
	struct WallCollisionManager_eventCanActorPassThroughGate_Parms
	{
		AActor* Actor;
		int32 GateIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gates" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GateIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCanActorPassThroughGate_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_GateIndex = { "GateIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCanActorPassThroughGate_Parms, GateIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventCanActorPassThroughGate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventCanActorPassThroughGate_Parms), &Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_GateIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CanActorPassThroughGate", Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::WallCollisionManager_eventCanActorPassThroughGate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::WallCollisionManager_eventCanActorPassThroughGate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCanActorPassThroughGate)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_PROPERTY(FIntProperty,Z_Param_GateIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanActorPassThroughGate(Z_Param_Actor,Z_Param_GateIndex);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CanActorPassThroughGate *********************

// ********** Begin Class AWallCollisionManager Function CheckCollisionWithWalls *******************
struct Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics
{
	struct WallCollisionManager_eventCheckCollisionWithWalls_Parms
	{
		FVector StartPos;
		FVector EndPos;
		FWallCollisionData OutCollisionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de colis\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de colis\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutCollisionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCheckCollisionWithWalls_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCheckCollisionWithWalls_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_OutCollisionData = { "OutCollisionData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCheckCollisionWithWalls_Parms, OutCollisionData), Z_Construct_UScriptStruct_FWallCollisionData, METADATA_PARAMS(0, nullptr) }; // 2147688072
void Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventCheckCollisionWithWalls_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventCheckCollisionWithWalls_Parms), &Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_OutCollisionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CheckCollisionWithWalls", Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::WallCollisionManager_eventCheckCollisionWithWalls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::WallCollisionManager_eventCheckCollisionWithWalls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCheckCollisionWithWalls)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_GET_STRUCT_REF(FWallCollisionData,Z_Param_Out_OutCollisionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckCollisionWithWalls(Z_Param_Out_StartPos,Z_Param_Out_EndPos,Z_Param_Out_OutCollisionData);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CheckCollisionWithWalls *********************

// ********** Begin Class AWallCollisionManager Function CloseGate *********************************
struct Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics
{
	struct WallCollisionManager_eventCloseGate_Parms
	{
		int32 GateIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gates" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GateIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::NewProp_GateIndex = { "GateIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCloseGate_Parms, GateIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::NewProp_GateIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CloseGate", Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::WallCollisionManager_eventCloseGate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::WallCollisionManager_eventCloseGate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_CloseGate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CloseGate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCloseGate)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GateIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CloseGate(Z_Param_GateIndex);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CloseGate ***********************************

// ********** Begin Class AWallCollisionManager Function CreateGates *******************************
struct Z_Construct_UFunction_AWallCollisionManager_CreateGates_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de port\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de port\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CreateGates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CreateGates", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CreateGates_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CreateGates_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_CreateGates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CreateGates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCreateGates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateGates();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CreateGates *********************************

// ********** Begin Class AWallCollisionManager Function CreateWallMeshes **************************
struct Z_Construct_UFunction_AWallCollisionManager_CreateWallMeshes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de cria\xc3\xa7\xc3\xa3o de meshes\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de cria\xc3\xa7\xc3\xa3o de meshes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CreateWallMeshes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CreateWallMeshes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CreateWallMeshes_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CreateWallMeshes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_CreateWallMeshes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CreateWallMeshes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCreateWallMeshes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateWallMeshes();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CreateWallMeshes ****************************

// ********** Begin Class AWallCollisionManager Function CreateWallSegment *************************
struct Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics
{
	struct WallCollisionManager_eventCreateWallSegment_Parms
	{
		FWallSection WallSection;
		int32 SegmentIndex;
		UStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallSection_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WallSection;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SegmentIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::NewProp_WallSection = { "WallSection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCreateWallSegment_Parms, WallSection), Z_Construct_UScriptStruct_FWallSection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallSection_MetaData), NewProp_WallSection_MetaData) }; // 3342107562
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::NewProp_SegmentIndex = { "SegmentIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCreateWallSegment_Parms, SegmentIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventCreateWallSegment_Parms, ReturnValue), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::NewProp_WallSection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::NewProp_SegmentIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "CreateWallSegment", Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::WallCollisionManager_eventCreateWallSegment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::WallCollisionManager_eventCreateWallSegment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execCreateWallSegment)
{
	P_GET_STRUCT_REF(FWallSection,Z_Param_Out_WallSection);
	P_GET_PROPERTY(FIntProperty,Z_Param_SegmentIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UStaticMeshComponent**)Z_Param__Result=P_THIS->CreateWallSegment(Z_Param_Out_WallSection,Z_Param_SegmentIndex);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function CreateWallSegment ***************************

// ********** Begin Class AWallCollisionManager Function DistancePointToLine ***********************
struct Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics
{
	struct WallCollisionManager_eventDistancePointToLine_Parms
	{
		FVector Point;
		FVector LineStart;
		FVector LineEnd;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utils" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineStart_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineEnd_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineStart;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineEnd;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventDistancePointToLine_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_LineStart = { "LineStart", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventDistancePointToLine_Parms, LineStart), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineStart_MetaData), NewProp_LineStart_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_LineEnd = { "LineEnd", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventDistancePointToLine_Parms, LineEnd), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineEnd_MetaData), NewProp_LineEnd_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventDistancePointToLine_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_LineStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_LineEnd,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "DistancePointToLine", Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::WallCollisionManager_eventDistancePointToLine_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::WallCollisionManager_eventDistancePointToLine_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execDistancePointToLine)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineStart);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineEnd);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->DistancePointToLine(Z_Param_Out_Point,Z_Param_Out_LineStart,Z_Param_Out_LineEnd);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function DistancePointToLine *************************

// ********** Begin Class AWallCollisionManager Function DrawDebugCollisions ***********************
struct Z_Construct_UFunction_AWallCollisionManager_DrawDebugCollisions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_DrawDebugCollisions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "DrawDebugCollisions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_DrawDebugCollisions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_DrawDebugCollisions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_DrawDebugCollisions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_DrawDebugCollisions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execDrawDebugCollisions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugCollisions();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function DrawDebugCollisions *************************

// ********** Begin Class AWallCollisionManager Function DrawDebugGates ****************************
struct Z_Construct_UFunction_AWallCollisionManager_DrawDebugGates_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_DrawDebugGates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "DrawDebugGates", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_DrawDebugGates_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_DrawDebugGates_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_DrawDebugGates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_DrawDebugGates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execDrawDebugGates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugGates();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function DrawDebugGates ******************************

// ********** Begin Class AWallCollisionManager Function DrawDebugPatrolPath ***********************
struct Z_Construct_UFunction_AWallCollisionManager_DrawDebugPatrolPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_DrawDebugPatrolPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "DrawDebugPatrolPath", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_DrawDebugPatrolPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_DrawDebugPatrolPath_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_DrawDebugPatrolPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_DrawDebugPatrolPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execDrawDebugPatrolPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugPatrolPath();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function DrawDebugPatrolPath *************************

// ********** Begin Class AWallCollisionManager Function DrawDebugWalls ****************************
struct Z_Construct_UFunction_AWallCollisionManager_DrawDebugWalls_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_DrawDebugWalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "DrawDebugWalls", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_DrawDebugWalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_DrawDebugWalls_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_DrawDebugWalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_DrawDebugWalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execDrawDebugWalls)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugWalls();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function DrawDebugWalls ******************************

// ********** Begin Class AWallCollisionManager Function FindPathAroundWalls ***********************
struct Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics
{
	struct WallCollisionManager_eventFindPathAroundWalls_Parms
	{
		FVector StartPos;
		FVector EndPos;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de pathfinding e navega\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de pathfinding e navega\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventFindPathAroundWalls_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventFindPathAroundWalls_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventFindPathAroundWalls_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "FindPathAroundWalls", Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::WallCollisionManager_eventFindPathAroundWalls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::WallCollisionManager_eventFindPathAroundWalls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execFindPathAroundWalls)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindPathAroundWalls(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function FindPathAroundWalls *************************

// ********** Begin Class AWallCollisionManager Function GenerateCornerWallPoints ******************
struct Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics
{
	struct WallCollisionManager_eventGenerateCornerWallPoints_Parms
	{
		FVector CornerPos;
		EWallOrientation Orientation;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CornerPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CornerPos;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Orientation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_CornerPos = { "CornerPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateCornerWallPoints_Parms, CornerPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CornerPos_MetaData), NewProp_CornerPos_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_Orientation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateCornerWallPoints_Parms, Orientation), Z_Construct_UEnum_Aura_EWallOrientation, METADATA_PARAMS(0, nullptr) }; // 3305671181
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateCornerWallPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_CornerPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_Orientation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GenerateCornerWallPoints", Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::WallCollisionManager_eventGenerateCornerWallPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::WallCollisionManager_eventGenerateCornerWallPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGenerateCornerWallPoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CornerPos);
	P_GET_ENUM(EWallOrientation,Z_Param_Orientation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateCornerWallPoints(Z_Param_Out_CornerPos,EWallOrientation(Z_Param_Orientation));
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GenerateCornerWallPoints ********************

// ********** Begin Class AWallCollisionManager Function GenerateCurvedWallPoints ******************
struct Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics
{
	struct WallCollisionManager_eventGenerateCurvedWallPoints_Parms
	{
		FVector StartPos;
		FVector EndPos;
		float CurveRadius;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurveRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateCurvedWallPoints_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateCurvedWallPoints_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_CurveRadius = { "CurveRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateCurvedWallPoints_Parms, CurveRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateCurvedWallPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_CurveRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GenerateCurvedWallPoints", Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::WallCollisionManager_eventGenerateCurvedWallPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::WallCollisionManager_eventGenerateCurvedWallPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGenerateCurvedWallPoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurveRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateCurvedWallPoints(Z_Param_Out_StartPos,Z_Param_Out_EndPos,Z_Param_CurveRadius);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GenerateCurvedWallPoints ********************

// ********** Begin Class AWallCollisionManager Function GeneratePatrolPath ************************
struct Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics
{
	struct WallCollisionManager_eventGeneratePatrolPath_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGeneratePatrolPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GeneratePatrolPath", Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::WallCollisionManager_eventGeneratePatrolPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::WallCollisionManager_eventGeneratePatrolPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGeneratePatrolPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GeneratePatrolPath();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GeneratePatrolPath **************************

// ********** Begin Class AWallCollisionManager Function GenerateStraightWallPoints ****************
struct Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics
{
	struct WallCollisionManager_eventGenerateStraightWallPoints_Parms
	{
		FVector StartPos;
		FVector EndPos;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateStraightWallPoints_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateStraightWallPoints_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGenerateStraightWallPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GenerateStraightWallPoints", Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::WallCollisionManager_eventGenerateStraightWallPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::WallCollisionManager_eventGenerateStraightWallPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGenerateStraightWallPoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateStraightWallPoints(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GenerateStraightWallPoints ******************

// ********** Begin Class AWallCollisionManager Function GenerateWallGeometry **********************
struct Z_Construct_UFunction_AWallCollisionManager_GenerateWallGeometry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wall System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GenerateWallGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GenerateWallGeometry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GenerateWallGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GenerateWallGeometry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_GenerateWallGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GenerateWallGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGenerateWallGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateWallGeometry();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GenerateWallGeometry ************************

// ********** Begin Class AWallCollisionManager Function GetClosestPointOnWalls ********************
struct Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics
{
	struct WallCollisionManager_eventGetClosestPointOnWalls_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetClosestPointOnWalls_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetClosestPointOnWalls_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetClosestPointOnWalls", Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::WallCollisionManager_eventGetClosestPointOnWalls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::WallCollisionManager_eventGetClosestPointOnWalls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetClosestPointOnWalls)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetClosestPointOnWalls(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetClosestPointOnWalls **********************

// ********** Begin Class AWallCollisionManager Function GetDistanceToNearestWall ******************
struct Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics
{
	struct WallCollisionManager_eventGetDistanceToNearestWall_Parms
	{
		FVector Position;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetDistanceToNearestWall_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetDistanceToNearestWall_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetDistanceToNearestWall", Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::WallCollisionManager_eventGetDistanceToNearestWall_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::WallCollisionManager_eventGetDistanceToNearestWall_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetDistanceToNearestWall)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDistanceToNearestWall(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetDistanceToNearestWall ********************

// ********** Begin Class AWallCollisionManager Function GetGates **********************************
struct Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics
{
	struct WallCollisionManager_eventGetGates_Parms
	{
		TArray<FGateData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGateData, METADATA_PARAMS(0, nullptr) }; // 2506052715
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetGates_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2506052715
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetGates", Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::WallCollisionManager_eventGetGates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::WallCollisionManager_eventGetGates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetGates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetGates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetGates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FGateData>*)Z_Param__Result=P_THIS->GetGates();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetGates ************************************

// ********** Begin Class AWallCollisionManager Function GetMapArea ********************************
struct Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics
{
	struct WallCollisionManager_eventGetMapArea_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetMapArea_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetMapArea", Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::WallCollisionManager_eventGetMapArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::WallCollisionManager_eventGetMapArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetMapArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetMapArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetMapArea)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMapArea();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetMapArea **********************************

// ********** Begin Class AWallCollisionManager Function GetMapCenter ******************************
struct Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics
{
	struct WallCollisionManager_eventGetMapCenter_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetMapCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetMapCenter", Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::WallCollisionManager_eventGetMapCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::WallCollisionManager_eventGetMapCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetMapCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetMapCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetMapCenter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetMapCenter();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetMapCenter ********************************

// ********** Begin Class AWallCollisionManager Function GetMaterialConfig *************************
struct Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics
{
	struct WallCollisionManager_eventGetMaterialConfig_Parms
	{
		FWallMaterialConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetMaterialConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FWallMaterialConfig, METADATA_PARAMS(0, nullptr) }; // 3033661663
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetMaterialConfig", Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::WallCollisionManager_eventGetMaterialConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::WallCollisionManager_eventGetMaterialConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetMaterialConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FWallMaterialConfig*)Z_Param__Result=P_THIS->GetMaterialConfig();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetMaterialConfig ***************************

// ********** Begin Class AWallCollisionManager Function GetNearestGatePosition ********************
struct Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics
{
	struct WallCollisionManager_eventGetNearestGatePosition_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gates" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetNearestGatePosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetNearestGatePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetNearestGatePosition", Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::WallCollisionManager_eventGetNearestGatePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::WallCollisionManager_eventGetNearestGatePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetNearestGatePosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetNearestGatePosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetNearestGatePosition **********************

// ********** Begin Class AWallCollisionManager Function GetSafePositionNearWalls ******************
struct Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics
{
	struct WallCollisionManager_eventGetSafePositionNearWalls_Parms
	{
		FVector DesiredPosition;
		float SafeDistance;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesiredPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DesiredPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SafeDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::NewProp_DesiredPosition = { "DesiredPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetSafePositionNearWalls_Parms, DesiredPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesiredPosition_MetaData), NewProp_DesiredPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::NewProp_SafeDistance = { "SafeDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetSafePositionNearWalls_Parms, SafeDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetSafePositionNearWalls_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::NewProp_DesiredPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::NewProp_SafeDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetSafePositionNearWalls", Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::WallCollisionManager_eventGetSafePositionNearWalls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::WallCollisionManager_eventGetSafePositionNearWalls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetSafePositionNearWalls)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DesiredPosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SafeDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetSafePositionNearWalls(Z_Param_Out_DesiredPosition,Z_Param_SafeDistance);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetSafePositionNearWalls ********************

// ********** Begin Class AWallCollisionManager Function GetTotalWallLength ************************
struct Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics
{
	struct WallCollisionManager_eventGetTotalWallLength_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetTotalWallLength_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetTotalWallLength", Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::WallCollisionManager_eventGetTotalWallLength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::WallCollisionManager_eventGetTotalWallLength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetTotalWallLength)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalWallLength();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetTotalWallLength **************************

// ********** Begin Class AWallCollisionManager Function GetWallCenter *****************************
struct Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics
{
	struct WallCollisionManager_eventGetWallCenter_Parms
	{
		FWallSection WallSection;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallSection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WallSection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::NewProp_WallSection = { "WallSection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetWallCenter_Parms, WallSection), Z_Construct_UScriptStruct_FWallSection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallSection_MetaData), NewProp_WallSection_MetaData) }; // 3342107562
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetWallCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::NewProp_WallSection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetWallCenter", Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::WallCollisionManager_eventGetWallCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::WallCollisionManager_eventGetWallCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetWallCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetWallCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetWallCenter)
{
	P_GET_STRUCT_REF(FWallSection,Z_Param_Out_WallSection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetWallCenter(Z_Param_Out_WallSection);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetWallCenter *******************************

// ********** Begin Class AWallCollisionManager Function GetWallIntersectionPoints *****************
struct Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics
{
	struct WallCollisionManager_eventGetWallIntersectionPoints_Parms
	{
		FVector LineStart;
		FVector LineEnd;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineStart_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineEnd_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineStart;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineEnd;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_LineStart = { "LineStart", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetWallIntersectionPoints_Parms, LineStart), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineStart_MetaData), NewProp_LineStart_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_LineEnd = { "LineEnd", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetWallIntersectionPoints_Parms, LineEnd), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineEnd_MetaData), NewProp_LineEnd_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetWallIntersectionPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_LineStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_LineEnd,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetWallIntersectionPoints", Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::WallCollisionManager_eventGetWallIntersectionPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::WallCollisionManager_eventGetWallIntersectionPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetWallIntersectionPoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineStart);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineEnd);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetWallIntersectionPoints(Z_Param_Out_LineStart,Z_Param_Out_LineEnd);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetWallIntersectionPoints *******************

// ********** Begin Class AWallCollisionManager Function GetWallPerimeter **************************
struct Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics
{
	struct WallCollisionManager_eventGetWallPerimeter_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetWallPerimeter_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetWallPerimeter", Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::WallCollisionManager_eventGetWallPerimeter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::WallCollisionManager_eventGetWallPerimeter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetWallPerimeter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetWallPerimeter();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetWallPerimeter ****************************

// ********** Begin Class AWallCollisionManager Function GetWallSections ***************************
struct Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics
{
	struct WallCollisionManager_eventGetWallSections_Parms
	{
		TArray<FWallSection> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Getters e Setters\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Getters e Setters" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FWallSection, METADATA_PARAMS(0, nullptr) }; // 3342107562
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventGetWallSections_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3342107562
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "GetWallSections", Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::WallCollisionManager_eventGetWallSections_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::WallCollisionManager_eventGetWallSections_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_GetWallSections()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_GetWallSections_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execGetWallSections)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FWallSection>*)Z_Param__Result=P_THIS->GetWallSections();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function GetWallSections *****************************

// ********** Begin Class AWallCollisionManager Function InitializeWallSystem **********************
struct Z_Construct_UFunction_AWallCollisionManager_InitializeWallSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wall System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es principais de inicializa\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es principais de inicializa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_InitializeWallSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "InitializeWallSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_InitializeWallSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_InitializeWallSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_InitializeWallSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_InitializeWallSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execInitializeWallSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWallSystem();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function InitializeWallSystem ************************

// ********** Begin Class AWallCollisionManager Function IsGateOpen ********************************
struct Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics
{
	struct WallCollisionManager_eventIsGateOpen_Parms
	{
		int32 GateIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gates" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GateIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::NewProp_GateIndex = { "GateIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventIsGateOpen_Parms, GateIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventIsGateOpen_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventIsGateOpen_Parms), &Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::NewProp_GateIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "IsGateOpen", Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::WallCollisionManager_eventIsGateOpen_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::WallCollisionManager_eventIsGateOpen_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_IsGateOpen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_IsGateOpen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execIsGateOpen)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GateIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGateOpen(Z_Param_GateIndex);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function IsGateOpen **********************************

// ********** Begin Class AWallCollisionManager Function IsPathClearOfWalls ************************
struct Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics
{
	struct WallCollisionManager_eventIsPathClearOfWalls_Parms
	{
		FVector StartPos;
		FVector EndPos;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventIsPathClearOfWalls_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventIsPathClearOfWalls_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
void Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventIsPathClearOfWalls_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventIsPathClearOfWalls_Parms), &Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "IsPathClearOfWalls", Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::WallCollisionManager_eventIsPathClearOfWalls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::WallCollisionManager_eventIsPathClearOfWalls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execIsPathClearOfWalls)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPathClearOfWalls(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function IsPathClearOfWalls **************************

// ********** Begin Class AWallCollisionManager Function IsPointInsidePolygon **********************
struct Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics
{
	struct WallCollisionManager_eventIsPointInsidePolygon_Parms
	{
		FVector Point;
		TArray<FVector> PolygonVertices;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utils" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PolygonVertices_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PolygonVertices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PolygonVertices;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventIsPointInsidePolygon_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_PolygonVertices_Inner = { "PolygonVertices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_PolygonVertices = { "PolygonVertices", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventIsPointInsidePolygon_Parms, PolygonVertices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PolygonVertices_MetaData), NewProp_PolygonVertices_MetaData) };
void Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventIsPointInsidePolygon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventIsPointInsidePolygon_Parms), &Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_PolygonVertices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_PolygonVertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "IsPointInsidePolygon", Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::WallCollisionManager_eventIsPointInsidePolygon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::WallCollisionManager_eventIsPointInsidePolygon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execIsPointInsidePolygon)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_PolygonVertices);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPointInsidePolygon(Z_Param_Out_Point,Z_Param_Out_PolygonVertices);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function IsPointInsidePolygon ************************

// ********** Begin Class AWallCollisionManager Function IsPositionInsideWalls *********************
struct Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics
{
	struct WallCollisionManager_eventIsPositionInsideWalls_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventIsPositionInsideWalls_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventIsPositionInsideWalls_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventIsPositionInsideWalls_Parms), &Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "IsPositionInsideWalls", Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::WallCollisionManager_eventIsPositionInsideWalls_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::WallCollisionManager_eventIsPositionInsideWalls_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execIsPositionInsideWalls)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInsideWalls(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function IsPositionInsideWalls ***********************

// ********** Begin Class AWallCollisionManager Function LineIntersectsWall ************************
struct Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics
{
	struct WallCollisionManager_eventLineIntersectsWall_Parms
	{
		FVector LineStart;
		FVector LineEnd;
		FWallSection WallSection;
		FVector OutIntersectionPoint;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineStart_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineEnd_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallSection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineStart;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineEnd;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WallSection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutIntersectionPoint;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_LineStart = { "LineStart", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventLineIntersectsWall_Parms, LineStart), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineStart_MetaData), NewProp_LineStart_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_LineEnd = { "LineEnd", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventLineIntersectsWall_Parms, LineEnd), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineEnd_MetaData), NewProp_LineEnd_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_WallSection = { "WallSection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventLineIntersectsWall_Parms, WallSection), Z_Construct_UScriptStruct_FWallSection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallSection_MetaData), NewProp_WallSection_MetaData) }; // 3342107562
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_OutIntersectionPoint = { "OutIntersectionPoint", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventLineIntersectsWall_Parms, OutIntersectionPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventLineIntersectsWall_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventLineIntersectsWall_Parms), &Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_LineStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_LineEnd,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_WallSection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_OutIntersectionPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "LineIntersectsWall", Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::WallCollisionManager_eventLineIntersectsWall_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::WallCollisionManager_eventLineIntersectsWall_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execLineIntersectsWall)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineStart);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineEnd);
	P_GET_STRUCT_REF(FWallSection,Z_Param_Out_WallSection);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_OutIntersectionPoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LineIntersectsWall(Z_Param_Out_LineStart,Z_Param_Out_LineEnd,Z_Param_Out_WallSection,Z_Param_Out_OutIntersectionPoint);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function LineIntersectsWall **************************

// ********** Begin Class AWallCollisionManager Function OnActorBeginOverlap ***********************
struct Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics
{
	struct WallCollisionManager_eventOnActorBeginOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComponent;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorBeginOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorBeginOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OtherComponent = { "OtherComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorBeginOverlap_Parms, OtherComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComponent_MetaData), NewProp_OtherComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorBeginOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((WallCollisionManager_eventOnActorBeginOverlap_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventOnActorBeginOverlap_Parms), &Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorBeginOverlap_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OtherComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "OnActorBeginOverlap", Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::WallCollisionManager_eventOnActorBeginOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::WallCollisionManager_eventOnActorBeginOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execOnActorBeginOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorBeginOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComponent,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function OnActorBeginOverlap *************************

// ********** Begin Class AWallCollisionManager Function OnActorEndOverlap *************************
struct Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics
{
	struct WallCollisionManager_eventOnActorEndOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComponent;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorEndOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorEndOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OtherComponent = { "OtherComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorEndOverlap_Parms, OtherComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComponent_MetaData), NewProp_OtherComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnActorEndOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OtherComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "OnActorEndOverlap", Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::WallCollisionManager_eventOnActorEndOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::WallCollisionManager_eventOnActorEndOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execOnActorEndOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorEndOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComponent,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function OnActorEndOverlap ***************************

// ********** Begin Class AWallCollisionManager Function OnWallHit *********************************
struct Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics
{
	struct WallCollisionManager_eventOnWallHit_Parms
	{
		UPrimitiveComponent* HitComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComponent;
		FVector NormalImpulse;
		FHitResult Hit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de colis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de colis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Hit_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HitComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NormalImpulse;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Hit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_HitComponent = { "HitComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnWallHit_Parms, HitComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitComponent_MetaData), NewProp_HitComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnWallHit_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_OtherComponent = { "OtherComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnWallHit_Parms, OtherComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComponent_MetaData), NewProp_OtherComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_NormalImpulse = { "NormalImpulse", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnWallHit_Parms, NormalImpulse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_Hit = { "Hit", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOnWallHit_Parms, Hit), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Hit_MetaData), NewProp_Hit_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_HitComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_OtherComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_NormalImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::NewProp_Hit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "OnWallHit", Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::WallCollisionManager_eventOnWallHit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::WallCollisionManager_eventOnWallHit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_OnWallHit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_OnWallHit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execOnWallHit)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_HitComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComponent);
	P_GET_STRUCT(FVector,Z_Param_NormalImpulse);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_Hit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnWallHit(Z_Param_HitComponent,Z_Param_OtherActor,Z_Param_OtherComponent,Z_Param_NormalImpulse,Z_Param_Out_Hit);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function OnWallHit ***********************************

// ********** Begin Class AWallCollisionManager Function OpenGate **********************************
struct Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics
{
	struct WallCollisionManager_eventOpenGate_Parms
	{
		int32 GateIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gates" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GateIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::NewProp_GateIndex = { "GateIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventOpenGate_Parms, GateIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::NewProp_GateIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "OpenGate", Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::WallCollisionManager_eventOpenGate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::WallCollisionManager_eventOpenGate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_OpenGate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_OpenGate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execOpenGate)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GateIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OpenGate(Z_Param_GateIndex);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function OpenGate ************************************

// ********** Begin Class AWallCollisionManager Function ProjectPointOntoLine **********************
struct Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics
{
	struct WallCollisionManager_eventProjectPointOntoLine_Parms
	{
		FVector Point;
		FVector LineStart;
		FVector LineEnd;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utils" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineStart_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineEnd_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineStart;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LineEnd;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventProjectPointOntoLine_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_LineStart = { "LineStart", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventProjectPointOntoLine_Parms, LineStart), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineStart_MetaData), NewProp_LineStart_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_LineEnd = { "LineEnd", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventProjectPointOntoLine_Parms, LineEnd), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineEnd_MetaData), NewProp_LineEnd_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventProjectPointOntoLine_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_LineStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_LineEnd,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "ProjectPointOntoLine", Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::WallCollisionManager_eventProjectPointOntoLine_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::WallCollisionManager_eventProjectPointOntoLine_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execProjectPointOntoLine)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineStart);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_LineEnd);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->ProjectPointOntoLine(Z_Param_Out_Point,Z_Param_Out_LineStart,Z_Param_Out_LineEnd);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function ProjectPointOntoLine ************************

// ********** Begin Class AWallCollisionManager Function RotatePointAroundCenter *******************
struct Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics
{
	struct WallCollisionManager_eventRotatePointAroundCenter_Parms
	{
		FVector Point;
		FVector Center;
		float AngleDegrees;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utils" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de utilidade matem\xc3\xa1tica\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de utilidade matem\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngleDegrees;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventRotatePointAroundCenter_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventRotatePointAroundCenter_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_AngleDegrees = { "AngleDegrees", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventRotatePointAroundCenter_Parms, AngleDegrees), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventRotatePointAroundCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_AngleDegrees,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "RotatePointAroundCenter", Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::WallCollisionManager_eventRotatePointAroundCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::WallCollisionManager_eventRotatePointAroundCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execRotatePointAroundCenter)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AngleDegrees);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->RotatePointAroundCenter(Z_Param_Out_Point,Z_Param_Out_Center,Z_Param_AngleDegrees);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function RotatePointAroundCenter *********************

// ********** Begin Class AWallCollisionManager Function SetupCollisionSystem **********************
struct Z_Construct_UFunction_AWallCollisionManager_SetupCollisionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wall System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_SetupCollisionSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "SetupCollisionSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetupCollisionSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_SetupCollisionSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_SetupCollisionSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_SetupCollisionSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execSetupCollisionSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupCollisionSystem();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function SetupCollisionSystem ************************

// ********** Begin Class AWallCollisionManager Function SetWallHeight *****************************
struct Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics
{
	struct WallCollisionManager_eventSetWallHeight_Parms
	{
		float NewHeight;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewHeight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::NewProp_NewHeight = { "NewHeight", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventSetWallHeight_Parms, NewHeight), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::NewProp_NewHeight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "SetWallHeight", Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::WallCollisionManager_eventSetWallHeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::WallCollisionManager_eventSetWallHeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_SetWallHeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_SetWallHeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execSetWallHeight)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewHeight);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWallHeight(Z_Param_NewHeight);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function SetWallHeight *******************************

// ********** Begin Class AWallCollisionManager Function SetWallMaterial ***************************
struct Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics
{
	struct WallCollisionManager_eventSetWallMaterial_Parms
	{
		FWallMaterialConfig NewMaterialConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewMaterialConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewMaterialConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::NewProp_NewMaterialConfig = { "NewMaterialConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventSetWallMaterial_Parms, NewMaterialConfig), Z_Construct_UScriptStruct_FWallMaterialConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewMaterialConfig_MetaData), NewProp_NewMaterialConfig_MetaData) }; // 3033661663
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::NewProp_NewMaterialConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "SetWallMaterial", Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::WallCollisionManager_eventSetWallMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::WallCollisionManager_eventSetWallMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execSetWallMaterial)
{
	P_GET_STRUCT_REF(FWallMaterialConfig,Z_Param_Out_NewMaterialConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWallMaterial(Z_Param_Out_NewMaterialConfig);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function SetWallMaterial *****************************

// ********** Begin Class AWallCollisionManager Function SetWallThickness **************************
struct Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics
{
	struct WallCollisionManager_eventSetWallThickness_Parms
	{
		float NewThickness;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewThickness;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::NewProp_NewThickness = { "NewThickness", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WallCollisionManager_eventSetWallThickness_Parms, NewThickness), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::NewProp_NewThickness,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "SetWallThickness", Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::WallCollisionManager_eventSetWallThickness_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::WallCollisionManager_eventSetWallThickness_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_SetWallThickness()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_SetWallThickness_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execSetWallThickness)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewThickness);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWallThickness(Z_Param_NewThickness);
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function SetWallThickness ****************************

// ********** Begin Class AWallCollisionManager Function UpdateWallMeshes **************************
struct Z_Construct_UFunction_AWallCollisionManager_UpdateWallMeshes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Mesh Generation" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_UpdateWallMeshes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "UpdateWallMeshes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_UpdateWallMeshes_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_UpdateWallMeshes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AWallCollisionManager_UpdateWallMeshes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_UpdateWallMeshes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execUpdateWallMeshes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateWallMeshes();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function UpdateWallMeshes ****************************

// ********** Begin Class AWallCollisionManager Function ValidateCollisionSetup ********************
struct Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics
{
	struct WallCollisionManager_eventValidateCollisionSetup_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventValidateCollisionSetup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventValidateCollisionSetup_Parms), &Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "ValidateCollisionSetup", Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::WallCollisionManager_eventValidateCollisionSetup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::WallCollisionManager_eventValidateCollisionSetup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execValidateCollisionSetup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateCollisionSetup();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function ValidateCollisionSetup **********************

// ********** Begin Class AWallCollisionManager Function ValidateWallGeometry **********************
struct Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics
{
	struct WallCollisionManager_eventValidateWallGeometry_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WallCollisionManager_eventValidateWallGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WallCollisionManager_eventValidateWallGeometry_Parms), &Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AWallCollisionManager, nullptr, "ValidateWallGeometry", Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::WallCollisionManager_eventValidateWallGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::WallCollisionManager_eventValidateWallGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AWallCollisionManager::execValidateWallGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateWallGeometry();
	P_NATIVE_END;
}
// ********** End Class AWallCollisionManager Function ValidateWallGeometry ************************

// ********** Begin Class AWallCollisionManager ****************************************************
void AWallCollisionManager::StaticRegisterNativesAWallCollisionManager()
{
	UClass* Class = AWallCollisionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyWallMaterial", &AWallCollisionManager::execApplyWallMaterial },
		{ "AuthorizeActorForGate", &AWallCollisionManager::execAuthorizeActorForGate },
		{ "CalculateAngleBetweenVectors", &AWallCollisionManager::execCalculateAngleBetweenVectors },
		{ "CalculateWallBoundaries", &AWallCollisionManager::execCalculateWallBoundaries },
		{ "CalculateWallLength", &AWallCollisionManager::execCalculateWallLength },
		{ "CalculateWallNormal", &AWallCollisionManager::execCalculateWallNormal },
		{ "CanActorPassThroughGate", &AWallCollisionManager::execCanActorPassThroughGate },
		{ "CheckCollisionWithWalls", &AWallCollisionManager::execCheckCollisionWithWalls },
		{ "CloseGate", &AWallCollisionManager::execCloseGate },
		{ "CreateGates", &AWallCollisionManager::execCreateGates },
		{ "CreateWallMeshes", &AWallCollisionManager::execCreateWallMeshes },
		{ "CreateWallSegment", &AWallCollisionManager::execCreateWallSegment },
		{ "DistancePointToLine", &AWallCollisionManager::execDistancePointToLine },
		{ "DrawDebugCollisions", &AWallCollisionManager::execDrawDebugCollisions },
		{ "DrawDebugGates", &AWallCollisionManager::execDrawDebugGates },
		{ "DrawDebugPatrolPath", &AWallCollisionManager::execDrawDebugPatrolPath },
		{ "DrawDebugWalls", &AWallCollisionManager::execDrawDebugWalls },
		{ "FindPathAroundWalls", &AWallCollisionManager::execFindPathAroundWalls },
		{ "GenerateCornerWallPoints", &AWallCollisionManager::execGenerateCornerWallPoints },
		{ "GenerateCurvedWallPoints", &AWallCollisionManager::execGenerateCurvedWallPoints },
		{ "GeneratePatrolPath", &AWallCollisionManager::execGeneratePatrolPath },
		{ "GenerateStraightWallPoints", &AWallCollisionManager::execGenerateStraightWallPoints },
		{ "GenerateWallGeometry", &AWallCollisionManager::execGenerateWallGeometry },
		{ "GetClosestPointOnWalls", &AWallCollisionManager::execGetClosestPointOnWalls },
		{ "GetDistanceToNearestWall", &AWallCollisionManager::execGetDistanceToNearestWall },
		{ "GetGates", &AWallCollisionManager::execGetGates },
		{ "GetMapArea", &AWallCollisionManager::execGetMapArea },
		{ "GetMapCenter", &AWallCollisionManager::execGetMapCenter },
		{ "GetMaterialConfig", &AWallCollisionManager::execGetMaterialConfig },
		{ "GetNearestGatePosition", &AWallCollisionManager::execGetNearestGatePosition },
		{ "GetSafePositionNearWalls", &AWallCollisionManager::execGetSafePositionNearWalls },
		{ "GetTotalWallLength", &AWallCollisionManager::execGetTotalWallLength },
		{ "GetWallCenter", &AWallCollisionManager::execGetWallCenter },
		{ "GetWallIntersectionPoints", &AWallCollisionManager::execGetWallIntersectionPoints },
		{ "GetWallPerimeter", &AWallCollisionManager::execGetWallPerimeter },
		{ "GetWallSections", &AWallCollisionManager::execGetWallSections },
		{ "InitializeWallSystem", &AWallCollisionManager::execInitializeWallSystem },
		{ "IsGateOpen", &AWallCollisionManager::execIsGateOpen },
		{ "IsPathClearOfWalls", &AWallCollisionManager::execIsPathClearOfWalls },
		{ "IsPointInsidePolygon", &AWallCollisionManager::execIsPointInsidePolygon },
		{ "IsPositionInsideWalls", &AWallCollisionManager::execIsPositionInsideWalls },
		{ "LineIntersectsWall", &AWallCollisionManager::execLineIntersectsWall },
		{ "OnActorBeginOverlap", &AWallCollisionManager::execOnActorBeginOverlap },
		{ "OnActorEndOverlap", &AWallCollisionManager::execOnActorEndOverlap },
		{ "OnWallHit", &AWallCollisionManager::execOnWallHit },
		{ "OpenGate", &AWallCollisionManager::execOpenGate },
		{ "ProjectPointOntoLine", &AWallCollisionManager::execProjectPointOntoLine },
		{ "RotatePointAroundCenter", &AWallCollisionManager::execRotatePointAroundCenter },
		{ "SetupCollisionSystem", &AWallCollisionManager::execSetupCollisionSystem },
		{ "SetWallHeight", &AWallCollisionManager::execSetWallHeight },
		{ "SetWallMaterial", &AWallCollisionManager::execSetWallMaterial },
		{ "SetWallThickness", &AWallCollisionManager::execSetWallThickness },
		{ "UpdateWallMeshes", &AWallCollisionManager::execUpdateWallMeshes },
		{ "ValidateCollisionSetup", &AWallCollisionManager::execValidateCollisionSetup },
		{ "ValidateWallGeometry", &AWallCollisionManager::execValidateWallGeometry },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AWallCollisionManager;
UClass* AWallCollisionManager::GetPrivateStaticClass()
{
	using TClass = AWallCollisionManager;
	if (!Z_Registration_Info_UClass_AWallCollisionManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("WallCollisionManager"),
			Z_Registration_Info_UClass_AWallCollisionManager.InnerSingleton,
			StaticRegisterNativesAWallCollisionManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AWallCollisionManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister()
{
	return AWallCollisionManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AWallCollisionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "AWallCollisionManager.h" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes principais\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallMeshes_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionBoxes_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallSections_MetaData[] = {
		{ "Category", "Wall System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados das paredes\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados das paredes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Gates_MetaData[] = {
		{ "Category", "Wall System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialConfig_MetaData[] = {
		{ "Category", "Wall System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolArea_MetaData[] = {
		{ "Category", "Wall System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapBoundarySize_MetaData[] = {
		{ "Category", "Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de geometria\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de geometria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallHeight_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallThickness_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CornerRadius_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallSegments_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecentCollisions_MetaData[] = {
		{ "Category", "Collision System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de colis\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de colis\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionTolerance_MetaData[] = {
		{ "Category", "Collision System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionLogging_MetaData[] = {
		{ "Category", "Collision System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCollisionHistory_MetaData[] = {
		{ "Category", "Collision System" },
		{ "ModuleRelativePath", "Public/AWallCollisionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WallMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WallMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionBoxes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CollisionBoxes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WallSections_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WallSections;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Gates_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Gates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaterialConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolArea;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MapBoundarySize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WallHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WallThickness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CornerRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WallSegments;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RecentCollisions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecentCollisions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CollisionTolerance;
	static void NewProp_bEnableCollisionLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionLogging;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCollisionHistory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AWallCollisionManager_ApplyWallMaterial, "ApplyWallMaterial" }, // 3711341776
		{ &Z_Construct_UFunction_AWallCollisionManager_AuthorizeActorForGate, "AuthorizeActorForGate" }, // 550488629
		{ &Z_Construct_UFunction_AWallCollisionManager_CalculateAngleBetweenVectors, "CalculateAngleBetweenVectors" }, // 1810267193
		{ &Z_Construct_UFunction_AWallCollisionManager_CalculateWallBoundaries, "CalculateWallBoundaries" }, // 813851864
		{ &Z_Construct_UFunction_AWallCollisionManager_CalculateWallLength, "CalculateWallLength" }, // 2021397861
		{ &Z_Construct_UFunction_AWallCollisionManager_CalculateWallNormal, "CalculateWallNormal" }, // 2559923661
		{ &Z_Construct_UFunction_AWallCollisionManager_CanActorPassThroughGate, "CanActorPassThroughGate" }, // 346970283
		{ &Z_Construct_UFunction_AWallCollisionManager_CheckCollisionWithWalls, "CheckCollisionWithWalls" }, // 2164126782
		{ &Z_Construct_UFunction_AWallCollisionManager_CloseGate, "CloseGate" }, // 3025003365
		{ &Z_Construct_UFunction_AWallCollisionManager_CreateGates, "CreateGates" }, // 3595609356
		{ &Z_Construct_UFunction_AWallCollisionManager_CreateWallMeshes, "CreateWallMeshes" }, // 2089372249
		{ &Z_Construct_UFunction_AWallCollisionManager_CreateWallSegment, "CreateWallSegment" }, // 300129828
		{ &Z_Construct_UFunction_AWallCollisionManager_DistancePointToLine, "DistancePointToLine" }, // 495376666
		{ &Z_Construct_UFunction_AWallCollisionManager_DrawDebugCollisions, "DrawDebugCollisions" }, // 1843342091
		{ &Z_Construct_UFunction_AWallCollisionManager_DrawDebugGates, "DrawDebugGates" }, // 813403481
		{ &Z_Construct_UFunction_AWallCollisionManager_DrawDebugPatrolPath, "DrawDebugPatrolPath" }, // 1139046283
		{ &Z_Construct_UFunction_AWallCollisionManager_DrawDebugWalls, "DrawDebugWalls" }, // 2872040769
		{ &Z_Construct_UFunction_AWallCollisionManager_FindPathAroundWalls, "FindPathAroundWalls" }, // 81203343
		{ &Z_Construct_UFunction_AWallCollisionManager_GenerateCornerWallPoints, "GenerateCornerWallPoints" }, // 1256278174
		{ &Z_Construct_UFunction_AWallCollisionManager_GenerateCurvedWallPoints, "GenerateCurvedWallPoints" }, // 1198341914
		{ &Z_Construct_UFunction_AWallCollisionManager_GeneratePatrolPath, "GeneratePatrolPath" }, // 3287424401
		{ &Z_Construct_UFunction_AWallCollisionManager_GenerateStraightWallPoints, "GenerateStraightWallPoints" }, // 336629320
		{ &Z_Construct_UFunction_AWallCollisionManager_GenerateWallGeometry, "GenerateWallGeometry" }, // 3091241139
		{ &Z_Construct_UFunction_AWallCollisionManager_GetClosestPointOnWalls, "GetClosestPointOnWalls" }, // 3464547413
		{ &Z_Construct_UFunction_AWallCollisionManager_GetDistanceToNearestWall, "GetDistanceToNearestWall" }, // 971960319
		{ &Z_Construct_UFunction_AWallCollisionManager_GetGates, "GetGates" }, // 1449539117
		{ &Z_Construct_UFunction_AWallCollisionManager_GetMapArea, "GetMapArea" }, // 1733500716
		{ &Z_Construct_UFunction_AWallCollisionManager_GetMapCenter, "GetMapCenter" }, // 3938657793
		{ &Z_Construct_UFunction_AWallCollisionManager_GetMaterialConfig, "GetMaterialConfig" }, // 3939384099
		{ &Z_Construct_UFunction_AWallCollisionManager_GetNearestGatePosition, "GetNearestGatePosition" }, // 2675277474
		{ &Z_Construct_UFunction_AWallCollisionManager_GetSafePositionNearWalls, "GetSafePositionNearWalls" }, // 3783564004
		{ &Z_Construct_UFunction_AWallCollisionManager_GetTotalWallLength, "GetTotalWallLength" }, // 2508574922
		{ &Z_Construct_UFunction_AWallCollisionManager_GetWallCenter, "GetWallCenter" }, // 1748908309
		{ &Z_Construct_UFunction_AWallCollisionManager_GetWallIntersectionPoints, "GetWallIntersectionPoints" }, // 648237594
		{ &Z_Construct_UFunction_AWallCollisionManager_GetWallPerimeter, "GetWallPerimeter" }, // 2233759777
		{ &Z_Construct_UFunction_AWallCollisionManager_GetWallSections, "GetWallSections" }, // 500172927
		{ &Z_Construct_UFunction_AWallCollisionManager_InitializeWallSystem, "InitializeWallSystem" }, // 164856499
		{ &Z_Construct_UFunction_AWallCollisionManager_IsGateOpen, "IsGateOpen" }, // 3436222960
		{ &Z_Construct_UFunction_AWallCollisionManager_IsPathClearOfWalls, "IsPathClearOfWalls" }, // 3963708879
		{ &Z_Construct_UFunction_AWallCollisionManager_IsPointInsidePolygon, "IsPointInsidePolygon" }, // 2311708881
		{ &Z_Construct_UFunction_AWallCollisionManager_IsPositionInsideWalls, "IsPositionInsideWalls" }, // 3807797633
		{ &Z_Construct_UFunction_AWallCollisionManager_LineIntersectsWall, "LineIntersectsWall" }, // 650326352
		{ &Z_Construct_UFunction_AWallCollisionManager_OnActorBeginOverlap, "OnActorBeginOverlap" }, // 3972037053
		{ &Z_Construct_UFunction_AWallCollisionManager_OnActorEndOverlap, "OnActorEndOverlap" }, // 4103299670
		{ &Z_Construct_UFunction_AWallCollisionManager_OnWallHit, "OnWallHit" }, // 3262361052
		{ &Z_Construct_UFunction_AWallCollisionManager_OpenGate, "OpenGate" }, // 1893522431
		{ &Z_Construct_UFunction_AWallCollisionManager_ProjectPointOntoLine, "ProjectPointOntoLine" }, // 3721296130
		{ &Z_Construct_UFunction_AWallCollisionManager_RotatePointAroundCenter, "RotatePointAroundCenter" }, // 1663322901
		{ &Z_Construct_UFunction_AWallCollisionManager_SetupCollisionSystem, "SetupCollisionSystem" }, // 4090637927
		{ &Z_Construct_UFunction_AWallCollisionManager_SetWallHeight, "SetWallHeight" }, // 3632719931
		{ &Z_Construct_UFunction_AWallCollisionManager_SetWallMaterial, "SetWallMaterial" }, // 488393771
		{ &Z_Construct_UFunction_AWallCollisionManager_SetWallThickness, "SetWallThickness" }, // 545149010
		{ &Z_Construct_UFunction_AWallCollisionManager_UpdateWallMeshes, "UpdateWallMeshes" }, // 3387717831
		{ &Z_Construct_UFunction_AWallCollisionManager_ValidateCollisionSetup, "ValidateCollisionSetup" }, // 1691696300
		{ &Z_Construct_UFunction_AWallCollisionManager_ValidateWallGeometry, "ValidateWallGeometry" }, // 735538240
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AWallCollisionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallMeshes_Inner = { "WallMeshes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallMeshes = { "WallMeshes", nullptr, (EPropertyFlags)0x001000800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, WallMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallMeshes_MetaData), NewProp_WallMeshes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CollisionBoxes_Inner = { "CollisionBoxes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CollisionBoxes = { "CollisionBoxes", nullptr, (EPropertyFlags)0x001000800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, CollisionBoxes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionBoxes_MetaData), NewProp_CollisionBoxes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallSections_Inner = { "WallSections", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FWallSection, METADATA_PARAMS(0, nullptr) }; // 3342107562
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallSections = { "WallSections", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, WallSections), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallSections_MetaData), NewProp_WallSections_MetaData) }; // 3342107562
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_Gates_Inner = { "Gates", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGateData, METADATA_PARAMS(0, nullptr) }; // 2506052715
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_Gates = { "Gates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, Gates), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Gates_MetaData), NewProp_Gates_MetaData) }; // 2506052715
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_MaterialConfig = { "MaterialConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, MaterialConfig), Z_Construct_UScriptStruct_FWallMaterialConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialConfig_MetaData), NewProp_MaterialConfig_MetaData) }; // 3033661663
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_PatrolArea = { "PatrolArea", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, PatrolArea), Z_Construct_UScriptStruct_FPatrolArea, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolArea_MetaData), NewProp_PatrolArea_MetaData) }; // 2053412072
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_MapBoundarySize = { "MapBoundarySize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, MapBoundarySize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapBoundarySize_MetaData), NewProp_MapBoundarySize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallHeight = { "WallHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, WallHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallHeight_MetaData), NewProp_WallHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallThickness = { "WallThickness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, WallThickness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallThickness_MetaData), NewProp_WallThickness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CornerRadius = { "CornerRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, CornerRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CornerRadius_MetaData), NewProp_CornerRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallSegments = { "WallSegments", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, WallSegments), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallSegments_MetaData), NewProp_WallSegments_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_RecentCollisions_Inner = { "RecentCollisions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FWallCollisionData, METADATA_PARAMS(0, nullptr) }; // 2147688072
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_RecentCollisions = { "RecentCollisions", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, RecentCollisions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecentCollisions_MetaData), NewProp_RecentCollisions_MetaData) }; // 2147688072
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CollisionTolerance = { "CollisionTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, CollisionTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionTolerance_MetaData), NewProp_CollisionTolerance_MetaData) };
void Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_bEnableCollisionLogging_SetBit(void* Obj)
{
	((AWallCollisionManager*)Obj)->bEnableCollisionLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_bEnableCollisionLogging = { "bEnableCollisionLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AWallCollisionManager), &Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_bEnableCollisionLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionLogging_MetaData), NewProp_bEnableCollisionLogging_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_MaxCollisionHistory = { "MaxCollisionHistory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AWallCollisionManager, MaxCollisionHistory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCollisionHistory_MetaData), NewProp_MaxCollisionHistory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AWallCollisionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CollisionBoxes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CollisionBoxes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallSections_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallSections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_Gates_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_Gates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_MaterialConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_PatrolArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_MapBoundarySize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallThickness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CornerRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_WallSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_RecentCollisions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_RecentCollisions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_CollisionTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_bEnableCollisionLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AWallCollisionManager_Statics::NewProp_MaxCollisionHistory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AWallCollisionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AWallCollisionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AWallCollisionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AWallCollisionManager_Statics::ClassParams = {
	&AWallCollisionManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AWallCollisionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AWallCollisionManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AWallCollisionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AWallCollisionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AWallCollisionManager()
{
	if (!Z_Registration_Info_UClass_AWallCollisionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AWallCollisionManager.OuterSingleton, Z_Construct_UClass_AWallCollisionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AWallCollisionManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AWallCollisionManager);
AWallCollisionManager::~AWallCollisionManager() {}
// ********** End Class AWallCollisionManager ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EWallType_StaticEnum, TEXT("EWallType"), &Z_Registration_Info_UEnum_EWallType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3601448334U) },
		{ EWallOrientation_StaticEnum, TEXT("EWallOrientation"), &Z_Registration_Info_UEnum_EWallOrientation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3305671181U) },
		{ EWallMaterial_StaticEnum, TEXT("EWallMaterial"), &Z_Registration_Info_UEnum_EWallMaterial, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2951854745U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FWallSection::StaticStruct, Z_Construct_UScriptStruct_FWallSection_Statics::NewStructOps, TEXT("WallSection"), &Z_Registration_Info_UScriptStruct_FWallSection, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWallSection), 3342107562U) },
		{ FWallCollisionData::StaticStruct, Z_Construct_UScriptStruct_FWallCollisionData_Statics::NewStructOps, TEXT("WallCollisionData"), &Z_Registration_Info_UScriptStruct_FWallCollisionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWallCollisionData), 2147688072U) },
		{ FWallMaterialConfig::StaticStruct, Z_Construct_UScriptStruct_FWallMaterialConfig_Statics::NewStructOps, TEXT("WallMaterialConfig"), &Z_Registration_Info_UScriptStruct_FWallMaterialConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWallMaterialConfig), 3033661663U) },
		{ FGateData::StaticStruct, Z_Construct_UScriptStruct_FGateData_Statics::NewStructOps, TEXT("GateData"), &Z_Registration_Info_UScriptStruct_FGateData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FGateData), 2506052715U) },
		{ FPatrolArea::StaticStruct, Z_Construct_UScriptStruct_FPatrolArea_Statics::NewStructOps, TEXT("PatrolArea"), &Z_Registration_Info_UScriptStruct_FPatrolArea, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPatrolArea), 2053412072U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AWallCollisionManager, AWallCollisionManager::StaticClass, TEXT("AWallCollisionManager"), &Z_Registration_Info_UClass_AWallCollisionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AWallCollisionManager), 954412974U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_1391404880(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AWallCollisionManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
