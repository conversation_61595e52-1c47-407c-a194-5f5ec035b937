#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/SceneComponent.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Containers/Array.h"
#include "UObject/ObjectMacros.h"
#include "TimerManager.h"
#include "AbilitySystemComponent.h"
#include "GameplayTagContainer.h"
#include "GameplayEffect.h"
#include "AttributeSet.h"
#include "Kismet/KismetSystemLibrary.h"
#include "ABaronAuracronManager.generated.h"

// Enums para estados e tipos do Barão
UENUM(BlueprintType)
enum class EBaronState : uint8
{
    Dormant     UMETA(DisplayName = "Dormindo"),
    Spawning    UMETA(DisplayName = "Aparecendo"),
    Active      UMETA(DisplayName = "Ativo"),
    Combat      UMETA(DisplayName = "Em Combate"),
    Rotating    UMETA(DisplayName = "Rotacionando"),
    Dead        UMETA(DisplayName = "Morto")
};

UENUM(BlueprintType)
enum class ESentinelType : uint8
{
    Cristalina  UMETA(DisplayName = "Sentinela Cristalina"),
    Guardian    UMETA(DisplayName = "Guardião dos Portais")
};

// Estruturas de dados para o Barão e objetivos secundários
USTRUCT(BlueprintType)
struct FBaronData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float BaseHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float CurrentHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float HealthScaling;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float SpawnTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float RespawnTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float RotationInterval;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    EBaronState CurrentState;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    TObjectPtr<AActor> BaronActor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float LastRotationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float CombatStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float LastPlayerCheckTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float LastStateChangeTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    bool bInCombat;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron")
    float CurrentRotation;

    FBaronData()
    {
        Position = FVector(0.0f, -4800.0f, 0.0f);
        BaseHealth = 5000.0f;
        CurrentHealth = 5000.0f;
        HealthScaling = 300.0f;
        SpawnTime = 1200.0f; // 20 minutos
        RespawnTime = 360.0f; // 6 minutos
        RotationInterval = 60.0f; // 1 minuto
        CurrentState = EBaronState::Dormant;
        BaronActor = nullptr;
        LastRotationTime = 0.0f;
        CombatStartTime = 0.0f;
        LastPlayerCheckTime = 0.0f;
        LastStateChangeTime = 0.0f;
        bInCombat = false;
        CurrentRotation = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FHexagonalArea
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    FVector Center;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float Radius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FVector> Vertices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float Area;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FVector> EntrancePositions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float EntranceWidth;

    FHexagonalArea()
    {
        Center = FVector(0.0f, -4800.0f, 0.0f);
        Radius = 700.0f;
        Area = 0.0f;
        EntranceWidth = 200.0f;
    }
};

USTRUCT(BlueprintType)
struct FSentinelData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    ESentinelType Type;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    FVector OriginalPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    float Radius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    float Health;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    float RespawnTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    float RotationSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    float RotationInterval;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    bool bIsAlive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    TObjectPtr<AActor> SentinelActor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    float LastAttackTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinel")
    float AttackCooldown;

    FSentinelData()
    {
        Type = ESentinelType::Cristalina;
        Position = FVector::ZeroVector;
        OriginalPosition = FVector::ZeroVector;
        Radius = 400.0f;
        Health = 1500.0f;
        RespawnTime = 180.0f;
        RotationSpeed = 90.0f; // graus por rotação
        RotationInterval = 120.0f; // segundos
        bIsAlive = true;
        SentinelActor = nullptr;
        LastAttackTime = 0.0f;
        AttackCooldown = 2.0f; // 2 segundos entre ataques
    }
};

USTRUCT(BlueprintType)
struct FGuardianData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    FVector OriginalPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float Radius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float Health;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float RespawnTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    bool bIsAlive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    TObjectPtr<AActor> GuardianActor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float LastAttackTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float AttackCooldown;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    bool bDefenseMode;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float DefenseModeStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    bool bIsDefending;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float DefenseStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Guardian")
    float RotationSpeed;

    FGuardianData()
    {
        Position = FVector::ZeroVector;
        OriginalPosition = FVector::ZeroVector;
        Radius = 300.0f;
        Health = 2000.0f;
        RespawnTime = 240.0f;
        bIsAlive = true;
        GuardianActor = nullptr;
        LastAttackTime = 0.0f;
        AttackCooldown = 1.5f; // 1.5 segundos entre ataques
        bDefenseMode = false;
        DefenseModeStartTime = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FBuffData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Buff")
    float DamageBonus;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Buff")
    float SpeedBonus;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Buff")
    float Duration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Buff")
    float EffectRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Buff")
    int32 TeamID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Buff")
    float BuffStartTime;

    FBuffData()
    {
        DamageBonus = 0.2f; // +20%
        SpeedBonus = 0.15f; // +15%
        Duration = 180.0f; // 3 minutos
        EffectRadius = 1200.0f;
        TeamID = -1;
        BuffStartTime = 0.0f;
    }
};

UCLASS(BlueprintType, Blueprintable)
class AURA_API ABaronAuracronManager : public AActor
{
    GENERATED_BODY()

public:
    ABaronAuracronManager();

protected:
    // Funções principais do ciclo de vida (com override para UE5.6)
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Componentes principais (usando TObjectPtr para UE5.6)
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UStaticMeshComponent> BaronMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USphereComponent> CombatArea;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USphereComponent> BuffArea;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UStaticMeshComponent> HexagonMeshComponent;

    // Dados do Barão Auracron
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Baron Configuration")
    FBaronData BaronData;

    // Geometria hexagonal do covil
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hexagonal Geometry")
    FHexagonalArea HexagonalCovil;

    // Sentinelas e guardiões
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinels")
    TArray<FSentinelData> SentinelData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinels")
    TArray<FGuardianData> GuardianData;

    // Arrays para compatibilidade com código existente
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinels")
    TArray<FSentinelData> SentinelasCristalinas;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sentinels")
    TArray<FGuardianData> GuardioesPortais;

    // Sistema de buffs
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Buff System")
    FBuffData TeamBuff;

    // Configurações de timing
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float GameStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float CurrentGameTime;

    // Referências para atores dinâmicos
    UPROPERTY(BlueprintReadWrite, Category = "Runtime")
    TObjectPtr<AActor> BaronActor;

    UPROPERTY(BlueprintReadWrite, Category = "Runtime")
    TArray<TObjectPtr<AActor>> SentinelActors;

    UPROPERTY(BlueprintReadWrite, Category = "Runtime")
    TArray<TObjectPtr<AActor>> GuardianActors;

    // GameplayEffects para sistema de combate
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Effects")
    TSubclassOf<class UGameplayEffect> SentinelDamageEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Effects")
    TSubclassOf<class UGameplayEffect> GoldRewardEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Effects")
    TSubclassOf<class UGameplayEffect> ExperienceRewardEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Effects")
    TSubclassOf<class UGameplayEffect> GuardianDamageEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Effects")
    TSubclassOf<class UGameplayEffect> BaronAreaDamageEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Effects")
    TObjectPtr<class UGameplayEffect> TeamBuffEffect;

    // Classes para efeitos visuais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSubclassOf<AActor> RewardEffectClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSubclassOf<AActor> SentinelAttackEffectClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSubclassOf<AActor> GuardianAttackEffectClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effects")
    TSubclassOf<AActor> BaronAreaEffectClass;

    // Geometria hexagonal (BlueprintCallable para UE5.6)
    UFUNCTION(BlueprintCallable, Category = "Hexagonal Geometry")
    void CalculateHexagonalVertices();

    UFUNCTION(BlueprintCallable, Category = "Hexagonal Geometry")
    float CalculateHexagonalArea() const;

    UFUNCTION(BlueprintCallable, Category = "Hexagonal Geometry")
    TArray<FVector> GetHexagonalEntrances() const;

    UFUNCTION(BlueprintCallable, Category = "Hexagonal Geometry")
    bool IsPositionInHexagon(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Hexagonal Geometry")
    FVector GetClosestEntrancePosition(const FVector& FromPosition) const;

    // Funções de spawn e controle do Barão
    UFUNCTION(BlueprintCallable, Category = "Baron Management")
    void SpawnBaron();

    UFUNCTION(BlueprintCallable, Category = "Baron Management")
    void DespawnBaron();

    UFUNCTION(BlueprintCallable, Category = "Baron Management")
    void UpdateBaronHealth();

    UFUNCTION(BlueprintCallable, Category = "Baron Management")
    void StartBaronRotation();

    UFUNCTION(BlueprintCallable, Category = "Baron Management")
    void PerformBaronRotation();

    // Funções de combate
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void StartCombat(AActor* Attacker);

    UFUNCTION(BlueprintCallable, Category = "Combat")
    void EndCombat();

    UFUNCTION(BlueprintCallable, Category = "Combat")
    virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;

    UFUNCTION(BlueprintCallable, Category = "Combat")
    void OnBaronDeath(AActor* Killer);

    // Funções de sentinelas e guardiões
    UFUNCTION(BlueprintCallable, Category = "Sentinels")
    void InitializeSentinels();

    UFUNCTION(BlueprintCallable, Category = "Sentinels")
    void SpawnSentinelasCristalinas();

    UFUNCTION(BlueprintCallable, Category = "Sentinels")
    void SpawnGuardioesPortais();

    UFUNCTION(BlueprintCallable, Category = "Sentinels")
    void UpdateSentinelRotations();

    UFUNCTION(BlueprintCallable, Category = "Sentinels")
    void RespawnSentinel(int32 SentinelIndex, ESentinelType Type);

    // Sistema de buffs
    UFUNCTION(BlueprintCallable, Category = "Buff System")
    void ApplyTeamBuff(int32 TeamID);

    UFUNCTION(BlueprintCallable, Category = "Buff System")
    void RemoveTeamBuff(int32 TeamID);

    UFUNCTION(BlueprintCallable, Category = "Buff System")
    TArray<AActor*> GetPlayersInBuffRange() const;

    UFUNCTION(BlueprintCallable, Category = "Buff System")
    void DistributeRewards(int32 WinningTeam);

    // Validação da geometria (const para UE5.6)
    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateHexagonalGeometry() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    void DrawDebugHexagon() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    void DrawDebugSentinels() const;

    // Timing e estado do Barão (const quando apropriado)
    UFUNCTION(BlueprintCallable, Category = "Timing")
    bool ShouldSpawnBaron() const;

    UFUNCTION(BlueprintCallable, Category = "Timing")
    float GetTimeUntilSpawn() const;

    UFUNCTION(BlueprintCallable, Category = "Timing")
    float GetTimeUntilRespawn() const;

    UFUNCTION(BlueprintCallable, Category = "State")
    void SetBaronState(EBaronState NewState);

    UFUNCTION(BlueprintCallable, Category = "State")
    EBaronState GetBaronState() const { return BaronData.CurrentState; }

    // Funções auxiliares para implementações
    void UpdateCombatBehavior(float CombatDuration, int32 PlayerCount);
    void ExecuteSentinelAttack(int32 SentinelIndex, AActor* Target);
    void ExecuteGuardianAttack(int32 GuardianIndex, AActor* Target);
    void UpdateSentinelPositionsForRotation(float NewRotation);
    int32 GetPlayerTeamID(APlayerController* PlayerController) const;
    void TriggerBaronAreaAbility();
    void SpawnRewardEffect(const FVector& Location);
    void SpawnSentinelAttackEffect(const FVector& From, const FVector& To);
    void SpawnGuardianAttackEffect(const FVector& From, const FVector& To);
    void SpawnBaronAreaEffect(const FVector& Center, float Radius);

private:
    // Funções auxiliares privadas
    void UpdateGameTime(float DeltaTime);
    void CheckSpawnConditions();
    void UpdateBaronBehavior(float DeltaTime);
    void UpdateSentinelBehavior(float DeltaTime);
    
    // Funções matemáticas para geometria hexagonal
    FVector CalculateHexagonVertex(int32 VertexIndex, const FVector& Center, float Radius) const;
    bool IsPointInsideHexagon(const FVector& Point, const TArray<FVector>& HexVertices) const;
    float CalculateDistanceToHexagonEdge(const FVector& Point) const;
    
    // Funções de rotação matemática
    FVector RotatePointAroundCenter(const FVector& Point, const FVector& Center, float AngleDegrees) const;
    void CalculateSentinelPositions();
    
    // Timers para controle de eventos
    FTimerHandle BaronSpawnTimer;
    FTimerHandle BaronRotationTimer;
    FTimerHandle SentinelRotationTimer;
    FTimerHandle BuffDurationTimer;
    
    // Constantes matemáticas (conforme documentação)
    static constexpr float HEXAGON_RADIUS = 700.0f; // Raio do hexágono
    static constexpr float HEXAGON_AREA_MULTIPLIER = 2.598076f; // (3√3/2)
    static constexpr float DEGREES_PER_VERTEX = 60.0f; // 360/6 vértices
    static constexpr float ENTRANCE_WIDTH = 200.0f;
    
    // Áreas de efeito (conforme documentação)
    static constexpr float COMBAT_AREA_RADIUS = 1200.0f; // Área de combate
    static constexpr float BUFF_AREA_RADIUS = 1200.0f; // Área de buff
    
    // Posições das sentinelas (coordenadas fixas conforme doc)
    static constexpr float SENTINEL_RADIUS = 400.0f; // Raio das sentinelas cristalinas
    static constexpr float GUARDIAN_RADIUS = 300.0f; // Raio dos guardiões
    
    // Configurações de HP (conforme documentação)
    static constexpr float BASE_BARON_HEALTH = 5000.0f; // HP base
    static constexpr float BARON_HEALTH_SCALING = 300.0f; // +300 HP por minuto

    // Constantes de combate para sentinelas e guardiões
    static constexpr float SENTINEL_ATTACK_RANGE = 600.0f;
    static constexpr float SENTINEL_ATTACK_COOLDOWN = 2.0f;
    static constexpr float GUARDIAN_DETECTION_RANGE = 800.0f;
    static constexpr float GUARDIAN_ATTACK_RANGE = 500.0f;
    static constexpr float GUARDIAN_ATTACK_COOLDOWN = 1.5f;
    
    // Configurações de buff (conforme documentação)
    static constexpr float TEAM_BUFF_DAMAGE = 0.2f; // +20% dano
    static constexpr float TEAM_BUFF_SPEED = 0.15f; // +15% velocidade
    static constexpr float BUFF_DURATION = 180.0f; // 180 segundos
    
    // Configurações de timing (conforme documentação)
    static constexpr float BARON_SPAWN_TIME = 1200.0f; // 20 minutos (1200s)
    static constexpr float BARON_RESPAWN_TIME = 360.0f; // 6 minutos (360s)
    static constexpr float HEXAGON_ROTATION_INTERVAL = 60.0f; // Rotação hexagonal a cada 60s
};