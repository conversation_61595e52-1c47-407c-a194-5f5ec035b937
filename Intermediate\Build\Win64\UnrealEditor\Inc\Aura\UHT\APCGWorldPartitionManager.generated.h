// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "APCGWorldPartitionManager.h"

#ifdef AURA_APCGWorldPartitionManager_generated_h
#error "APCGWorldPartitionManager.generated.h already included, missing '#pragma once' in APCGWorldPartitionManager.h"
#endif
#define AURA_APCGWorldPartitionManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AProceduralMapGenerator;
class UPCGComponent;
enum class EPCGWorldPartitionState : uint8;
struct FPCGDataLayerConfig;
struct FPCGStreamingCell;
struct FPCGWorldPartitionConfig;

// ********** Begin ScriptStruct FPCGWorldPartitionConfig ******************************************
#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_62_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGWorldPartitionConfig;
// ********** End ScriptStruct FPCGWorldPartitionConfig ********************************************

// ********** Begin ScriptStruct FPCGStreamingCell *************************************************
#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_102_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGStreamingCell_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGStreamingCell;
// ********** End ScriptStruct FPCGStreamingCell ***************************************************

// ********** Begin ScriptStruct FPCGDataLayerConfig ***********************************************
#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_150_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGDataLayerConfig;
// ********** End ScriptStruct FPCGDataLayerConfig *************************************************

// ********** Begin Class APCGWorldPartitionManager ************************************************
#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_204_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnPCGGenerationComplete); \
	DECLARE_FUNCTION(execOnCellUnloaded); \
	DECLARE_FUNCTION(execOnCellLoaded); \
	DECLARE_FUNCTION(execIntegrateWithLumen); \
	DECLARE_FUNCTION(execIntegrateWithNanite); \
	DECLARE_FUNCTION(execSetProceduralMapGenerator); \
	DECLARE_FUNCTION(execGetMemoryUsageMB); \
	DECLARE_FUNCTION(execGetOverallGenerationProgress); \
	DECLARE_FUNCTION(execGetGeneratingCellCount); \
	DECLARE_FUNCTION(execGetLoadedCellCount); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execGetCurrentState); \
	DECLARE_FUNCTION(execStopAllCellGeneration); \
	DECLARE_FUNCTION(execStartAllCellGeneration); \
	DECLARE_FUNCTION(execStopCellGeneration); \
	DECLARE_FUNCTION(execStartCellGeneration); \
	DECLARE_FUNCTION(execClearStreamingSources); \
	DECLARE_FUNCTION(execRemoveStreamingSource); \
	DECLARE_FUNCTION(execAddStreamingSource); \
	DECLARE_FUNCTION(execSetStreamingSource); \
	DECLARE_FUNCTION(execGetDataLayers); \
	DECLARE_FUNCTION(execSetDataLayerState); \
	DECLARE_FUNCTION(execRemoveDataLayer); \
	DECLARE_FUNCTION(execCreateDataLayer); \
	DECLARE_FUNCTION(execGetCellsInRadius); \
	DECLARE_FUNCTION(execGetLoadedCells); \
	DECLARE_FUNCTION(execGetStreamingCell); \
	DECLARE_FUNCTION(execRemoveStreamingCell); \
	DECLARE_FUNCTION(execCreateStreamingCell); \
	DECLARE_FUNCTION(execGetWorldPartitionConfig); \
	DECLARE_FUNCTION(execSetWorldPartitionConfig); \
	DECLARE_FUNCTION(execShutdownWorldPartition); \
	DECLARE_FUNCTION(execInitializeWorldPartition);


AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();

#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_204_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPCGWorldPartitionManager(); \
	friend struct Z_Construct_UClass_APCGWorldPartitionManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister(); \
public: \
	DECLARE_CLASS2(APCGWorldPartitionManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister) \
	DECLARE_SERIALIZER(APCGWorldPartitionManager)


#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_204_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	APCGWorldPartitionManager(APCGWorldPartitionManager&&) = delete; \
	APCGWorldPartitionManager(const APCGWorldPartitionManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APCGWorldPartitionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APCGWorldPartitionManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APCGWorldPartitionManager) \
	NO_API virtual ~APCGWorldPartitionManager();


#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_201_PROLOG
#define FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_204_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_204_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_204_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h_204_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class APCGWorldPartitionManager;

// ********** End Class APCGWorldPartitionManager **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h

// ********** Begin Enum EPCGWorldPartitionState ***************************************************
#define FOREACH_ENUM_EPCGWORLDPARTITIONSTATE(op) \
	op(EPCGWorldPartitionState::Uninitialized) \
	op(EPCGWorldPartitionState::Initializing) \
	op(EPCGWorldPartitionState::Active) \
	op(EPCGWorldPartitionState::Streaming) \
	op(EPCGWorldPartitionState::Paused) \
	op(EPCGWorldPartitionState::Error) 

enum class EPCGWorldPartitionState : uint8;
template<> struct TIsUEnumClass<EPCGWorldPartitionState> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGWorldPartitionState>();
// ********** End Enum EPCGWorldPartitionState *****************************************************

// ********** Begin Enum EPCGStreamingPriority *****************************************************
#define FOREACH_ENUM_EPCGSTREAMINGPRIORITY(op) \
	op(EPCGStreamingPriority::Low) \
	op(EPCGStreamingPriority::Medium) \
	op(EPCGStreamingPriority::High) \
	op(EPCGStreamingPriority::Critical) 

enum class EPCGStreamingPriority : uint8;
template<> struct TIsUEnumClass<EPCGStreamingPriority> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGStreamingPriority>();
// ********** End Enum EPCGStreamingPriority *******************************************************

// ********** Begin Enum EPCGDataLayerType *********************************************************
#define FOREACH_ENUM_EPCGDATALAYERTYPE(op) \
	op(EPCGDataLayerType::Terrain) \
	op(EPCGDataLayerType::Vegetation) \
	op(EPCGDataLayerType::Structures) \
	op(EPCGDataLayerType::Interactive) \
	op(EPCGDataLayerType::Lighting) \
	op(EPCGDataLayerType::Audio) 

enum class EPCGDataLayerType : uint8;
template<> struct TIsUEnumClass<EPCGDataLayerType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGDataLayerType>();
// ********** End Enum EPCGDataLayerType ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
