// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "APCGCacheManager.h"

#ifdef AURA_APCGCacheManager_generated_h
#error "APCGCacheManager.generated.h already included, missing '#pragma once' in APCGCacheManager.h"
#endif
#define AURA_APCGCacheManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APCGLumenIntegrator;
class APCGNaniteOptimizer;
class APCGStreamingManager;
class APCGWorldPartitionManager;
class UPCGPerformanceProfiler;
enum class EPCGCacheCompressionLevel : uint8;
enum class EPCGCachePriority : uint8;
enum class EPCGCacheStorageType : uint8;
struct FPCGCacheEntry;
struct FPCGCacheOperationResult;
struct FPCGCacheStatistics;

// ********** Begin ScriptStruct FPCGCacheConfig ***************************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_86_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGCacheConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGCacheConfig;
// ********** End ScriptStruct FPCGCacheConfig *****************************************************

// ********** Begin ScriptStruct FPCGCacheEntry ****************************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_158_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGCacheEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGCacheEntry;
// ********** End ScriptStruct FPCGCacheEntry ******************************************************

// ********** Begin ScriptStruct FPCGCacheStatistics ***********************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_231_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGCacheStatistics;
// ********** End ScriptStruct FPCGCacheStatistics *************************************************

// ********** Begin ScriptStruct FPCGCacheOperationResult ******************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_289_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGCacheOperationResult;
// ********** End ScriptStruct FPCGCacheOperationResult ********************************************

// ********** Begin Delegate FOnCacheEntryLoaded ***************************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_313_DELEGATE \
AURA_API void FOnCacheEntryLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnCacheEntryLoaded, const FString& EntryKey, FPCGCacheOperationResult const& Result);


// ********** End Delegate FOnCacheEntryLoaded *****************************************************

// ********** Begin Delegate FOnCacheEntryEvicted **************************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_314_DELEGATE \
AURA_API void FOnCacheEntryEvicted_DelegateWrapper(const FMulticastScriptDelegate& OnCacheEntryEvicted, const FString& EntryKey, EPCGCachePriority Priority);


// ********** End Delegate FOnCacheEntryEvicted ****************************************************

// ********** Begin Delegate FOnCacheStatisticsUpdated *********************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_315_DELEGATE \
AURA_API void FOnCacheStatisticsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnCacheStatisticsUpdated, FPCGCacheStatistics const& Statistics);


// ********** End Delegate FOnCacheStatisticsUpdated ***********************************************

// ********** Begin Delegate FOnCacheError *********************************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_316_DELEGATE \
AURA_API void FOnCacheError_DelegateWrapper(const FMulticastScriptDelegate& OnCacheError, const FString& ErrorMessage, const FString& EntryKey);


// ********** End Delegate FOnCacheError ***********************************************************

// ********** Begin Class APCGCacheManager *********************************************************
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_333_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDownloadFromNetwork); \
	DECLARE_FUNCTION(execUploadToNetwork); \
	DECLARE_FUNCTION(execSyncWithNetworkCache); \
	DECLARE_FUNCTION(execGetDiskCacheSize); \
	DECLARE_FUNCTION(execCleanupDiskCache); \
	DECLARE_FUNCTION(execLoadFromDisk); \
	DECLARE_FUNCTION(execSaveToDisk); \
	DECLARE_FUNCTION(execIntegrateWithProfiler); \
	DECLARE_FUNCTION(execIntegrateWithStreaming); \
	DECLARE_FUNCTION(execIntegrateWithLumen); \
	DECLARE_FUNCTION(execIntegrateWithNanite); \
	DECLARE_FUNCTION(execIntegrateWithWorldPartition); \
	DECLARE_FUNCTION(execEvictLeastFrequentlyUsed); \
	DECLARE_FUNCTION(execEvictLeastRecentlyUsed); \
	DECLARE_FUNCTION(execEvictExpiredEntries); \
	DECLARE_FUNCTION(execEvictByPriority); \
	DECLARE_FUNCTION(execForceEviction); \
	DECLARE_FUNCTION(execGetCompressionRatio); \
	DECLARE_FUNCTION(execDecompressData); \
	DECLARE_FUNCTION(execCompressData); \
	DECLARE_FUNCTION(execExportStatistics); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetDiskUsage); \
	DECLARE_FUNCTION(execGetMemoryUsage); \
	DECLARE_FUNCTION(execGetCacheHitRate); \
	DECLARE_FUNCTION(execGetHitRatio); \
	DECLARE_FUNCTION(execGetCacheStatistics); \
	DECLARE_FUNCTION(execRemoveEntryDependency); \
	DECLARE_FUNCTION(execAddEntryDependency); \
	DECLARE_FUNCTION(execSetEntryPriority); \
	DECLARE_FUNCTION(execGetEntriesByType); \
	DECLARE_FUNCTION(execGetEntriesByPriority); \
	DECLARE_FUNCTION(execGetAllEntries); \
	DECLARE_FUNCTION(execGetEntryInfo); \
	DECLARE_FUNCTION(execPrefetchByPattern); \
	DECLARE_FUNCTION(execPrefetchData); \
	DECLARE_FUNCTION(execStoreDataAsync); \
	DECLARE_FUNCTION(execLoadDataAsync); \
	DECLARE_FUNCTION(execRefreshEntry); \
	DECLARE_FUNCTION(execInvalidateEntry); \
	DECLARE_FUNCTION(execRemoveEntry); \
	DECLARE_FUNCTION(execHasEntry); \
	DECLARE_FUNCTION(execLoadData); \
	DECLARE_FUNCTION(execStoreData); \
	DECLARE_FUNCTION(execOptimizeCache); \
	DECLARE_FUNCTION(execClearCache); \
	DECLARE_FUNCTION(execSetMaintenanceMode); \
	DECLARE_FUNCTION(execEnableCache); \
	DECLARE_FUNCTION(execShutdownCache); \
	DECLARE_FUNCTION(execInitializeCache);


AURA_API UClass* Z_Construct_UClass_APCGCacheManager_NoRegister();

#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_333_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPCGCacheManager(); \
	friend struct Z_Construct_UClass_APCGCacheManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_APCGCacheManager_NoRegister(); \
public: \
	DECLARE_CLASS2(APCGCacheManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_APCGCacheManager_NoRegister) \
	DECLARE_SERIALIZER(APCGCacheManager)


#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_333_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	APCGCacheManager(APCGCacheManager&&) = delete; \
	APCGCacheManager(const APCGCacheManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APCGCacheManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APCGCacheManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APCGCacheManager) \
	NO_API virtual ~APCGCacheManager();


#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_330_PROLOG
#define FID_Aura_Source_Aura_Public_APCGCacheManager_h_333_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_APCGCacheManager_h_333_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGCacheManager_h_333_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGCacheManager_h_333_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class APCGCacheManager;

// ********** End Class APCGCacheManager ***********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_APCGCacheManager_h

// ********** Begin Enum EPCGCachePriority *********************************************************
#define FOREACH_ENUM_EPCGCACHEPRIORITY(op) \
	op(EPCGCachePriority::VeryLow) \
	op(EPCGCachePriority::Low) \
	op(EPCGCachePriority::Medium) \
	op(EPCGCachePriority::High) \
	op(EPCGCachePriority::VeryHigh) \
	op(EPCGCachePriority::Critical) 

enum class EPCGCachePriority : uint8;
template<> struct TIsUEnumClass<EPCGCachePriority> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGCachePriority>();
// ********** End Enum EPCGCachePriority ***********************************************************

// ********** Begin Enum EPCGCacheState ************************************************************
#define FOREACH_ENUM_EPCGCACHESTATE(op) \
	op(EPCGCacheState::Invalid) \
	op(EPCGCacheState::Loading) \
	op(EPCGCacheState::Valid) \
	op(EPCGCacheState::Dirty) \
	op(EPCGCacheState::Expired) \
	op(EPCGCacheState::Evicted) 

enum class EPCGCacheState : uint8;
template<> struct TIsUEnumClass<EPCGCacheState> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGCacheState>();
// ********** End Enum EPCGCacheState **************************************************************

// ********** Begin Enum EPCGCacheStorageType ******************************************************
#define FOREACH_ENUM_EPCGCACHESTORAGETYPE(op) \
	op(EPCGCacheStorageType::Memory) \
	op(EPCGCacheStorageType::Disk) \
	op(EPCGCacheStorageType::Hybrid) \
	op(EPCGCacheStorageType::Network) \
	op(EPCGCacheStorageType::Compressed) 

enum class EPCGCacheStorageType : uint8;
template<> struct TIsUEnumClass<EPCGCacheStorageType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGCacheStorageType>();
// ********** End Enum EPCGCacheStorageType ********************************************************

// ********** Begin Enum EPCGCacheEvictionPolicy ***************************************************
#define FOREACH_ENUM_EPCGCACHEEVICTIONPOLICY(op) \
	op(EPCGCacheEvictionPolicy::LRU) \
	op(EPCGCacheEvictionPolicy::LFU) \
	op(EPCGCacheEvictionPolicy::FIFO) \
	op(EPCGCacheEvictionPolicy::Random) \
	op(EPCGCacheEvictionPolicy::Priority) \
	op(EPCGCacheEvictionPolicy::Adaptive) 

enum class EPCGCacheEvictionPolicy : uint8;
template<> struct TIsUEnumClass<EPCGCacheEvictionPolicy> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGCacheEvictionPolicy>();
// ********** End Enum EPCGCacheEvictionPolicy *****************************************************

// ********** Begin Enum EPCGCacheCompressionLevel *************************************************
#define FOREACH_ENUM_EPCGCACHECOMPRESSIONLEVEL(op) \
	op(EPCGCacheCompressionLevel::None) \
	op(EPCGCacheCompressionLevel::Fast) \
	op(EPCGCacheCompressionLevel::Balanced) \
	op(EPCGCacheCompressionLevel::Maximum) \
	op(EPCGCacheCompressionLevel::Adaptive) 

enum class EPCGCacheCompressionLevel : uint8;
template<> struct TIsUEnumClass<EPCGCacheCompressionLevel> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGCacheCompressionLevel>();
// ********** End Enum EPCGCacheCompressionLevel ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
