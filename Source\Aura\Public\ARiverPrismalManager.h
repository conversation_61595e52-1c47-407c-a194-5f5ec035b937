#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Components/SplineComponent.h"
#include "Components/BoxComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Math/UnrealMathUtility.h"
#include "ARiverPrismalManager.generated.h"

// Constantes para o sistema de rio
#define RIVER_LENGTH 15000.0f          // 150 metros de comprimento
#define RIVER_WIDTH 400.0f             // 4 metros de largura
#define RIVER_DEPTH 200.0f             // 2 metros de profundidade
#define RIVER_AMPLITUDE 800.0f         // 8 metros de amplitude senoidal
#define RIVER_FREQUENCY 0.0005f        // Frequência da onda senoidal
#define RIVER_SEGMENTS 200             // Número de segmentos do rio
#define ISLAND_RADIUS 600.0f           // 6 metros de raio da ilha
#define ISLAND_HEIGHT 100.0f           // 1 metro de altura da ilha
#define HEXAGON_SIDES 6                // Lados do hexágono
#define WATER_FLOW_SPEED 100.0f        // 1 m/s velocidade da água
#define RIVER_COLLISION_TOLERANCE 50.0f // Tolerância de colisão

// Enums para o sistema de rio
UENUM(BlueprintType)
enum class ERiverFlowDirection : uint8
{
    Eastward     UMETA(DisplayName = "Eastward"),
    Westward     UMETA(DisplayName = "Westward"),
    Northward    UMETA(DisplayName = "Northward"),
    Southward    UMETA(DisplayName = "Southward"),
    Bidirectional UMETA(DisplayName = "Bidirectional")
};

UENUM(BlueprintType)
enum class ERiverSegmentType : uint8
{
    Straight     UMETA(DisplayName = "Straight"),
    Curved       UMETA(DisplayName = "Curved"),
    Bend         UMETA(DisplayName = "Bend"),
    Island       UMETA(DisplayName = "Island"),
    Bridge       UMETA(DisplayName = "Bridge")
};

UENUM(BlueprintType)
enum class EWaterQuality : uint8
{
    Pure         UMETA(DisplayName = "Pure"),
    Clear        UMETA(DisplayName = "Clear"),
    Murky        UMETA(DisplayName = "Murky"),
    Polluted     UMETA(DisplayName = "Polluted"),
    Magical      UMETA(DisplayName = "Magical")
};

UENUM(BlueprintType)
enum class EIslandType : uint8
{
    Hexagonal    UMETA(DisplayName = "Hexagonal"),
    Circular     UMETA(DisplayName = "Circular"),
    Irregular    UMETA(DisplayName = "Irregular"),
    Rectangular  UMETA(DisplayName = "Rectangular")
};

// Estruturas de dados para o rio
USTRUCT(BlueprintType)
struct FRiverSegment
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    FVector StartPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    FVector EndPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    FVector CenterPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    ERiverSegmentType SegmentType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    float Width;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    float Depth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    float FlowSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    FVector FlowDirection;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    TArray<FVector> SplinePoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Segment")
    float CurvatureRadius;

    FRiverSegment()
    {
        StartPosition = FVector::ZeroVector;
        EndPosition = FVector::ZeroVector;
        CenterPosition = FVector::ZeroVector;
        SegmentType = ERiverSegmentType::Straight;
        Width = RIVER_WIDTH;
        Depth = RIVER_DEPTH;
        FlowSpeed = WATER_FLOW_SPEED;
        FlowDirection = FVector::ForwardVector;
        CurvatureRadius = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FHexagonalIsland
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    FVector CenterPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    float Radius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    float Height;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    EIslandType IslandType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    TArray<FVector> HexagonVertices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    TArray<FVector> EdgeMidpoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    float EdgeLength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    float Apothem;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    bool bHasVegetation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island")
    bool bIsAccessible;

    FHexagonalIsland()
    {
        CenterPosition = FVector::ZeroVector;
        Radius = ISLAND_RADIUS;
        Height = ISLAND_HEIGHT;
        IslandType = EIslandType::Hexagonal;
        EdgeLength = 0.0f;
        Apothem = 0.0f;
        bHasVegetation = true;
        bIsAccessible = false;
    }
};

USTRUCT(BlueprintType)
struct FWaterProperties
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    EWaterQuality Quality;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float Temperature;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float Viscosity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    FLinearColor WaterColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float Transparency;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float RefractionIndex;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    bool bHasCurrent;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    bool bIsSwimmable;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    bool bCausesDamage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float FlowSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float SurfaceIncline;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float WaveAmplitude;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    float WaveFrequency;

    FWaterProperties()
    {
        Quality = EWaterQuality::Clear;
        Temperature = 20.0f;
        Viscosity = 1.0f;
        WaterColor = FLinearColor::Blue;
        Transparency = 0.8f;
        RefractionIndex = 1.33f;
        bHasCurrent = true;
        bIsSwimmable = true;
        bCausesDamage = false;
        FlowSpeed = 100.0f;
        SurfaceIncline = 0.0f;
        WaveAmplitude = 10.0f;
        WaveFrequency = 1.0f;
    }
};

USTRUCT(BlueprintType)
struct FRiverCollisionData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FVector CollisionPoint;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FVector WaterSurfaceNormal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    float WaterDepthAtPoint;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    AActor* CollidingActor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FDateTime CollisionTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FVector FlowVelocityAtPoint;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    bool bIsUnderwater;

    FRiverCollisionData()
    {
        CollisionPoint = FVector::ZeroVector;
        WaterSurfaceNormal = FVector::UpVector;
        WaterDepthAtPoint = 0.0f;
        CollidingActor = nullptr;
        CollisionTime = FDateTime::Now();
        FlowVelocityAtPoint = FVector::ZeroVector;
        bIsUnderwater = false;
    }
};

USTRUCT(BlueprintType)
struct FBridgeData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bridge")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bridge")
    FRotator Rotation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bridge")
    float Length;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bridge")
    float Width;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bridge")
    float Height;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bridge")
    bool bIsDestructible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bridge")
    TArray<AActor*> AuthorizedUnits;

    FBridgeData()
    {
        Position = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Length = 800.0f;
        Width = 300.0f;
        Height = 150.0f;
        bIsDestructible = false;
    }
};

/**
 * Gerenciador do sistema de rio com geometria senoidal e ilha hexagonal central
 * Implementa física da água, colisões e navegação aquática
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API ARiverPrismalManager : public AActor
{
    GENERATED_BODY()

public:
    ARiverPrismalManager();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Componentes principais
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USplineComponent* RiverSpline;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UStaticMeshComponent* IslandMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UBoxComponent* WaterCollisionBox;

    // Configurações do rio
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Settings")
    float RiverLength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Settings")
    float RiverWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Settings")
    float RiverDepth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Settings")
    float SinusoidalAmplitude;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Settings")
    float SinusoidalFrequency;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Settings")
    int32 RiverSegmentCount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Settings")
    ERiverFlowDirection FlowDirection;

    // Configurações da ilha
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Settings")
    FHexagonalIsland CentralIsland;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Island Settings")
    bool bGenerateIsland;

    // Propriedades da água
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Properties")
    FWaterProperties WaterConfig;

    // Arrays de dados
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "River Data")
    TArray<FRiverSegment> RiverSegments;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "River Data")
    TArray<UStaticMeshComponent*> RiverMeshes;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "River Data")
    TArray<UBoxComponent*> WaterCollisionBoxes;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Bridge Data")
    TArray<FBridgeData> Bridges;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Bridge Data")
    TArray<UStaticMeshComponent*> BridgeMeshes;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Collision Data")
    TArray<FRiverCollisionData> RecentWaterCollisions;

    // Materiais
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    UMaterialInterface* WaterMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    UMaterialInterface* IslandMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    UMaterialInterface* BridgeMaterial;

    // Configurações de debug
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDebugLines;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowFlowVectors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowIslandGeometry;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableCollisionLogging;

public:
    // Funções principais de inicialização
    UFUNCTION(BlueprintCallable, Category = "River System")
    void InitializeRiverSystem();

    UFUNCTION(BlueprintCallable, Category = "River System")
    void GenerateRiverGeometry();

    UFUNCTION(BlueprintCallable, Category = "River System")
    void SetupWaterPhysics();

    // Funções de geometria senoidal
    UFUNCTION(BlueprintCallable, Category = "River Geometry")
    TArray<FVector> GenerateSinusoidalPath(const FVector& StartPos, const FVector& EndPos) const;

    UFUNCTION(BlueprintCallable, Category = "River Geometry")
    FVector CalculateSinusoidalPoint(float Distance, const FVector& BaseDirection) const;

    UFUNCTION(BlueprintCallable, Category = "River Geometry")
    TArray<FVector> GenerateRiverBankPoints(const TArray<FVector>& CenterLine, float BankOffset) const;

    UFUNCTION(BlueprintCallable, Category = "River Geometry")
    FVector CalculateFlowDirection(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Geometry")
    float CalculateFlowSpeed(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Geometry")
    float CalculateWaterDepth(const FVector& Position) const;

    // Funções da ilha hexagonal
    UFUNCTION(BlueprintCallable, Category = "Island Geometry")
    void GenerateHexagonalIsland();

    UFUNCTION(BlueprintCallable, Category = "Island Geometry")
    TArray<FVector> CalculateHexagonVertices(const FVector& Center, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Island Geometry")
    TArray<FVector> CalculateHexagonEdgeMidpoints(const TArray<FVector>& Vertices) const;

    UFUNCTION(BlueprintCallable, Category = "Island Geometry")
    float CalculateHexagonApothem(float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Island Geometry")
    float CalculateHexagonEdgeLength(float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Island Geometry")
    bool IsPointInsideHexagon(const FVector& Point, const FHexagonalIsland& Island) const;

    UFUNCTION(BlueprintCallable, Category = "Island Geometry")
    FVector GetClosestPointOnIsland(const FVector& Position) const;

    // Sistema de colisões aquáticas
    UFUNCTION(BlueprintCallable, Category = "Water Collision")
    bool CheckWaterCollision(const FVector& Position, FRiverCollisionData& OutCollisionData) const;

    UFUNCTION(BlueprintCallable, Category = "Water Collision")
    bool IsPositionInWater(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Water Collision")
    bool IsPositionOnIsland(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Water Collision")
    FVector GetWaterSurfacePosition(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Water Collision")
    FVector ApplyWaterCurrent(const FVector& ActorVelocity, const FVector& Position, float DeltaTime) const;

    // Sistema de pontes
    UFUNCTION(BlueprintCallable, Category = "Bridge System")
    void CreateBridges();

    UFUNCTION(BlueprintCallable, Category = "Bridge System")
    int32 AddBridge(const FVector& Position, const FRotator& Rotation, float Length, float Width);

    UFUNCTION(BlueprintCallable, Category = "Bridge System")
    void RemoveBridge(int32 BridgeIndex);

    UFUNCTION(BlueprintCallable, Category = "Bridge System")
    bool CanUnitCrossBridge(AActor* Unit, int32 BridgeIndex) const;

    UFUNCTION(BlueprintCallable, Category = "Bridge System")
    FVector GetNearestBridgePosition(const FVector& Position) const;

    // Funções de navegação aquática
    UFUNCTION(BlueprintCallable, Category = "Water Navigation")
    TArray<FVector> FindWaterPath(const FVector& StartPos, const FVector& EndPos) const;

    UFUNCTION(BlueprintCallable, Category = "Water Navigation")
    bool IsWaterPathClear(const FVector& StartPos, const FVector& EndPos) const;

    UFUNCTION(BlueprintCallable, Category = "Water Navigation")
    FVector GetSafeWaterPosition(const FVector& DesiredPosition, float SafeDistance) const;

    UFUNCTION(BlueprintCallable, Category = "Water Navigation")
    TArray<FVector> GetSwimmingLanes() const;

    // Funções de validação e debug
    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateRiverGeometry() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateIslandGeometry() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateWaterPhysics() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugRiver() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugIsland() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugWaterFlow() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugBridges() const;

    // Eventos de colisão
    UFUNCTION()
    void OnWaterHit(UPrimitiveComponent* HitComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, FVector NormalImpulse, const FHitResult& Hit);

    UFUNCTION()
    void OnActorEnterWater(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnActorExitWater(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex);

    // Funções de utilidade matemática
    UFUNCTION(BlueprintCallable, Category = "Math Utilities")
    FVector RotateVectorAroundAxis(const FVector& Vector, const FVector& Axis, float AngleDegrees) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utilities")
    float CalculateDistanceToRiver(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utilities")
    FVector ProjectPointOntoRiver(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utilities")
    float InterpolateWaterDepth(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utilities")
    FVector CalculateWaterNormal(const FVector& Position) const;

    // Getters
    UFUNCTION(BlueprintCallable, Category = "Getters")
    float GetTotalRiverLength() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    float GetRiverVolume() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    FVector GetRiverCenter() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    float GetIslandArea() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    FVector GetIslandCenter() const;

    // Setters
    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetWaterProperties(const FWaterProperties& InWaterConfig);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetRiverDimensions(float NewLength, float NewWidth, float NewDepth);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetSinusoidalParameters(float NewAmplitude, float NewFrequency);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetIslandRadius(float NewRadius);

private:
    // Variáveis internas
    float LastUpdateTime;
    bool bIsInitialized;
    TMap<AActor*, float> ActorWaterDepthCache;
    FTimerHandle WaterUpdateTimer;
    FTimerHandle CollisionCleanupTimer;

    // Funções auxiliares internas
    void InitializeDefaultRiverSegments();
    void CreateRiverMeshes();
    void SetupWaterCollisionBoxes();
    void SetupCollisionCallbacks();
    void UpdateWaterFlow(float DeltaTime);
    void CleanupOldWaterCollisions();
    void UpdateActorWaterDepthCache();
    
    // Funções de criação de meshes
    UStaticMeshComponent* CreateRiverSegmentMesh(const FRiverSegment& Segment, int32 SegmentIndex);
    UStaticMeshComponent* CreateIslandMesh();
    UStaticMeshComponent* CreateBridgeMesh(const FBridgeData& BridgeData, int32 BridgeIndex);
    
    // Funções de validação interna
    bool ValidateRiverSegment(const FRiverSegment& Segment) const;
    bool ValidateHexagonalIsland(const FHexagonalIsland& Island) const;
    
    // Funções de utilidade interna
    FVector CalculateRiverCenterAtDistance(float Distance) const;
    TArray<FVector> InterpolateRiverPoints(const TArray<FVector>& ControlPoints, int32 Segments) const;
    void LogWaterCollisionEvent(const FRiverCollisionData& CollisionData) const;
    void UpdateCollisionHistory(const FRiverCollisionData& CollisionData);
};