// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AProceduralMapGenerator.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAProceduralMapGenerator() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AGeometricValidator_NoRegister();
AURA_API UClass* Z_Construct_UClass_ALaneManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AMapManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AMinionWaveManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator();
AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister();
AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGDistributionType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGGenerationPhase();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGMapAssetType();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGAssetReference();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationProgress();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGSpawnRule();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGGenerationPhase *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGGenerationPhase;
static UEnum* EPCGGenerationPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGGenerationPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGGenerationPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGGenerationPhase, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGGenerationPhase"));
	}
	return Z_Registration_Info_UEnum_EPCGGenerationPhase.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGGenerationPhase>()
{
	return EPCGGenerationPhase_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== ENUMS =====\n" },
#endif
		{ "Completed.DisplayName", "Conclu\xc3\xad""do" },
		{ "Completed.Name", "EPCGGenerationPhase::Completed" },
		{ "Finalization.DisplayName", "Finaliza\xc3\xa7\xc3\xa3o" },
		{ "Finalization.Name", "EPCGGenerationPhase::Finalization" },
		{ "Initialization.DisplayName", "Inicializa\xc3\xa7\xc3\xa3o" },
		{ "Initialization.Name", "EPCGGenerationPhase::Initialization" },
		{ "Lanes.DisplayName", "Lanes" },
		{ "Lanes.Name", "EPCGGenerationPhase::Lanes" },
		{ "Lighting.DisplayName", "Ilumina\xc3\xa7\xc3\xa3o" },
		{ "Lighting.Name", "EPCGGenerationPhase::Lighting" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "None.DisplayName", "Nenhuma" },
		{ "None.Name", "EPCGGenerationPhase::None" },
		{ "Objectives.DisplayName", "Objetivos" },
		{ "Objectives.Name", "EPCGGenerationPhase::Objectives" },
		{ "Props.DisplayName", "Props" },
		{ "Props.Name", "EPCGGenerationPhase::Props" },
		{ "River.DisplayName", "Rio" },
		{ "River.Name", "EPCGGenerationPhase::River" },
		{ "Terrain.DisplayName", "Terreno" },
		{ "Terrain.Name", "EPCGGenerationPhase::Terrain" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== ENUMS =====" },
#endif
		{ "Vegetation.DisplayName", "Vegeta\xc3\xa7\xc3\xa3o" },
		{ "Vegetation.Name", "EPCGGenerationPhase::Vegetation" },
		{ "Walls.DisplayName", "Paredes" },
		{ "Walls.Name", "EPCGGenerationPhase::Walls" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGGenerationPhase::None", (int64)EPCGGenerationPhase::None },
		{ "EPCGGenerationPhase::Initialization", (int64)EPCGGenerationPhase::Initialization },
		{ "EPCGGenerationPhase::Terrain", (int64)EPCGGenerationPhase::Terrain },
		{ "EPCGGenerationPhase::Lanes", (int64)EPCGGenerationPhase::Lanes },
		{ "EPCGGenerationPhase::Objectives", (int64)EPCGGenerationPhase::Objectives },
		{ "EPCGGenerationPhase::Walls", (int64)EPCGGenerationPhase::Walls },
		{ "EPCGGenerationPhase::River", (int64)EPCGGenerationPhase::River },
		{ "EPCGGenerationPhase::Vegetation", (int64)EPCGGenerationPhase::Vegetation },
		{ "EPCGGenerationPhase::Props", (int64)EPCGGenerationPhase::Props },
		{ "EPCGGenerationPhase::Lighting", (int64)EPCGGenerationPhase::Lighting },
		{ "EPCGGenerationPhase::Finalization", (int64)EPCGGenerationPhase::Finalization },
		{ "EPCGGenerationPhase::Completed", (int64)EPCGGenerationPhase::Completed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGGenerationPhase",
	"EPCGGenerationPhase",
	Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGGenerationPhase()
{
	if (!Z_Registration_Info_UEnum_EPCGGenerationPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGGenerationPhase.InnerSingleton, Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGGenerationPhase.InnerSingleton;
}
// ********** End Enum EPCGGenerationPhase *********************************************************

// ********** Begin Enum EPCGMapAssetType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGMapAssetType;
static UEnum* EPCGMapAssetType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGMapAssetType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGMapAssetType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGMapAssetType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGMapAssetType"));
	}
	return Z_Registration_Info_UEnum_EPCGMapAssetType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGMapAssetType>()
{
	return EPCGMapAssetType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Audio.DisplayName", "Audio" },
		{ "Audio.Name", "EPCGMapAssetType::Audio" },
		{ "Blueprint.DisplayName", "Blueprint" },
		{ "Blueprint.Name", "EPCGMapAssetType::Blueprint" },
		{ "BlueprintType", "true" },
		{ "Foliage.DisplayName", "Foliage" },
		{ "Foliage.Name", "EPCGMapAssetType::Foliage" },
		{ "Landscape.DisplayName", "Landscape" },
		{ "Landscape.Name", "EPCGMapAssetType::Landscape" },
		{ "Material.DisplayName", "Material" },
		{ "Material.Name", "EPCGMapAssetType::Material" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "Particle.DisplayName", "Particle System" },
		{ "Particle.Name", "EPCGMapAssetType::Particle" },
		{ "SkeletalMesh.DisplayName", "Skeletal Mesh" },
		{ "SkeletalMesh.Name", "EPCGMapAssetType::SkeletalMesh" },
		{ "StaticMesh.DisplayName", "Static Mesh" },
		{ "StaticMesh.Name", "EPCGMapAssetType::StaticMesh" },
		{ "Texture.DisplayName", "Texture" },
		{ "Texture.Name", "EPCGMapAssetType::Texture" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGMapAssetType::StaticMesh", (int64)EPCGMapAssetType::StaticMesh },
		{ "EPCGMapAssetType::SkeletalMesh", (int64)EPCGMapAssetType::SkeletalMesh },
		{ "EPCGMapAssetType::Material", (int64)EPCGMapAssetType::Material },
		{ "EPCGMapAssetType::Texture", (int64)EPCGMapAssetType::Texture },
		{ "EPCGMapAssetType::Landscape", (int64)EPCGMapAssetType::Landscape },
		{ "EPCGMapAssetType::Foliage", (int64)EPCGMapAssetType::Foliage },
		{ "EPCGMapAssetType::Particle", (int64)EPCGMapAssetType::Particle },
		{ "EPCGMapAssetType::Audio", (int64)EPCGMapAssetType::Audio },
		{ "EPCGMapAssetType::Blueprint", (int64)EPCGMapAssetType::Blueprint },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGMapAssetType",
	"EPCGMapAssetType",
	Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGMapAssetType()
{
	if (!Z_Registration_Info_UEnum_EPCGMapAssetType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGMapAssetType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGMapAssetType.InnerSingleton;
}
// ********** End Enum EPCGMapAssetType ************************************************************

// ********** Begin Enum EPCGDistributionType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGDistributionType;
static UEnum* EPCGDistributionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGDistributionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGDistributionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGDistributionType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGDistributionType"));
	}
	return Z_Registration_Info_UEnum_EPCGDistributionType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGDistributionType>()
{
	return EPCGDistributionType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGDistributionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Clustered.DisplayName", "Agrupado" },
		{ "Clustered.Name", "EPCGDistributionType::Clustered" },
		{ "Geometric.DisplayName", "Geom\xc3\xa9trico" },
		{ "Geometric.Name", "EPCGDistributionType::Geometric" },
		{ "Grid.DisplayName", "Grade" },
		{ "Grid.Name", "EPCGDistributionType::Grid" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "Poisson.DisplayName", "Poisson Disk" },
		{ "Poisson.Name", "EPCGDistributionType::Poisson" },
		{ "Random.DisplayName", "Aleat\xc3\xb3rio" },
		{ "Random.Name", "EPCGDistributionType::Random" },
		{ "Weighted.DisplayName", "Ponderado" },
		{ "Weighted.Name", "EPCGDistributionType::Weighted" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGDistributionType::Random", (int64)EPCGDistributionType::Random },
		{ "EPCGDistributionType::Grid", (int64)EPCGDistributionType::Grid },
		{ "EPCGDistributionType::Poisson", (int64)EPCGDistributionType::Poisson },
		{ "EPCGDistributionType::Geometric", (int64)EPCGDistributionType::Geometric },
		{ "EPCGDistributionType::Weighted", (int64)EPCGDistributionType::Weighted },
		{ "EPCGDistributionType::Clustered", (int64)EPCGDistributionType::Clustered },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGDistributionType",
	"EPCGDistributionType",
	Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGDistributionType()
{
	if (!Z_Registration_Info_UEnum_EPCGDistributionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGDistributionType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGDistributionType.InnerSingleton;
}
// ********** End Enum EPCGDistributionType ********************************************************

// ********** Begin ScriptStruct FPCGAssetReference ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGAssetReference;
class UScriptStruct* FPCGAssetReference::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGAssetReference.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGAssetReference.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGAssetReference, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGAssetReference"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGAssetReference.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGAssetReference_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== ESTRUTURAS DE DADOS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== ESTRUTURAS DE DADOS =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetType_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPath_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetName_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weight_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomRotation_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomScale_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleRange_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AssetType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AssetType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetPath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static void NewProp_bRandomRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomRotation;
	static void NewProp_bRandomScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGAssetReference>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType = { "AssetType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetType), Z_Construct_UEnum_Aura_EPCGMapAssetType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetType_MetaData), NewProp_AssetType_MetaData) }; // 2580812996
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetPath = { "AssetPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPath_MetaData), NewProp_AssetPath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetName = { "AssetName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetName_MetaData), NewProp_AssetName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Weight = { "Weight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, Weight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weight_MetaData), NewProp_Weight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
void Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation_SetBit(void* Obj)
{
	((FPCGAssetReference*)Obj)->bRandomRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation = { "bRandomRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGAssetReference), &Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomRotation_MetaData), NewProp_bRandomRotation_MetaData) };
void Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale_SetBit(void* Obj)
{
	((FPCGAssetReference*)Obj)->bRandomScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale = { "bRandomScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGAssetReference), &Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomScale_MetaData), NewProp_bRandomScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_ScaleRange = { "ScaleRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, ScaleRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleRange_MetaData), NewProp_ScaleRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Weight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_ScaleRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGAssetReference",
	Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers),
	sizeof(FPCGAssetReference),
	alignof(FPCGAssetReference),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAssetReference_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGAssetReference_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGAssetReference()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGAssetReference.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGAssetReference.InnerSingleton, Z_Construct_UScriptStruct_FPCGAssetReference_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGAssetReference.InnerSingleton;
}
// ********** End ScriptStruct FPCGAssetReference **************************************************

// ********** Begin ScriptStruct FPCGSpawnRule *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGSpawnRule;
class UScriptStruct* FPCGSpawnRule::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGSpawnRule.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGSpawnRule.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGSpawnRule, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGSpawnRule"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGSpawnRule.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGSpawnRule_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleName_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistributionType_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Assets_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDistance_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDistance_MetaData[] = {
		{ "Category", "Spawn Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 1 metro\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "1 metro" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightRange_MetaData[] = {
		{ "Category", "Spawn Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 5 metros\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "5 metros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeRange_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToSurface_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAvoidOverlap_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRespectBounds_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusionTags_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistributionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistributionType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Assets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Assets;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlopeRange;
	static void NewProp_bAlignToSurface_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToSurface;
	static void NewProp_bAvoidOverlap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvoidOverlap;
	static void NewProp_bRespectBounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRespectBounds;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExclusionTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExclusionTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGSpawnRule>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RuleName = { "RuleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, RuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleName_MetaData), NewProp_RuleName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType = { "DistributionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, DistributionType), Z_Construct_UEnum_Aura_EPCGDistributionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistributionType_MetaData), NewProp_DistributionType_MetaData) }; // 103216633
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets_Inner = { "Assets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGAssetReference, METADATA_PARAMS(0, nullptr) }; // 3234866593
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets = { "Assets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, Assets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Assets_MetaData), NewProp_Assets_MetaData) }; // 3234866593
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MinDistance = { "MinDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, MinDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDistance_MetaData), NewProp_MinDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MaxDistance = { "MaxDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, MaxDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDistance_MetaData), NewProp_MaxDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_HeightRange = { "HeightRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, HeightRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightRange_MetaData), NewProp_HeightRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_SlopeRange = { "SlopeRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, SlopeRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeRange_MetaData), NewProp_SlopeRange_MetaData) };
void Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface_SetBit(void* Obj)
{
	((FPCGSpawnRule*)Obj)->bAlignToSurface = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface = { "bAlignToSurface", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGSpawnRule), &Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToSurface_MetaData), NewProp_bAlignToSurface_MetaData) };
void Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap_SetBit(void* Obj)
{
	((FPCGSpawnRule*)Obj)->bAvoidOverlap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap = { "bAvoidOverlap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGSpawnRule), &Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAvoidOverlap_MetaData), NewProp_bAvoidOverlap_MetaData) };
void Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds_SetBit(void* Obj)
{
	((FPCGSpawnRule*)Obj)->bRespectBounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds = { "bRespectBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGSpawnRule), &Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRespectBounds_MetaData), NewProp_bRespectBounds_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags_Inner = { "ExclusionTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags = { "ExclusionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, ExclusionTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusionTags_MetaData), NewProp_ExclusionTags_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags_Inner = { "RequiredTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, RequiredTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MinDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MaxDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_HeightRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_SlopeRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGSpawnRule",
	Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers),
	sizeof(FPCGSpawnRule),
	alignof(FPCGSpawnRule),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGSpawnRule()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGSpawnRule.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGSpawnRule.InnerSingleton, Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGSpawnRule.InnerSingleton;
}
// ********** End ScriptStruct FPCGSpawnRule *******************************************************

// ********** Begin ScriptStruct FPCGGenerationConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGGenerationConfig;
class UScriptStruct* FPCGGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGGenerationConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutoGeneration_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRealTimeUpdates_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationSeed_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxIterations_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityThreshold_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultithreading_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadCount_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSaveGenerationData_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputDirectory_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableAutoGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutoGeneration;
	static void NewProp_bEnableRealTimeUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRealTimeUpdates;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationSeed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxIterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityThreshold;
	static void NewProp_bUseMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bSaveGenerationData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSaveGenerationData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputDirectory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bEnableAutoGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration = { "bEnableAutoGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutoGeneration_MetaData), NewProp_bEnableAutoGeneration_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bEnableRealTimeUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates = { "bEnableRealTimeUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRealTimeUpdates_MetaData), NewProp_bEnableRealTimeUpdates_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_GenerationSeed = { "GenerationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, GenerationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationSeed_MetaData), NewProp_GenerationSeed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_MaxIterations = { "MaxIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, MaxIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxIterations_MetaData), NewProp_MaxIterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_QualityThreshold = { "QualityThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, QualityThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityThreshold_MetaData), NewProp_QualityThreshold_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bUseMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading = { "bUseMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultithreading_MetaData), NewProp_bUseMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, ThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadCount_MetaData), NewProp_ThreadCount_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bSaveGenerationData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData = { "bSaveGenerationData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSaveGenerationData_MetaData), NewProp_bSaveGenerationData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_OutputDirectory = { "OutputDirectory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, OutputDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputDirectory_MetaData), NewProp_OutputDirectory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_GenerationSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_MaxIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_QualityThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_ThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_OutputDirectory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGGenerationConfig",
	Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers),
	sizeof(FPCGGenerationConfig),
	alignof(FPCGGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGGenerationConfig ************************************************

// ********** Begin ScriptStruct FPCGGenerationProgress ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGGenerationProgress;
class UScriptStruct* FPCGGenerationProgress::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGGenerationProgress, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGGenerationProgress"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverallProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedAssets_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalAssets_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedEndTime_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentOperation_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedPhases_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessages_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OverallProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GeneratedAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalAssets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EstimatedEndTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentOperation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletedPhases_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedPhases;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrorMessages;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGGenerationProgress>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, CurrentPhase), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 2886374832
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_PhaseProgress = { "PhaseProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, PhaseProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseProgress_MetaData), NewProp_PhaseProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_OverallProgress = { "OverallProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, OverallProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverallProgress_MetaData), NewProp_OverallProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_GeneratedAssets = { "GeneratedAssets", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, GeneratedAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedAssets_MetaData), NewProp_GeneratedAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_TotalAssets = { "TotalAssets", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, TotalAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalAssets_MetaData), NewProp_TotalAssets_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, StartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_EstimatedEndTime = { "EstimatedEndTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, EstimatedEndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedEndTime_MetaData), NewProp_EstimatedEndTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentOperation = { "CurrentOperation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, CurrentOperation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentOperation_MetaData), NewProp_CurrentOperation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases_Inner = { "CompletedPhases", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases = { "CompletedPhases", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, CompletedPhases), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedPhases_MetaData), NewProp_CompletedPhases_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages_Inner = { "ErrorMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages = { "ErrorMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, ErrorMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessages_MetaData), NewProp_ErrorMessages_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_PhaseProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_OverallProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_GeneratedAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_TotalAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_EstimatedEndTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGGenerationProgress",
	Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers),
	sizeof(FPCGGenerationProgress),
	alignof(FPCGGenerationProgress),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationProgress()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.InnerSingleton, Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.InnerSingleton;
}
// ********** End ScriptStruct FPCGGenerationProgress **********************************************

// ********** Begin Delegate FOnPCGPhaseChanged ****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPCGPhaseChanged_Parms
	{
		EPCGGenerationPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== DELEGATES =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== DELEGATES =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 2886374832
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGPhaseChanged__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::_Script_Aura_eventOnPCGPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::_Script_Aura_eventOnPCGPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPCGPhaseChanged, EPCGGenerationPhase NewPhase)
{
	struct _Script_Aura_eventOnPCGPhaseChanged_Parms
	{
		EPCGGenerationPhase NewPhase;
	};
	_Script_Aura_eventOnPCGPhaseChanged_Parms Parms;
	Parms.NewPhase=NewPhase;
	OnPCGPhaseChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGPhaseChanged ******************************************************

// ********** Begin Delegate FOnPCGProgressUpdated *************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPCGProgressUpdated_Parms
	{
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGProgressUpdated_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGProgressUpdated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::_Script_Aura_eventOnPCGProgressUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::_Script_Aura_eventOnPCGProgressUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGProgressUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnPCGProgressUpdated, float Progress)
{
	struct _Script_Aura_eventOnPCGProgressUpdated_Parms
	{
		float Progress;
	};
	_Script_Aura_eventOnPCGProgressUpdated_Parms Parms;
	Parms.Progress=Progress;
	OnPCGProgressUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGProgressUpdated ***************************************************

// ********** Begin Delegate FOnPCGGenerationCompleted *********************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGGenerationCompleted__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationCompleted)
{
	OnPCGGenerationCompleted.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnPCGGenerationCompleted ***********************************************

// ********** Begin Delegate FOnPCGGenerationFailed ************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPCGGenerationFailed_Parms
	{
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGGenerationFailed_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGGenerationFailed__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::_Script_Aura_eventOnPCGGenerationFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::_Script_Aura_eventOnPCGGenerationFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGGenerationFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationFailed, const FString& ErrorMessage)
{
	struct _Script_Aura_eventOnPCGGenerationFailed_Parms
	{
		FString ErrorMessage;
	};
	_Script_Aura_eventOnPCGGenerationFailed_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	OnPCGGenerationFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGGenerationFailed **************************************************

// ********** Begin Delegate FOnPCGAssetGenerated **************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPCGAssetGenerated_Parms
	{
		FString AssetName;
		int32 Count;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_AssetName = { "AssetName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGAssetGenerated_Parms, AssetName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetName_MetaData), NewProp_AssetName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGAssetGenerated_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_AssetName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_Count,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGAssetGenerated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::_Script_Aura_eventOnPCGAssetGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::_Script_Aura_eventOnPCGAssetGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGAssetGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnPCGAssetGenerated, const FString& AssetName, int32 Count)
{
	struct _Script_Aura_eventOnPCGAssetGenerated_Parms
	{
		FString AssetName;
		int32 Count;
	};
	_Script_Aura_eventOnPCGAssetGenerated_Parms Parms;
	Parms.AssetName=AssetName;
	Parms.Count=Count;
	OnPCGAssetGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGAssetGenerated ****************************************************

// ********** Begin Class AProceduralMapGenerator Function AddSpawnRule ****************************
struct Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics
{
	struct ProceduralMapGenerator_eventAddSpawnRule_Parms
	{
		FPCGSpawnRule NewRule;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewRule_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewRule;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::NewProp_NewRule = { "NewRule", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventAddSpawnRule_Parms, NewRule), Z_Construct_UScriptStruct_FPCGSpawnRule, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewRule_MetaData), NewProp_NewRule_MetaData) }; // 931756877
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::NewProp_NewRule,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "AddSpawnRule", Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::ProceduralMapGenerator_eventAddSpawnRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::ProceduralMapGenerator_eventAddSpawnRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execAddSpawnRule)
{
	P_GET_STRUCT_REF(FPCGSpawnRule,Z_Param_Out_NewRule);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddSpawnRule(Z_Param_Out_NewRule);
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function AddSpawnRule ******************************

// ********** Begin Class AProceduralMapGenerator Function ClearGeneration *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ClearGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execClearGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ClearGeneration ***************************

// ********** Begin Class AProceduralMapGenerator Function ClearSpawnRules *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ClearSpawnRules", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execClearSpawnRules)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearSpawnRules();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ClearSpawnRules ***************************

// ********** Begin Class AProceduralMapGenerator Function GetCurrentPhase *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics
{
	struct ProceduralMapGenerator_eventGetCurrentPhase_Parms
	{
		EPCGGenerationPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetCurrentPhase_Parms, ReturnValue), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 2886374832
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetCurrentPhase", Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::ProceduralMapGenerator_eventGetCurrentPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::ProceduralMapGenerator_eventGetCurrentPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetCurrentPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EPCGGenerationPhase*)Z_Param__Result=P_THIS->GetCurrentPhase();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetCurrentPhase ***************************

// ********** Begin Class AProceduralMapGenerator Function GetGeneratedAssetCount ******************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics
{
	struct ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetGeneratedAssetCount", Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetGeneratedAssetCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetGeneratedAssetCount();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetGeneratedAssetCount ********************

// ********** Begin Class AProceduralMapGenerator Function GetGenerationConfig *********************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics
{
	struct ProceduralMapGenerator_eventGetGenerationConfig_Parms
	{
		FPCGGenerationConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetGenerationConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGGenerationConfig, METADATA_PARAMS(0, nullptr) }; // 702549597
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetGenerationConfig", Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::ProceduralMapGenerator_eventGetGenerationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::ProceduralMapGenerator_eventGetGenerationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetGenerationConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGGenerationConfig*)Z_Param__Result=P_THIS->GetGenerationConfig();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetGenerationConfig ***********************

// ********** Begin Class AProceduralMapGenerator Function GetGenerationProgress *******************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics
{
	struct ProceduralMapGenerator_eventGetGenerationProgress_Parms
	{
		FPCGGenerationProgress ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES DE ESTADO =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES DE ESTADO =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetGenerationProgress_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGGenerationProgress, METADATA_PARAMS(0, nullptr) }; // 3844196119
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetGenerationProgress", Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::ProceduralMapGenerator_eventGetGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::ProceduralMapGenerator_eventGetGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetGenerationProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGGenerationProgress*)Z_Param__Result=P_THIS->GetGenerationProgress();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetGenerationProgress *********************

// ********** Begin Class AProceduralMapGenerator Function GetOverallProgress **********************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics
{
	struct ProceduralMapGenerator_eventGetOverallProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetOverallProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetOverallProgress", Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::ProceduralMapGenerator_eventGetOverallProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::ProceduralMapGenerator_eventGetOverallProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetOverallProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetOverallProgress();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetOverallProgress ************************

// ********** Begin Class AProceduralMapGenerator Function InitializeManagers **********************
struct Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES DE INTEGRA\xc3\x87\xc3\x83O =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES DE INTEGRA\xc3\x87\xc3\x83O =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "InitializeManagers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execInitializeManagers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeManagers();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function InitializeManagers ************************

// ********** Begin Class AProceduralMapGenerator Function IsGenerating ****************************
struct Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics
{
	struct ProceduralMapGenerator_eventIsGenerating_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralMapGenerator_eventIsGenerating_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralMapGenerator_eventIsGenerating_Parms), &Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "IsGenerating", Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::ProceduralMapGenerator_eventIsGenerating_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::ProceduralMapGenerator_eventIsGenerating_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execIsGenerating)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGenerating();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function IsGenerating ******************************

// ********** Begin Class AProceduralMapGenerator Function IsPaused ********************************
struct Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics
{
	struct ProceduralMapGenerator_eventIsPaused_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralMapGenerator_eventIsPaused_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralMapGenerator_eventIsPaused_Parms), &Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "IsPaused", Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::ProceduralMapGenerator_eventIsPaused_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::ProceduralMapGenerator_eventIsPaused_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_IsPaused()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execIsPaused)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPaused();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function IsPaused **********************************

// ********** Begin Class AProceduralMapGenerator Function PauseGeneration *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "PauseGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execPauseGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function PauseGeneration ***************************

// ********** Begin Class AProceduralMapGenerator Function RemoveSpawnRule *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics
{
	struct ProceduralMapGenerator_eventRemoveSpawnRule_Parms
	{
		FString RuleName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::NewProp_RuleName = { "RuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventRemoveSpawnRule_Parms, RuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleName_MetaData), NewProp_RuleName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::NewProp_RuleName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "RemoveSpawnRule", Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::ProceduralMapGenerator_eventRemoveSpawnRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::ProceduralMapGenerator_eventRemoveSpawnRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execRemoveSpawnRule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveSpawnRule(Z_Param_RuleName);
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function RemoveSpawnRule ***************************

// ********** Begin Class AProceduralMapGenerator Function RestartGeneration ***********************
struct Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "RestartGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execRestartGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RestartGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function RestartGeneration *************************

// ********** Begin Class AProceduralMapGenerator Function ResumeGeneration ************************
struct Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ResumeGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execResumeGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ResumeGeneration **************************

// ********** Begin Class AProceduralMapGenerator Function SetGenerationConfig *********************
struct Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics
{
	struct ProceduralMapGenerator_eventSetGenerationConfig_Parms
	{
		FPCGGenerationConfig NewConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES DE CONFIGURA\xc3\x87\xc3\x83O =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES DE CONFIGURA\xc3\x87\xc3\x83O =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventSetGenerationConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FPCGGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 702549597
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::NewProp_NewConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "SetGenerationConfig", Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::ProceduralMapGenerator_eventSetGenerationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::ProceduralMapGenerator_eventSetGenerationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execSetGenerationConfig)
{
	P_GET_STRUCT_REF(FPCGGenerationConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGenerationConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function SetGenerationConfig ***********************

// ********** Begin Class AProceduralMapGenerator Function StartGeneration *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES PRINCIPAIS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES PRINCIPAIS =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "StartGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execStartGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function StartGeneration ***************************

// ********** Begin Class AProceduralMapGenerator Function StopGeneration **************************
struct Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "StopGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execStopGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function StopGeneration ****************************

// ********** Begin Class AProceduralMapGenerator Function SynchronizeWithMapManager ***************
struct Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "SynchronizeWithMapManager", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execSynchronizeWithMapManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithMapManager();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function SynchronizeWithMapManager *****************

// ********** Begin Class AProceduralMapGenerator Function UpdatePCGFromGeometry *******************
struct Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "UpdatePCGFromGeometry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execUpdatePCGFromGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePCGFromGeometry();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function UpdatePCGFromGeometry *********************

// ********** Begin Class AProceduralMapGenerator Function ValidateManagerReferences ***************
struct Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ValidateManagerReferences", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execValidateManagerReferences)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateManagerReferences();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ValidateManagerReferences *****************

// ********** Begin Class AProceduralMapGenerator **************************************************
void AProceduralMapGenerator::StaticRegisterNativesAProceduralMapGenerator()
{
	UClass* Class = AProceduralMapGenerator::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddSpawnRule", &AProceduralMapGenerator::execAddSpawnRule },
		{ "ClearGeneration", &AProceduralMapGenerator::execClearGeneration },
		{ "ClearSpawnRules", &AProceduralMapGenerator::execClearSpawnRules },
		{ "GetCurrentPhase", &AProceduralMapGenerator::execGetCurrentPhase },
		{ "GetGeneratedAssetCount", &AProceduralMapGenerator::execGetGeneratedAssetCount },
		{ "GetGenerationConfig", &AProceduralMapGenerator::execGetGenerationConfig },
		{ "GetGenerationProgress", &AProceduralMapGenerator::execGetGenerationProgress },
		{ "GetOverallProgress", &AProceduralMapGenerator::execGetOverallProgress },
		{ "InitializeManagers", &AProceduralMapGenerator::execInitializeManagers },
		{ "IsGenerating", &AProceduralMapGenerator::execIsGenerating },
		{ "IsPaused", &AProceduralMapGenerator::execIsPaused },
		{ "PauseGeneration", &AProceduralMapGenerator::execPauseGeneration },
		{ "RemoveSpawnRule", &AProceduralMapGenerator::execRemoveSpawnRule },
		{ "RestartGeneration", &AProceduralMapGenerator::execRestartGeneration },
		{ "ResumeGeneration", &AProceduralMapGenerator::execResumeGeneration },
		{ "SetGenerationConfig", &AProceduralMapGenerator::execSetGenerationConfig },
		{ "StartGeneration", &AProceduralMapGenerator::execStartGeneration },
		{ "StopGeneration", &AProceduralMapGenerator::execStopGeneration },
		{ "SynchronizeWithMapManager", &AProceduralMapGenerator::execSynchronizeWithMapManager },
		{ "UpdatePCGFromGeometry", &AProceduralMapGenerator::execUpdatePCGFromGeometry },
		{ "ValidateManagerReferences", &AProceduralMapGenerator::execValidateManagerReferences },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AProceduralMapGenerator;
UClass* AProceduralMapGenerator::GetPrivateStaticClass()
{
	using TClass = AProceduralMapGenerator;
	if (!Z_Registration_Info_UClass_AProceduralMapGenerator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("ProceduralMapGenerator"),
			Z_Registration_Info_UClass_AProceduralMapGenerator.InnerSingleton,
			StaticRegisterNativesAProceduralMapGenerator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AProceduralMapGenerator.InnerSingleton;
}
UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister()
{
	return AProceduralMapGenerator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AProceduralMapGenerator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== CLASSE PRINCIPAL =====\n" },
#endif
		{ "IncludePath", "AProceduralMapGenerator.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== CLASSE PRINCIPAL =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== COMPONENTES =====\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== COMPONENTES =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoundsVisualization_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationConfig_MetaData[] = {
		{ "Category", "PCG Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== CONFIGURA\xc3\x87\xc3\x83O =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== CONFIGURA\xc3\x87\xc3\x83O =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnRules_MetaData[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraphAsset_MetaData[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapBounds_MetaData[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapCenter_MetaData[] = {
		{ "Category", "PCG Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 50km x 50km x 2km\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "50km x 50km x 2km" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneManager_MetaData[] = {
		{ "Category", "Managers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== REFER\xc3\x8aNCIAS DOS MANAGERS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== REFER\xc3\x8aNCIAS DOS MANAGERS =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaronManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometricValidator_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationProgress_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== ESTADO =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== ESTADO =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerating_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPaused_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedActors_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhaseChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== DELEGATES =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== DELEGATES =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnProgressUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGenerationCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGenerationFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetGenerated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BoundsVisualization;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnRules_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnRules;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PCGGraphAsset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapBounds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapCenter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaronManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragonManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WallManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RiverManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MinionManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MapManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeometricValidator;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationProgress;
	static void NewProp_bIsGenerating_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerating;
	static void NewProp_bIsPaused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPaused;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedActors;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhaseChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnProgressUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGenerationCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGenerationFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetGenerated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule, "AddSpawnRule" }, // 1944940994
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration, "ClearGeneration" }, // 2552607104
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules, "ClearSpawnRules" }, // 3121219462
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase, "GetCurrentPhase" }, // 1992987361
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount, "GetGeneratedAssetCount" }, // 476861668
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig, "GetGenerationConfig" }, // 979441392
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress, "GetGenerationProgress" }, // 491872281
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress, "GetOverallProgress" }, // 144586471
		{ &Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers, "InitializeManagers" }, // 280277284
		{ &Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating, "IsGenerating" }, // 4122472116
		{ &Z_Construct_UFunction_AProceduralMapGenerator_IsPaused, "IsPaused" }, // 1963397404
		{ &Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration, "PauseGeneration" }, // 3575664076
		{ &Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule, "RemoveSpawnRule" }, // 6684046
		{ &Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration, "RestartGeneration" }, // 433070153
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration, "ResumeGeneration" }, // 2299003040
		{ &Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig, "SetGenerationConfig" }, // 60156516
		{ &Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration, "StartGeneration" }, // 1690989740
		{ &Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration, "StopGeneration" }, // 3329786590
		{ &Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager, "SynchronizeWithMapManager" }, // 3622304507
		{ &Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry, "UpdatePCGFromGeometry" }, // 2772828719
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences, "ValidateManagerReferences" }, // 1424320463
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AProceduralMapGenerator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BoundsVisualization = { "BoundsVisualization", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, BoundsVisualization), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoundsVisualization_MetaData), NewProp_BoundsVisualization_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationConfig = { "GenerationConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GenerationConfig), Z_Construct_UScriptStruct_FPCGGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationConfig_MetaData), NewProp_GenerationConfig_MetaData) }; // 702549597
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules_Inner = { "SpawnRules", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGSpawnRule, METADATA_PARAMS(0, nullptr) }; // 931756877
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules = { "SpawnRules", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, SpawnRules), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnRules_MetaData), NewProp_SpawnRules_MetaData) }; // 931756877
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGGraphAsset = { "PCGGraphAsset", nullptr, (EPropertyFlags)0x0024080000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, PCGGraphAsset), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraphAsset_MetaData), NewProp_PCGGraphAsset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapBounds = { "MapBounds", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MapBounds), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapBounds_MetaData), NewProp_MapBounds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapCenter = { "MapCenter", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MapCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapCenter_MetaData), NewProp_MapCenter_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_LaneManager = { "LaneManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, LaneManager), Z_Construct_UClass_ALaneManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneManager_MetaData), NewProp_LaneManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BaronManager = { "BaronManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, BaronManager), Z_Construct_UClass_ABaronAuracronManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaronManager_MetaData), NewProp_BaronManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_DragonManager = { "DragonManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, DragonManager), Z_Construct_UClass_ADragonPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonManager_MetaData), NewProp_DragonManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_WallManager = { "WallManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, WallManager), Z_Construct_UClass_AWallCollisionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallManager_MetaData), NewProp_WallManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RiverManager = { "RiverManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, RiverManager), Z_Construct_UClass_ARiverPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverManager_MetaData), NewProp_RiverManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MinionManager = { "MinionManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MinionManager), Z_Construct_UClass_AMinionWaveManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionManager_MetaData), NewProp_MinionManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapManager = { "MapManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MapManager), Z_Construct_UClass_AMapManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapManager_MetaData), NewProp_MapManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeometricValidator = { "GeometricValidator", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GeometricValidator), Z_Construct_UClass_AGeometricValidator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometricValidator_MetaData), NewProp_GeometricValidator_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationProgress = { "GenerationProgress", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GenerationProgress), Z_Construct_UScriptStruct_FPCGGenerationProgress, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationProgress_MetaData), NewProp_GenerationProgress_MetaData) }; // 3844196119
void Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating_SetBit(void* Obj)
{
	((AProceduralMapGenerator*)Obj)->bIsGenerating = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating = { "bIsGenerating", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AProceduralMapGenerator), &Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerating_MetaData), NewProp_bIsGenerating_MetaData) };
void Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused_SetBit(void* Obj)
{
	((AProceduralMapGenerator*)Obj)->bIsPaused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused = { "bIsPaused", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AProceduralMapGenerator), &Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPaused_MetaData), NewProp_bIsPaused_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors_Inner = { "GeneratedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors = { "GeneratedActors", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GeneratedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedActors_MetaData), NewProp_GeneratedActors_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnPhaseChanged = { "OnPhaseChanged", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnPhaseChanged), Z_Construct_UDelegateFunction_Aura_OnPCGPhaseChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhaseChanged_MetaData), NewProp_OnPhaseChanged_MetaData) }; // 1933990356
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnProgressUpdated = { "OnProgressUpdated", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnProgressUpdated), Z_Construct_UDelegateFunction_Aura_OnPCGProgressUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnProgressUpdated_MetaData), NewProp_OnProgressUpdated_MetaData) }; // 1168704590
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationCompleted = { "OnGenerationCompleted", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnGenerationCompleted), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGenerationCompleted_MetaData), NewProp_OnGenerationCompleted_MetaData) }; // 1403524541
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationFailed = { "OnGenerationFailed", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnGenerationFailed), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGenerationFailed_MetaData), NewProp_OnGenerationFailed_MetaData) }; // 4128819394
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnAssetGenerated = { "OnAssetGenerated", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnAssetGenerated), Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetGenerated_MetaData), NewProp_OnAssetGenerated_MetaData) }; // 3017971507
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BoundsVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGGraphAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_LaneManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BaronManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_DragonManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_WallManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RiverManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MinionManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeometricValidator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnPhaseChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnProgressUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnAssetGenerated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AProceduralMapGenerator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AProceduralMapGenerator_Statics::ClassParams = {
	&AProceduralMapGenerator::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::Class_MetaDataParams), Z_Construct_UClass_AProceduralMapGenerator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AProceduralMapGenerator()
{
	if (!Z_Registration_Info_UClass_AProceduralMapGenerator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AProceduralMapGenerator.OuterSingleton, Z_Construct_UClass_AProceduralMapGenerator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AProceduralMapGenerator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AProceduralMapGenerator);
AProceduralMapGenerator::~AProceduralMapGenerator() {}
// ********** End Class AProceduralMapGenerator ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGGenerationPhase_StaticEnum, TEXT("EPCGGenerationPhase"), &Z_Registration_Info_UEnum_EPCGGenerationPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2886374832U) },
		{ EPCGMapAssetType_StaticEnum, TEXT("EPCGMapAssetType"), &Z_Registration_Info_UEnum_EPCGMapAssetType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2580812996U) },
		{ EPCGDistributionType_StaticEnum, TEXT("EPCGDistributionType"), &Z_Registration_Info_UEnum_EPCGDistributionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 103216633U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGAssetReference::StaticStruct, Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewStructOps, TEXT("PCGAssetReference"), &Z_Registration_Info_UScriptStruct_FPCGAssetReference, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGAssetReference), 3234866593U) },
		{ FPCGSpawnRule::StaticStruct, Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewStructOps, TEXT("PCGSpawnRule"), &Z_Registration_Info_UScriptStruct_FPCGSpawnRule, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGSpawnRule), 931756877U) },
		{ FPCGGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewStructOps, TEXT("PCGGenerationConfig"), &Z_Registration_Info_UScriptStruct_FPCGGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGGenerationConfig), 702549597U) },
		{ FPCGGenerationProgress::StaticStruct, Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewStructOps, TEXT("PCGGenerationProgress"), &Z_Registration_Info_UScriptStruct_FPCGGenerationProgress, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGGenerationProgress), 3844196119U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AProceduralMapGenerator, AProceduralMapGenerator::StaticClass, TEXT("AProceduralMapGenerator"), &Z_Registration_Info_UClass_AProceduralMapGenerator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AProceduralMapGenerator), 2432436086U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_3025248516(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
