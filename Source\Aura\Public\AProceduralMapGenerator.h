// AProceduralMapGenerator.h
// Sistema de PCG (Procedural Content Generation) para geração automática do mapa
// Integra com UE5.6 PCG Framework e todos os managers do sistema
// Escala: 1 UU = 1 cm

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Materials/MaterialInterface.h"
#include "Engine/StaticMesh.h"
#include "Runtime/Landscape/Classes/Landscape.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGNode.h"
#include "Elements/PCGSpawnActor.h"
#include "Elements/PCGStaticMeshSpawner.h"
#include "Data/PCGPointData.h"
#include "Data/PCGSpatialData.h"
#include "Helpers/PCGHelpers.h"
#include "ALaneManager.h"
#include "ABaronAuracronManager.h"
#include "ADragonPrismalManager.h"
#include "AWallCollisionManager.h"
#include "ARiverPrismalManager.h"
#include "AMinionWaveManager.h"
#include "AMapManager.h"
#include "AGeometricValidator.h"
#include "implementacao_automatizada.h"
#include "AWallCollisionManager.h"
#include "Interfaces/LaneSystemInterface.h"
#include "Interfaces/RiverSystemInterface.h"
#include "AProceduralMapGenerator.generated.h"

// ===== ENUMS =====

UENUM(BlueprintType)
enum class EPCGGenerationPhase : uint8
{
    None            UMETA(DisplayName = "Nenhuma"),
    Initialization  UMETA(DisplayName = "Inicialização"),
    Terrain         UMETA(DisplayName = "Terreno"),
    Lanes           UMETA(DisplayName = "Lanes"),
    Objectives      UMETA(DisplayName = "Objetivos"),
    Walls           UMETA(DisplayName = "Paredes"),
    River           UMETA(DisplayName = "Rio"),
    Vegetation      UMETA(DisplayName = "Vegetação"),
    Props           UMETA(DisplayName = "Props"),
    Lighting        UMETA(DisplayName = "Iluminação"),
    Finalization    UMETA(DisplayName = "Finalização"),
    Completed       UMETA(DisplayName = "Concluído")
};

UENUM(BlueprintType)
enum class EObjectiveType : uint8
{
    Nexus           UMETA(DisplayName = "Nexus"),
    Base            UMETA(DisplayName = "Base"),
    Tower           UMETA(DisplayName = "Torre"),
    Outpost         UMETA(DisplayName = "Posto Avançado")
};



UENUM(BlueprintType)
enum class EPropType : uint8
{
    Rock            UMETA(DisplayName = "Rocha"),
    Crate           UMETA(DisplayName = "Caixa"),
    Barrel          UMETA(DisplayName = "Barril"),
    Pillar          UMETA(DisplayName = "Pilar"),
    Statue          UMETA(DisplayName = "Estátua"),
    Barricade       UMETA(DisplayName = "Barricada"),
    WatchTower      UMETA(DisplayName = "Torre de Vigia")
};

// Estrutura de progresso de geração
USTRUCT(BlueprintType)
struct AURA_API FGenerationProgress
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool TerrainGenerated = false;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool LanesGenerated = false;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool ObjectivesGenerated = false;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool WallsGenerated = false;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool RiverGenerated = false;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool VegetationGenerated = false;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool PropsGenerated = false;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    bool LightingGenerated = false;

    FGenerationProgress()
    {
        TerrainGenerated = false;
        LanesGenerated = false;
        ObjectivesGenerated = false;
        WallsGenerated = false;
        RiverGenerated = false;
        VegetationGenerated = false;
        PropsGenerated = false;
        LightingGenerated = false;
    }
};

// Estrutura para zonas de exclusão
USTRUCT(BlueprintType)
struct AURA_API FExclusionZone
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusion Zone")
    FVector Center;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusion Zone")
    float Radius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusion Zone")
    bool bExcludeVegetation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Exclusion Zone")
    bool bExcludeProps;

    FExclusionZone()
    {
        Center = FVector::ZeroVector;
        Radius = 500.0f;
        bExcludeVegetation = true;
        bExcludeProps = true;
    }
};

// Estrutura para configuração de terreno
USTRUCT(BlueprintType)
struct AURA_API FTerrainGenerationConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrain")
    float HeightVariation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrain")
    float NoiseScale;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrain")
    int32 NoiseOctaves;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Terrain")
    float NoisePersistence;

    FTerrainGenerationConfig()
    {
        HeightVariation = 500.0f;
        NoiseScale = 0.01f;
        NoiseOctaves = 4;
        NoisePersistence = 0.5f;
    }
};

// Estrutura para configuração de vegetação
USTRUCT(BlueprintType)
struct AURA_API FVegetationConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation")
    float TreeDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation")
    float GrassDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation")
    float MinTreeScale;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation")
    float MaxTreeScale;

    FVegetationConfig()
    {
        TreeDensity = 0.3f;
        GrassDensity = 0.7f;
        MinTreeScale = 0.8f;
        MaxTreeScale = 1.2f;
    }
};

UENUM(BlueprintType)
enum class EPCGMapAssetType : uint8
{
    StaticMesh      UMETA(DisplayName = "Static Mesh"),
    SkeletalMesh    UMETA(DisplayName = "Skeletal Mesh"),
    Material        UMETA(DisplayName = "Material"),
    Texture         UMETA(DisplayName = "Texture"),
    Landscape       UMETA(DisplayName = "Landscape"),
    Foliage         UMETA(DisplayName = "Foliage"),
    Particle        UMETA(DisplayName = "Particle System"),
    Audio           UMETA(DisplayName = "Audio"),
    Blueprint       UMETA(DisplayName = "Blueprint")
};

UENUM(BlueprintType)
enum class EPCGDistributionType : uint8
{
    Random          UMETA(DisplayName = "Aleatório"),
    Grid            UMETA(DisplayName = "Grade"),
    Poisson         UMETA(DisplayName = "Poisson Disk"),
    Geometric       UMETA(DisplayName = "Geométrico"),
    Weighted        UMETA(DisplayName = "Ponderado"),
    Clustered       UMETA(DisplayName = "Agrupado")
};

// ===== ESTRUTURAS DE DADOS =====

USTRUCT(BlueprintType)
struct AURA_API FPCGAssetReference
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    EPCGMapAssetType AssetType = EPCGMapAssetType::StaticMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    TSoftObjectPtr<UObject> AssetReference;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    FString AssetPath;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    FString AssetName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    float Weight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    FVector Scale = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    bool bRandomRotation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    bool bRandomScale = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    FVector2D ScaleRange = FVector2D(0.8f, 1.2f);

    FPCGAssetReference()
    {
        AssetType = EPCGMapAssetType::StaticMesh;
        AssetPath = TEXT("");
        AssetName = TEXT("");
        Weight = 1.0f;
        Scale = FVector::OneVector;
        Rotation = FRotator::ZeroRotator;
        bRandomRotation = false;
        bRandomScale = false;
        ScaleRange = FVector2D(0.8f, 1.2f);
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGSpawnRule
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    FString RuleName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    EPCGDistributionType DistributionType = EPCGDistributionType::Random;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    TArray<FPCGAssetReference> Assets;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    float Density = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    float MinDistance = 100.0f; // 1 metro

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    float MaxDistance = 500.0f; // 5 metros

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    FVector2D HeightRange = FVector2D(-1000.0f, 1000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    FVector2D SlopeRange = FVector2D(0.0f, 45.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    bool bAlignToSurface = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    bool bAvoidOverlap = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    bool bRespectBounds = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    TArray<FString> ExclusionTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Rule")
    TArray<FString> RequiredTags;

    FPCGSpawnRule()
    {
        RuleName = TEXT("Default Rule");
        DistributionType = EPCGDistributionType::Random;
        Density = 1.0f;
        MinDistance = 100.0f;
        MaxDistance = 500.0f;
        HeightRange = FVector2D(-1000.0f, 1000.0f);
        SlopeRange = FVector2D(0.0f, 45.0f);
        bAlignToSurface = true;
        bAvoidOverlap = true;
        bRespectBounds = true;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGGenerationConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bEnableAutoGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bEnableRealTimeUpdates = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float GenerationSeed = 12345.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 MaxIterations = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    float QualityThreshold = 0.95f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bUseMultithreading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 ThreadCount = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bEnableDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bSaveGenerationData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FString OutputDirectory = TEXT("/Game/Generated/");

    FPCGGenerationConfig()
    {
        bEnableAutoGeneration = true;
        bEnableRealTimeUpdates = false;
        GenerationSeed = 12345.0f;
        MaxIterations = 1000;
        QualityThreshold = 0.95f;
        bUseMultithreading = true;
        ThreadCount = 4;
        bEnableDebugVisualization = false;
        bSaveGenerationData = true;
        OutputDirectory = TEXT("/Game/Generated/");
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGGenerationProgress
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    EPCGGenerationPhase CurrentPhase = EPCGGenerationPhase::None;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float PhaseProgress = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float OverallProgress = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    int32 GeneratedAssets = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    int32 TotalAssets = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    FDateTime StartTime;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    FDateTime EstimatedEndTime;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    FString CurrentOperation;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    TArray<FString> CompletedPhases;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    TArray<FString> ErrorMessages;

    FPCGGenerationProgress()
    {
        CurrentPhase = EPCGGenerationPhase::None;
        PhaseProgress = 0.0f;
        OverallProgress = 0.0f;
        GeneratedAssets = 0;
        TotalAssets = 0;
        StartTime = FDateTime::Now();
        EstimatedEndTime = FDateTime::Now();
        CurrentOperation = TEXT("");
    }
};

USTRUCT(BlueprintType)
struct AURA_API FLaneGenerationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Generation")
    FVector2D MapBounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Generation")
    int32 NumLanes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Generation")
    float LaneWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Generation")
    float LaneSpacing;

    FLaneGenerationParams()
    {
        MapBounds = FVector2D(10000.0f, 10000.0f);
        NumLanes = 3;
        LaneWidth = 400.0f;
        LaneSpacing = 100.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FObjectiveData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    EObjectiveType Type;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    ETeam TeamOwnership;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    float Health;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    float MaxHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
    bool bIsActive;

    FObjectiveData()
    {
        Position = FVector::ZeroVector;
        Type = EObjectiveType::Nexus;
        TeamOwnership = ETeam::Neutro;
        Health = 1000.0f;
        MaxHealth = 1000.0f;
        bIsActive = true;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FWallData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall")
    FVector Dimensions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall")
    float Health;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall")
    float MaxHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall")
    bool bIsDestructible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall")
    EWallMaterial MaterialType;

    FWallData()
    {
        Position = FVector::ZeroVector;
        Dimensions = FVector(100.0f, 100.0f, 300.0f);
        Health = 500.0f;
        MaxHealth = 500.0f;
        bIsDestructible = true;
        MaterialType = EWallMaterial::Stone;
    }
};



USTRUCT(BlueprintType)
struct AURA_API FPropData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prop")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prop")
    EPropType Type;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prop")
    FVector Scale;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prop")
    FRotator Rotation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prop")
    bool bIsDestructible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prop")
    float Health;

    FPropData()
    {
        Position = FVector::ZeroVector;
        Type = EPropType::Rock;
        Scale = FVector::OneVector;
        Rotation = FRotator::ZeroRotator;
        bIsDestructible = true;
        Health = 100.0f;
    }
};

// ===== DELEGATES =====


DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnPCGGenerationCompleted);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPCGGenerationFailed, const FString&, ErrorMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPCGAssetGenerated, const FString&, AssetName, int32, Count);

// Delegates específicos para configuração
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnObjectiveConfigured, const FObjectiveData&, ObjectiveData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWallConfigured, const FWallData&, WallData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBridgeConfigured, const FBridgeData&, BridgeData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPropConfigured, const FPropData&, PropData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPhaseChanged, EPCGGenerationPhase, OldPhase, EPCGGenerationPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnProgressUpdated, const FString&, PhaseName, float, Progress, float, EstimatedTime);

// ===== CLASSE PRINCIPAL =====

UCLASS(BlueprintType, Blueprintable)
class AURA_API AProceduralMapGenerator : public AActor
{
    GENERATED_BODY()

public:
    AProceduralMapGenerator();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

    // ===== COMPONENTES =====

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UPCGComponent* PCGComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UStaticMeshComponent* BoundsVisualization;

    // ===== CONFIGURAÇÃO =====

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Configuration")
    FPCGGenerationConfig GenerationConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Configuration")
    TArray<FPCGSpawnRule> SpawnRules;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Configuration")
    TSoftObjectPtr<UPCGGraph> PCGGraphAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Configuration")
    FVector MapBounds = FVector(5000000.0f, 5000000.0f, 200000.0f); // 50km x 50km x 2km

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG Configuration")
    FVector MapCenter = FVector::ZeroVector;

    // ===== REFERÊNCIAS DOS MANAGERS =====

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<ALaneManager> LaneManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<ABaronAuracronManager> BaronManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<ADragonPrismalManager> DragonManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<AWallCollisionManager> WallManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<ARiverPrismalManager> RiverManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<AMinionWaveManager> MinionManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<AMapManager> MapManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<AGeometricValidator> GeometricValidator;

    // ===== ESTADO =====

    UPROPERTY(BlueprintReadOnly, Category = "State")
    FPCGGenerationProgress GenerationProgress;

    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsGenerating = false;

    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsPaused = false;

    UPROPERTY(BlueprintReadOnly, Category = "State")
    TArray<AActor*> GeneratedActors;

    // ===== DELEGATES =====



    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPCGGenerationCompleted OnGenerationCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPCGGenerationFailed OnGenerationFailed;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPCGAssetGenerated OnAssetGenerated;

public:
    // ===== FUNÇÕES PRINCIPAIS =====

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void StartGeneration();

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void StopGeneration();

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void PauseGeneration();

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void ResumeGeneration();

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void RestartGeneration();

    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void ClearGeneration();

    // ===== FUNÇÕES DE CONFIGURAÇÃO =====

    UFUNCTION(BlueprintCallable, Category = "PCG Configuration")
    void SetGenerationConfig(const FPCGGenerationConfig& NewConfig);

    UFUNCTION(BlueprintCallable, Category = "PCG Configuration")
    FPCGGenerationConfig GetGenerationConfig() const;

    UFUNCTION(BlueprintCallable, Category = "PCG Configuration")
    void AddSpawnRule(const FPCGSpawnRule& NewRule);

    UFUNCTION(BlueprintCallable, Category = "PCG Configuration")
    void RemoveSpawnRule(const FString& RuleName);

    UFUNCTION(BlueprintCallable, Category = "PCG Configuration")
    void ClearSpawnRules();

    // ===== FUNÇÕES DE ESTADO =====

    UFUNCTION(BlueprintCallable, Category = "PCG State")
    FPCGGenerationProgress GetGenerationProgress() const;

    UFUNCTION(BlueprintCallable, Category = "PCG State")
    bool IsGenerating() const;

    UFUNCTION(BlueprintCallable, Category = "PCG State")
    bool IsPaused() const;

    UFUNCTION(BlueprintCallable, Category = "PCG State")
    EPCGGenerationPhase GetCurrentPhase() const;

    UFUNCTION(BlueprintCallable, Category = "PCG State")
    float GetOverallProgress() const;

    UFUNCTION(BlueprintCallable, Category = "PCG State")
    int32 GetGeneratedAssetCount() const;

    // ===== FUNÇÕES DE INTEGRAÇÃO =====

    UFUNCTION(BlueprintCallable, Category = "PCG Integration")
    void InitializeManagers();

    UFUNCTION(BlueprintCallable, Category = "PCG Integration")
    void ValidateManagerReferences();

    UFUNCTION(BlueprintCallable, Category = "PCG Integration")
    void SynchronizeWithMapManager();

    UFUNCTION(BlueprintCallable, Category = "PCG Integration")
    void UpdatePCGFromGeometry();

protected:
    // ===== FUNÇÕES INTERNAS DE GERAÇÃO =====

    void ExecuteGenerationPhase(EPCGGenerationPhase Phase);
    void GenerateTerrainPhase();
    void GenerateLanesPhase();
    void GenerateObjectivesPhase();
    void GenerateWallsPhase();
    void GenerateRiverPhase();
    void GenerateVegetationPhase();
    void GeneratePropsPhase();
    void GenerateLightingPhase();
    void FinalizeGeneration();

    // ===== FUNÇÕES DE PCG =====

    void SetupPCGGraph();
    void ConfigurePCGNodes();
    void ExecutePCGGraph();
    void ProcessPCGResults();
    void CleanupPCGData();

    // ===== FUNÇÕES DE SPAWN =====

    void SpawnAssetsFromRule(const FPCGSpawnRule& Rule, const TArray<FVector>& Locations);
    TArray<FVector> GenerateSpawnLocations(const FPCGSpawnRule& Rule);
    bool ValidateSpawnLocation(const FVector& Location, const FPCGSpawnRule& Rule);
    AActor* SpawnAssetAtLocation(const FPCGAssetReference& Asset, const FVector& Location, const FRotator& Rotation);

    // ===== FUNÇÕES DE DISTRIBUIÇÃO =====

    TArray<FVector> GenerateRandomDistribution(const FPCGSpawnRule& Rule);
    TArray<FVector> GenerateGridDistribution(const FPCGSpawnRule& Rule);
    TArray<FVector> GeneratePoissonDistribution(const FPCGSpawnRule& Rule);
    TArray<FVector> GenerateGeometricDistribution(const FPCGSpawnRule& Rule);
    TArray<FVector> GenerateWeightedDistribution(const FPCGSpawnRule& Rule);
    TArray<FVector> GenerateClusteredDistribution(const FPCGSpawnRule& Rule);

    // ===== FUNÇÕES DE VALIDAÇÃO =====

    bool ValidateGenerationSetup();
    bool ValidateAssetReferences();
    bool ValidateSpawnRules();
    bool ValidateManagerIntegration();
    void LogValidationErrors(const TArray<FString>& Errors);

    // ===== FUNÇÕES DE PROGRESSO =====

    void UpdateGenerationProgress(const FString& PhaseName, float PhaseProgress);
    void SetCurrentPhase(EPCGGenerationPhase NewPhase);
    FString GetPhaseDisplayName(EPCGGenerationPhase Phase) const;
    void IncrementAssetCount();
    void CalculateEstimatedTime();

    // ===== FUNÇÕES DE DEBUG =====

    void DrawDebugBounds();
    void DrawDebugSpawnPoints(const TArray<FVector>& Points);
    void LogGenerationStatistics();
    void ExportGenerationReport(const FString& FilePath);

    // ===== FUNÇÕES DE LIMPEZA =====

    void CleanupGeneratedActors();
    void ResetGenerationState();
    void ClearDebugVisualization();

    // ===== FUNÇÕES AUXILIARES ROBUSTAS =====

    void GenerateLaneConnections(const TArray<FVector>& FromLane, const TArray<FVector>& ToLane);
    void GenerateDefensiveWalls(const FVector& BaseCenter, float Radius, TArray<FVector>& OutWallPositions);
    void ConfigureTerrainPCGNodes(UPCGGraph* PCGGraph);
    void ConfigureVegetationPCGNodes(UPCGGraph* PCGGraph);
    void ConfigurePropsPCGNodes(UPCGGraph* PCGGraph);

    // ===== VARIÁVEIS DE PROGRESSO =====

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    FGenerationProgress CurrentProgress;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float TotalProgress = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float EstimatedTimeRemaining = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float GenerationStartTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Generation State")
    bool bIsGenerationComplete = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDebugLines = false;

    UPROPERTY(BlueprintReadOnly, Category = "Generation State")
    EPCGGenerationPhase CurrentPhase = EPCGGenerationPhase::None;

    // ===== ASSETS E MATERIAIS =====

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TArray<UStaticMesh*> TerrainMeshes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TArray<UStaticMesh*> VegetationMeshes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TArray<UStaticMesh*> PropMeshes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    UMaterialInterface* TerrainMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    UMaterialInterface* WaterMaterial;

    // ===== CONFIGURAÇÕES =====

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float SpawnDensity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FTerrainGenerationConfig TerrainConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FVegetationConfig VegetationConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FExclusionZone> ExclusionZones;

    // ===== MANAGERS ADICIONAIS =====

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Managers")
    TObjectPtr<AActor> ObjectiveManager;

    // ===== DELEGATES =====

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnObjectiveConfigured OnObjectiveConfigured;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnWallConfigured OnWallConfigured;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBridgeConfigured OnBridgeConfigured;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPropConfigured OnPropConfigured;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPhaseChanged OnPhaseChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnProgressUpdated OnProgressUpdated;

    // ===== TIMERS =====

    FTimerHandle GenerationTimerHandle;
    FTimerHandle ProgressUpdateTimerHandle;
    FTimerHandle ValidationTimerHandle;

    // ===== DADOS INTERNOS =====

    TArray<FString> ValidationErrors;
    TMap<FString, int32> AssetCounts;
    TMap<EPCGGenerationPhase, float> PhaseTimings;
    FRandomStream RandomStream;

    // Internal asset tracking (since MapManager doesn't have GetGeneratedAssetNames)
    TArray<FString> InternalGeneratedAssetNames;
};
