// AMapManager.h - Controlador principal do sistema de geração procedural do mapa
// Unreal Engine 5.6 - APIs modernas
// 1 UU = 1 cm (Unreal Units)

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/GameplayStatics.h"
#include "Math/UnrealMathUtility.h"
#include "TimerManager.h"
#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"

// Incluir todos os managers específicos
#include "ALaneManager.h"
#include "ABaronAuracronManager.h"
#include "ADragonPrismalManager.h"
#include "AWallCollisionManager.h"
#include "ARiverPrismalManager.h"
#include "AMinionWaveManager.h"

#include "AMapManager.generated.h"

// Enums para configuração do mapa
UENUM(BlueprintType)
enum class EMapGenerationPhase : uint8
{
    None UMETA(DisplayName = "None"),
    Initializing UMETA(DisplayName = "Initializing"),
    GeneratingTerrain UMETA(DisplayName = "Generating Terrain"),
    CreatingLanes UMETA(DisplayName = "Creating Lanes"),
    PlacingObjectives UMETA(DisplayName = "Placing Objectives"),
    BuildingWalls UMETA(DisplayName = "Building Walls"),
    GeneratingRiver UMETA(DisplayName = "Generating River"),
    SpawningMinions UMETA(DisplayName = "Spawning Minions"),
    Validating UMETA(DisplayName = "Validating"),
    Complete UMETA(DisplayName = "Complete"),
    Error UMETA(DisplayName = "Error")
};

UENUM(BlueprintType)
enum class EMapSize : uint8
{
    Small UMETA(DisplayName = "Small (10000x10000)"),
    Medium UMETA(DisplayName = "Medium (15000x15000)"),
    Large UMETA(DisplayName = "Large (20000x20000)"),
    Custom UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EMapValidationLevel : uint8
{
    Basic UMETA(DisplayName = "Basic"),
    Standard UMETA(DisplayName = "Standard"),
    Comprehensive UMETA(DisplayName = "Comprehensive"),
    Debug UMETA(DisplayName = "Debug")
};

// Estruturas de dados para configuração
USTRUCT(BlueprintType)
struct FMapConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    EMapSize MapSize = EMapSize::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration", meta = (EditCondition = "MapSize == EMapSize::Custom"))
    FVector2D CustomMapSize = FVector2D(15000.0f, 15000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    int32 RandomSeed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    bool bUseAsyncGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    EMapValidationLevel ValidationLevel = EMapValidationLevel::Standard;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    bool bEnableRealTimeValidation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    float GenerationTimeLimit = 30.0f; // segundos

    FMapConfiguration()
    {
        MapSize = EMapSize::Medium;
        CustomMapSize = FVector2D(15000.0f, 15000.0f);
        RandomSeed = 12345;
        bUseAsyncGeneration = true;
        ValidationLevel = EMapValidationLevel::Standard;
        bEnableRealTimeValidation = true;
        GenerationTimeLimit = 30.0f;
    }
};

USTRUCT(BlueprintType)
struct FMapGenerationProgress
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Generation Progress")
    EMapGenerationPhase CurrentPhase = EMapGenerationPhase::None;

    UPROPERTY(BlueprintReadOnly, Category = "Generation Progress")
    float OverallProgress = 0.0f; // 0.0 a 1.0

    UPROPERTY(BlueprintReadOnly, Category = "Generation Progress")
    float PhaseProgress = 0.0f; // 0.0 a 1.0

    UPROPERTY(BlueprintReadOnly, Category = "Generation Progress")
    FString CurrentTask = TEXT("");

    UPROPERTY(BlueprintReadOnly, Category = "Generation Progress")
    float ElapsedTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Generation Progress")
    bool bHasErrors = false;

    UPROPERTY(BlueprintReadOnly, Category = "Generation Progress")
    TArray<FString> ErrorMessages;

    FMapGenerationProgress()
    {
        CurrentPhase = EMapGenerationPhase::None;
        OverallProgress = 0.0f;
        PhaseProgress = 0.0f;
        CurrentTask = TEXT("");
        ElapsedTime = 0.0f;
        bHasErrors = false;
        ErrorMessages.Empty();
    }
};

USTRUCT(BlueprintType)
struct FMapValidationResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    bool bIsValid = false;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    float ValidationScore = 0.0f; // 0.0 a 1.0

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> ValidationErrors;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> ValidationWarnings;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TMap<FString, float> ComponentScores;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    float ValidationTime = 0.0f;

    FMapValidationResult()
    {
        bIsValid = false;
        ValidationScore = 0.0f;
        ValidationErrors.Empty();
        ValidationWarnings.Empty();
        ComponentScores.Empty();
        ValidationTime = 0.0f;
    }
};

// Delegates para eventos de geração
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMapGenerationPhaseChanged, EMapGenerationPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMapGenerationProgress, const FMapGenerationProgress&, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMapGenerationComplete, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMapValidationComplete, const FMapValidationResult&, ValidationResult);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMapGenerationError, const FString&, ErrorMessage, EMapGenerationPhase, Phase);

/**
 * AMapManager - Controlador principal do sistema de geração procedural do mapa
 * 
 * Este manager coordena todos os outros managers específicos para criar
 * um mapa completo de forma procedural e automática.
 * 
 * Responsabilidades:
 * - Coordenar a geração sequencial de todos os componentes do mapa
 * - Gerenciar o progresso e status da geração
 * - Validar a integridade geométrica do mapa
 * - Fornecer interface para configuração e controle
 * - Otimizar performance durante a geração
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API AMapManager : public AActor
{
    GENERATED_BODY()

public:
    AMapManager();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void Tick(float DeltaTime) override;

    // ===== COMPONENTES =====
protected:
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    // ===== CONFIGURAÇÃO =====
public:
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    FMapConfiguration MapConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    bool bAutoGenerateOnBeginPlay = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Map Configuration")
    bool bShowDebugInfo = false;

    // ===== MANAGERS =====
protected:
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Managers")
    TObjectPtr<ALaneManager> LaneManager;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Managers")
    TObjectPtr<ABaronAuracronManager> BaronManager;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Managers")
    TObjectPtr<ADragonPrismalManager> DragonManager;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Managers")
    TObjectPtr<AWallCollisionManager> WallManager;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Managers")
    TObjectPtr<ARiverPrismalManager> RiverManager;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Managers")
    TObjectPtr<AMinionWaveManager> MinionManager;

    // ===== STATUS E PROGRESSO =====
protected:
    UPROPERTY(BlueprintReadOnly, Category = "Generation Status")
    FMapGenerationProgress GenerationProgress;

    UPROPERTY(BlueprintReadOnly, Category = "Generation Status")
    FMapValidationResult LastValidationResult;

    UPROPERTY(BlueprintReadOnly, Category = "Generation Status")
    bool bIsGenerating = false;

    UPROPERTY(BlueprintReadOnly, Category = "Generation Status")
    bool bIsValidating = false;

    // ===== TERRAIN DATA =====
protected:
    /** Bounds do terreno calculados durante a geração */
    FBox TerrainBounds;

    // ===== TIMERS E ASYNC =====
protected:
    FTimerHandle GenerationTimerHandle;
    FTimerHandle ValidationTimerHandle;
    FTimerHandle ProgressUpdateTimerHandle;

    FThreadSafeBool bStopGeneration;
    FDateTime GenerationStartTime;

    // ===== DELEGATES =====
public:
    UPROPERTY(BlueprintAssignable, Category = "Map Events")
    FOnMapGenerationPhaseChanged OnMapGenerationPhaseChanged;

    UPROPERTY(BlueprintAssignable, Category = "Map Events")
    FOnMapGenerationProgress OnMapGenerationProgress;

    UPROPERTY(BlueprintAssignable, Category = "Map Events")
    FOnMapGenerationComplete OnMapGenerationComplete;

    UPROPERTY(BlueprintAssignable, Category = "Map Events")
    FOnMapValidationComplete OnMapValidationComplete;

    UPROPERTY(BlueprintAssignable, Category = "Map Events")
    FOnMapGenerationError OnMapGenerationError;

    // ===== FUNÇÕES PRINCIPAIS =====
public:
    /**
     * Inicia a geração completa do mapa
     * @param Config Configuração para a geração
     * @return true se a geração foi iniciada com sucesso
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generation")
    bool StartMapGeneration(const FMapConfiguration& Config = FMapConfiguration());

    /**
     * Para a geração do mapa em andamento
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generation")
    void StopMapGeneration();

    /**
     * Reinicia a geração do mapa do zero
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generation")
    void RestartMapGeneration();

    /**
     * Valida a integridade do mapa atual
     * @param ValidationLevel Nível de validação a ser executado
     * @return Resultado da validação
     */
    UFUNCTION(BlueprintCallable, Category = "Map Validation")
    FMapValidationResult ValidateMap(EMapValidationLevel ValidationLevel = EMapValidationLevel::Standard);

    /**
     * Limpa todos os elementos do mapa
     */
    UFUNCTION(BlueprintCallable, Category = "Map Management")
    void ClearMap();

    // ===== GETTERS =====
public:
    UFUNCTION(BlueprintPure, Category = "Map Status")
    bool IsGenerating() const { return bIsGenerating; }

    UFUNCTION(BlueprintPure, Category = "Map Status")
    bool IsValidating() const { return bIsValidating; }

    UFUNCTION(BlueprintPure, Category = "Map Status")
    FMapGenerationProgress GetGenerationProgress() const { return GenerationProgress; }

    UFUNCTION(BlueprintPure, Category = "Map Status")
    FMapValidationResult GetLastValidationResult() const { return LastValidationResult; }

    UFUNCTION(BlueprintPure, Category = "Map Status")
    EMapGenerationPhase GetCurrentPhase() const { return GenerationProgress.CurrentPhase; }

    UFUNCTION(BlueprintPure, Category = "Map Managers")
    ALaneManager* GetLaneManager() const { return LaneManager; }

    UFUNCTION(BlueprintPure, Category = "Map Managers")
    ABaronAuracronManager* GetBaronManager() const { return BaronManager; }

    UFUNCTION(BlueprintPure, Category = "Map Managers")
    ADragonPrismalManager* GetDragonManager() const { return DragonManager; }

    UFUNCTION(BlueprintPure, Category = "Map Managers")
    AWallCollisionManager* GetWallManager() const { return WallManager; }

    UFUNCTION(BlueprintPure, Category = "Map Managers")
    ARiverPrismalManager* GetRiverManager() const { return RiverManager; }

    UFUNCTION(BlueprintPure, Category = "Map Managers")
    AMinionWaveManager* GetMinionManager() const { return MinionManager; }

    // ===== FUNÇÕES INTERNAS =====
protected:
    /**
     * Inicializa todos os managers
     */
    void InitializeManagers();

    /**
     * Executa a próxima fase da geração
     */
    void ExecuteNextGenerationPhase();

    /**
     * Atualiza o progresso da geração
     */
    void UpdateGenerationProgress();

    /**
     * Finaliza a geração do mapa
     */
    void CompleteMapGeneration(bool bSuccess);

    /**
     * Trata erros durante a geração
     */
    void HandleGenerationError(const FString& ErrorMessage, EMapGenerationPhase Phase);

    /**
     * Calcula o tamanho do mapa baseado na configuração
     */
    FVector2D GetMapSize() const;

    /**
     * Valida se a configuração é válida
     */
    bool IsConfigurationValid(const FMapConfiguration& Config) const;

    /**
     * Executa validação em tempo real
     */
    void PerformRealTimeValidation();

    // ===== FASES DE GERAÇÃO =====
protected:
    void Phase_Initialize();
    void Phase_GenerateTerrain();
    void Phase_CreateLanes();
    void Phase_PlaceObjectives();
    void Phase_BuildWalls();
    void Phase_GenerateRiver();
    void Phase_SpawnMinions();
    void Phase_Validate();

    // ===== VALIDAÇÃO =====
protected:
    /**
     * Valida geometria das lanes
     */
    bool ValidateLaneGeometry(TArray<FString>& OutErrors) const;

    /**
     * Valida posicionamento dos objetivos
     */
    bool ValidateObjectivePlacement(TArray<FString>& OutErrors) const;

    /**
     * Valida sistema de colisões
     */
    bool ValidateCollisionSystem(TArray<FString>& OutErrors) const;

    /**
     * Valida geometria do rio
     */
    bool ValidateRiverGeometry(TArray<FString>& OutErrors) const;

    /**
     * Valida pathfinding dos minions
     */
    bool ValidateMinionPathfinding(TArray<FString>& OutErrors) const;

    // ===== DEBUG =====
protected:
    /**
     * Desenha informações de debug
     */
    void DrawDebugInfo() const;

    /**
     * Log de informações de geração
     */
    void LogGenerationInfo(const FString& Message, bool bIsError = false) const;

public:
    /**
     * Ativa/desativa modo debug
     */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    void SetDebugMode(bool bEnabled) { bShowDebugInfo = bEnabled; }

    /**
     * Obtém estatísticas detalhadas do mapa
     */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    TMap<FString, FString> GetMapStatistics() const;
};