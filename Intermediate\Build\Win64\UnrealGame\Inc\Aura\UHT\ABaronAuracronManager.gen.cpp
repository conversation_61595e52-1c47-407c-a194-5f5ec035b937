// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "ABaronAuracronManager.h"
#include "Engine/DamageEvents.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeABaronAuracronManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager();
AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EBaronState();
AURA_API UEnum* Z_Construct_UEnum_Aura_ESentinelType();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FBaronData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FBuffData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FHexagonalArea();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FSentinelData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AController_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FDamageEvent();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EBaronState ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EBaronState;
static UEnum* EBaronState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EBaronState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EBaronState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EBaronState, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EBaronState"));
	}
	return Z_Registration_Info_UEnum_EBaronState.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EBaronState>()
{
	return EBaronState_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EBaronState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Ativo" },
		{ "Active.Name", "EBaronState::Active" },
		{ "BlueprintType", "true" },
		{ "Combat.DisplayName", "Em Combate" },
		{ "Combat.Name", "EBaronState::Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para estados e tipos do Bar\xc3\xa3o\n" },
#endif
		{ "Dead.DisplayName", "Morto" },
		{ "Dead.Name", "EBaronState::Dead" },
		{ "Dormant.DisplayName", "Dormindo" },
		{ "Dormant.Name", "EBaronState::Dormant" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
		{ "Rotating.DisplayName", "Rotacionando" },
		{ "Rotating.Name", "EBaronState::Rotating" },
		{ "Spawning.DisplayName", "Aparecendo" },
		{ "Spawning.Name", "EBaronState::Spawning" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para estados e tipos do Bar\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EBaronState::Dormant", (int64)EBaronState::Dormant },
		{ "EBaronState::Spawning", (int64)EBaronState::Spawning },
		{ "EBaronState::Active", (int64)EBaronState::Active },
		{ "EBaronState::Combat", (int64)EBaronState::Combat },
		{ "EBaronState::Rotating", (int64)EBaronState::Rotating },
		{ "EBaronState::Dead", (int64)EBaronState::Dead },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EBaronState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EBaronState",
	"EBaronState",
	Z_Construct_UEnum_Aura_EBaronState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EBaronState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EBaronState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EBaronState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EBaronState()
{
	if (!Z_Registration_Info_UEnum_EBaronState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EBaronState.InnerSingleton, Z_Construct_UEnum_Aura_EBaronState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EBaronState.InnerSingleton;
}
// ********** End Enum EBaronState *****************************************************************

// ********** Begin Enum ESentinelType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESentinelType;
static UEnum* ESentinelType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESentinelType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESentinelType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ESentinelType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ESentinelType"));
	}
	return Z_Registration_Info_UEnum_ESentinelType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ESentinelType>()
{
	return ESentinelType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ESentinelType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cristalina.DisplayName", "Sentinela Cristalina" },
		{ "Cristalina.Name", "ESentinelType::Cristalina" },
		{ "Guardian.DisplayName", "Guardi\xc3\xa3o dos Portais" },
		{ "Guardian.Name", "ESentinelType::Guardian" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESentinelType::Cristalina", (int64)ESentinelType::Cristalina },
		{ "ESentinelType::Guardian", (int64)ESentinelType::Guardian },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ESentinelType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ESentinelType",
	"ESentinelType",
	Z_Construct_UEnum_Aura_ESentinelType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ESentinelType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ESentinelType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ESentinelType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ESentinelType()
{
	if (!Z_Registration_Info_UEnum_ESentinelType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESentinelType.InnerSingleton, Z_Construct_UEnum_Aura_ESentinelType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESentinelType.InnerSingleton;
}
// ********** End Enum ESentinelType ***************************************************************

// ********** Begin ScriptStruct FBaronData ********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBaronData;
class UScriptStruct* FBaronData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBaronData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBaronData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBaronData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("BaronData"));
	}
	return Z_Registration_Info_UScriptStruct_FBaronData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBaronData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para o Bar\xc3\xa3o e objetivos secund\xc3\xa1rios\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para o Bar\xc3\xa3o e objetivos secund\xc3\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseHealth_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthScaling_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnTime_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationInterval_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "Baron" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationInterval;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBaronData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_BaseHealth = { "BaseHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, BaseHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseHealth_MetaData), NewProp_BaseHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_HealthScaling = { "HealthScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, HealthScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthScaling_MetaData), NewProp_HealthScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_SpawnTime = { "SpawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, SpawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnTime_MetaData), NewProp_SpawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_RotationInterval = { "RotationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, RotationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationInterval_MetaData), NewProp_RotationInterval_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBaronData, CurrentState), Z_Construct_UEnum_Aura_EBaronState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBaronData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_BaseHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_HealthScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_SpawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_RotationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBaronData_Statics::NewProp_CurrentState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBaronData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBaronData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"BaronData",
	Z_Construct_UScriptStruct_FBaronData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBaronData_Statics::PropPointers),
	sizeof(FBaronData),
	alignof(FBaronData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBaronData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBaronData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBaronData()
{
	if (!Z_Registration_Info_UScriptStruct_FBaronData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBaronData.InnerSingleton, Z_Construct_UScriptStruct_FBaronData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBaronData.InnerSingleton;
}
// ********** End ScriptStruct FBaronData **********************************************************

// ********** Begin ScriptStruct FHexagonalArea ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHexagonalArea;
class UScriptStruct* FHexagonalArea::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHexagonalArea.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHexagonalArea.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHexagonalArea, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("HexagonalArea"));
	}
	return Z_Registration_Info_UScriptStruct_FHexagonalArea.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHexagonalArea_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vertices_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntrancePositions_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntranceWidth_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vertices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Vertices;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Area;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EntrancePositions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EntrancePositions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EntranceWidth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHexagonalArea>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalArea, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalArea, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Vertices_Inner = { "Vertices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Vertices = { "Vertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalArea, Vertices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vertices_MetaData), NewProp_Vertices_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalArea, Area), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_EntrancePositions_Inner = { "EntrancePositions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_EntrancePositions = { "EntrancePositions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalArea, EntrancePositions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntrancePositions_MetaData), NewProp_EntrancePositions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_EntranceWidth = { "EntranceWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalArea, EntranceWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntranceWidth_MetaData), NewProp_EntranceWidth_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHexagonalArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Vertices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Vertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_EntrancePositions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_EntrancePositions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewProp_EntranceWidth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHexagonalArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHexagonalArea_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"HexagonalArea",
	Z_Construct_UScriptStruct_FHexagonalArea_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHexagonalArea_Statics::PropPointers),
	sizeof(FHexagonalArea),
	alignof(FHexagonalArea),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHexagonalArea_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHexagonalArea_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHexagonalArea()
{
	if (!Z_Registration_Info_UScriptStruct_FHexagonalArea.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHexagonalArea.InnerSingleton, Z_Construct_UScriptStruct_FHexagonalArea_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHexagonalArea.InnerSingleton;
}
// ********** End ScriptStruct FHexagonalArea ******************************************************

// ********** Begin ScriptStruct FSentinelData *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSentinelData;
class UScriptStruct* FSentinelData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSentinelData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSentinelData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSentinelData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("SentinelData"));
	}
	return Z_Registration_Info_UScriptStruct_FSentinelData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSentinelData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationSpeed_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationInterval_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAlive_MetaData[] = {
		{ "Category", "Sentinel" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationInterval;
	static void NewProp_bIsAlive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAlive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSentinelData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSentinelData, Type), Z_Construct_UEnum_Aura_ESentinelType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) }; // 4164581287
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSentinelData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSentinelData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSentinelData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSentinelData, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_RotationSpeed = { "RotationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSentinelData, RotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationSpeed_MetaData), NewProp_RotationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_RotationInterval = { "RotationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSentinelData, RotationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationInterval_MetaData), NewProp_RotationInterval_MetaData) };
void Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_bIsAlive_SetBit(void* Obj)
{
	((FSentinelData*)Obj)->bIsAlive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_bIsAlive = { "bIsAlive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSentinelData), &Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_bIsAlive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAlive_MetaData), NewProp_bIsAlive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSentinelData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_RotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_RotationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSentinelData_Statics::NewProp_bIsAlive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSentinelData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSentinelData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"SentinelData",
	Z_Construct_UScriptStruct_FSentinelData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSentinelData_Statics::PropPointers),
	sizeof(FSentinelData),
	alignof(FSentinelData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSentinelData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSentinelData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSentinelData()
{
	if (!Z_Registration_Info_UScriptStruct_FSentinelData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSentinelData.InnerSingleton, Z_Construct_UScriptStruct_FSentinelData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSentinelData.InnerSingleton;
}
// ********** End ScriptStruct FSentinelData *******************************************************

// ********** Begin ScriptStruct FBuffData *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBuffData;
class UScriptStruct* FBuffData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBuffData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBuffData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBuffData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("BuffData"));
	}
	return Z_Registration_Info_UScriptStruct_FBuffData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBuffData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageBonus_MetaData[] = {
		{ "Category", "Buff" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeedBonus_MetaData[] = {
		{ "Category", "Buff" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Buff" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectRadius_MetaData[] = {
		{ "Category", "Buff" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamID_MetaData[] = {
		{ "Category", "Buff" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBuffData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_DamageBonus = { "DamageBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuffData, DamageBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageBonus_MetaData), NewProp_DamageBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_SpeedBonus = { "SpeedBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuffData, SpeedBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeedBonus_MetaData), NewProp_SpeedBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuffData, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_EffectRadius = { "EffectRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuffData, EffectRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectRadius_MetaData), NewProp_EffectRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuffData, TeamID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamID_MetaData), NewProp_TeamID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBuffData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_DamageBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_SpeedBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_EffectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuffData_Statics::NewProp_TeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBuffData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBuffData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"BuffData",
	Z_Construct_UScriptStruct_FBuffData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBuffData_Statics::PropPointers),
	sizeof(FBuffData),
	alignof(FBuffData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBuffData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBuffData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBuffData()
{
	if (!Z_Registration_Info_UScriptStruct_FBuffData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBuffData.InnerSingleton, Z_Construct_UScriptStruct_FBuffData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBuffData.InnerSingleton;
}
// ********** End ScriptStruct FBuffData ***********************************************************

// ********** Begin Class ABaronAuracronManager Function ApplyTeamBuff *****************************
struct Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics
{
	struct BaronAuracronManager_eventApplyTeamBuff_Parms
	{
		int32 TeamID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Buff System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de buffs\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de buffs" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventApplyTeamBuff_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::NewProp_TeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "ApplyTeamBuff", Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::BaronAuracronManager_eventApplyTeamBuff_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::BaronAuracronManager_eventApplyTeamBuff_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execApplyTeamBuff)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTeamBuff(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function ApplyTeamBuff *******************************

// ********** Begin Class ABaronAuracronManager Function CalculateHexagonalArea ********************
struct Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics
{
	struct BaronAuracronManager_eventCalculateHexagonalArea_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Hexagonal Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventCalculateHexagonalArea_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "CalculateHexagonalArea", Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::BaronAuracronManager_eventCalculateHexagonalArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::BaronAuracronManager_eventCalculateHexagonalArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execCalculateHexagonalArea)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateHexagonalArea();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function CalculateHexagonalArea **********************

// ********** Begin Class ABaronAuracronManager Function CalculateHexagonalVertices ****************
struct Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalVertices_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Hexagonal Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para geometria hexagonal\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para geometria hexagonal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalVertices_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "CalculateHexagonalVertices", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalVertices_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalVertices_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalVertices()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalVertices_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execCalculateHexagonalVertices)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CalculateHexagonalVertices();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function CalculateHexagonalVertices ******************

// ********** Begin Class ABaronAuracronManager Function DespawnBaron ******************************
struct Z_Construct_UFunction_ABaronAuracronManager_DespawnBaron_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Baron Management" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_DespawnBaron_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "DespawnBaron", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_DespawnBaron_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_DespawnBaron_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_DespawnBaron()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_DespawnBaron_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execDespawnBaron)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DespawnBaron();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function DespawnBaron ********************************

// ********** Begin Class ABaronAuracronManager Function DistributeRewards *************************
struct Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics
{
	struct BaronAuracronManager_eventDistributeRewards_Parms
	{
		int32 WinningTeam;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Buff System" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WinningTeam;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::NewProp_WinningTeam = { "WinningTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventDistributeRewards_Parms, WinningTeam), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::NewProp_WinningTeam,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "DistributeRewards", Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::BaronAuracronManager_eventDistributeRewards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::BaronAuracronManager_eventDistributeRewards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execDistributeRewards)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_WinningTeam);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DistributeRewards(Z_Param_WinningTeam);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function DistributeRewards ***************************

// ********** Begin Class ABaronAuracronManager Function DrawDebugHexagon **************************
struct Z_Construct_UFunction_ABaronAuracronManager_DrawDebugHexagon_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_DrawDebugHexagon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "DrawDebugHexagon", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_DrawDebugHexagon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_DrawDebugHexagon_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_DrawDebugHexagon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_DrawDebugHexagon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execDrawDebugHexagon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugHexagon();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function DrawDebugHexagon ****************************

// ********** Begin Class ABaronAuracronManager Function DrawDebugSentinels ************************
struct Z_Construct_UFunction_ABaronAuracronManager_DrawDebugSentinels_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_DrawDebugSentinels_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "DrawDebugSentinels", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_DrawDebugSentinels_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_DrawDebugSentinels_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_DrawDebugSentinels()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_DrawDebugSentinels_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execDrawDebugSentinels)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugSentinels();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function DrawDebugSentinels **************************

// ********** Begin Class ABaronAuracronManager Function EndCombat *********************************
struct Z_Construct_UFunction_ABaronAuracronManager_EndCombat_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_EndCombat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "EndCombat", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_EndCombat_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_EndCombat_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_EndCombat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_EndCombat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execEndCombat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EndCombat();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function EndCombat ***********************************

// ********** Begin Class ABaronAuracronManager Function GetBaronState *****************************
struct Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics
{
	struct BaronAuracronManager_eventGetBaronState_Parms
	{
		EBaronState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventGetBaronState_Parms, ReturnValue), Z_Construct_UEnum_Aura_EBaronState, METADATA_PARAMS(0, nullptr) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "GetBaronState", Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::BaronAuracronManager_eventGetBaronState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::BaronAuracronManager_eventGetBaronState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_GetBaronState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_GetBaronState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execGetBaronState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EBaronState*)Z_Param__Result=P_THIS->GetBaronState();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function GetBaronState *******************************

// ********** Begin Class ABaronAuracronManager Function GetClosestEntrancePosition ****************
struct Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics
{
	struct BaronAuracronManager_eventGetClosestEntrancePosition_Parms
	{
		FVector FromPosition;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Hexagonal Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FromPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FromPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::NewProp_FromPosition = { "FromPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventGetClosestEntrancePosition_Parms, FromPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FromPosition_MetaData), NewProp_FromPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventGetClosestEntrancePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::NewProp_FromPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "GetClosestEntrancePosition", Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::BaronAuracronManager_eventGetClosestEntrancePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::BaronAuracronManager_eventGetClosestEntrancePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execGetClosestEntrancePosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_FromPosition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetClosestEntrancePosition(Z_Param_Out_FromPosition);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function GetClosestEntrancePosition ******************

// ********** Begin Class ABaronAuracronManager Function GetHexagonalEntrances *********************
struct Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics
{
	struct BaronAuracronManager_eventGetHexagonalEntrances_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Hexagonal Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventGetHexagonalEntrances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "GetHexagonalEntrances", Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::BaronAuracronManager_eventGetHexagonalEntrances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::BaronAuracronManager_eventGetHexagonalEntrances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execGetHexagonalEntrances)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetHexagonalEntrances();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function GetHexagonalEntrances ***********************

// ********** Begin Class ABaronAuracronManager Function GetPlayersInBuffRange *********************
struct Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics
{
	struct BaronAuracronManager_eventGetPlayersInBuffRange_Parms
	{
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Buff System" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventGetPlayersInBuffRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "GetPlayersInBuffRange", Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::BaronAuracronManager_eventGetPlayersInBuffRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::BaronAuracronManager_eventGetPlayersInBuffRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execGetPlayersInBuffRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetPlayersInBuffRange();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function GetPlayersInBuffRange ***********************

// ********** Begin Class ABaronAuracronManager Function GetTimeUntilRespawn ***********************
struct Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics
{
	struct BaronAuracronManager_eventGetTimeUntilRespawn_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventGetTimeUntilRespawn_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "GetTimeUntilRespawn", Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::BaronAuracronManager_eventGetTimeUntilRespawn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::BaronAuracronManager_eventGetTimeUntilRespawn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execGetTimeUntilRespawn)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeUntilRespawn();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function GetTimeUntilRespawn *************************

// ********** Begin Class ABaronAuracronManager Function GetTimeUntilSpawn *************************
struct Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics
{
	struct BaronAuracronManager_eventGetTimeUntilSpawn_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventGetTimeUntilSpawn_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "GetTimeUntilSpawn", Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::BaronAuracronManager_eventGetTimeUntilSpawn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::BaronAuracronManager_eventGetTimeUntilSpawn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execGetTimeUntilSpawn)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeUntilSpawn();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function GetTimeUntilSpawn ***************************

// ********** Begin Class ABaronAuracronManager Function InitializeSentinels ***********************
struct Z_Construct_UFunction_ABaronAuracronManager_InitializeSentinels_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sentinels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de sentinelas e guardi\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de sentinelas e guardi\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_InitializeSentinels_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "InitializeSentinels", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_InitializeSentinels_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_InitializeSentinels_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_InitializeSentinels()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_InitializeSentinels_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execInitializeSentinels)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeSentinels();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function InitializeSentinels *************************

// ********** Begin Class ABaronAuracronManager Function IsPositionInHexagon ***********************
struct Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics
{
	struct BaronAuracronManager_eventIsPositionInHexagon_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Hexagonal Geometry" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventIsPositionInHexagon_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((BaronAuracronManager_eventIsPositionInHexagon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(BaronAuracronManager_eventIsPositionInHexagon_Parms), &Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "IsPositionInHexagon", Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::BaronAuracronManager_eventIsPositionInHexagon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::BaronAuracronManager_eventIsPositionInHexagon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execIsPositionInHexagon)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInHexagon(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function IsPositionInHexagon *************************

// ********** Begin Class ABaronAuracronManager Function OnBaronDeath ******************************
struct Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics
{
	struct BaronAuracronManager_eventOnBaronDeath_Parms
	{
		AActor* Killer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventOnBaronDeath_Parms, Killer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::NewProp_Killer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "OnBaronDeath", Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::BaronAuracronManager_eventOnBaronDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::BaronAuracronManager_eventOnBaronDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execOnBaronDeath)
{
	P_GET_OBJECT(AActor,Z_Param_Killer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnBaronDeath(Z_Param_Killer);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function OnBaronDeath ********************************

// ********** Begin Class ABaronAuracronManager Function PerformBaronRotation **********************
struct Z_Construct_UFunction_ABaronAuracronManager_PerformBaronRotation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Baron Management" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_PerformBaronRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "PerformBaronRotation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_PerformBaronRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_PerformBaronRotation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_PerformBaronRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_PerformBaronRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execPerformBaronRotation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformBaronRotation();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function PerformBaronRotation ************************

// ********** Begin Class ABaronAuracronManager Function RemoveTeamBuff ****************************
struct Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics
{
	struct BaronAuracronManager_eventRemoveTeamBuff_Parms
	{
		int32 TeamID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Buff System" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventRemoveTeamBuff_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::NewProp_TeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "RemoveTeamBuff", Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::BaronAuracronManager_eventRemoveTeamBuff_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::BaronAuracronManager_eventRemoveTeamBuff_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execRemoveTeamBuff)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveTeamBuff(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function RemoveTeamBuff ******************************

// ********** Begin Class ABaronAuracronManager Function RespawnSentinel ***************************
struct Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics
{
	struct BaronAuracronManager_eventRespawnSentinel_Parms
	{
		int32 SentinelIndex;
		ESentinelType Type;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sentinels" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SentinelIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::NewProp_SentinelIndex = { "SentinelIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventRespawnSentinel_Parms, SentinelIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventRespawnSentinel_Parms, Type), Z_Construct_UEnum_Aura_ESentinelType, METADATA_PARAMS(0, nullptr) }; // 4164581287
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::NewProp_SentinelIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::NewProp_Type,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "RespawnSentinel", Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::BaronAuracronManager_eventRespawnSentinel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::BaronAuracronManager_eventRespawnSentinel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execRespawnSentinel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SentinelIndex);
	P_GET_ENUM(ESentinelType,Z_Param_Type);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RespawnSentinel(Z_Param_SentinelIndex,ESentinelType(Z_Param_Type));
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function RespawnSentinel *****************************

// ********** Begin Class ABaronAuracronManager Function SetBaronState *****************************
struct Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics
{
	struct BaronAuracronManager_eventSetBaronState_Parms
	{
		EBaronState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventSetBaronState_Parms, NewState), Z_Construct_UEnum_Aura_EBaronState, METADATA_PARAMS(0, nullptr) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "SetBaronState", Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::BaronAuracronManager_eventSetBaronState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::BaronAuracronManager_eventSetBaronState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_SetBaronState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_SetBaronState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execSetBaronState)
{
	P_GET_ENUM(EBaronState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBaronState(EBaronState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function SetBaronState *******************************

// ********** Begin Class ABaronAuracronManager Function ShouldSpawnBaron **************************
struct Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics
{
	struct BaronAuracronManager_eventShouldSpawnBaron_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de timing e estado\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de timing e estado" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((BaronAuracronManager_eventShouldSpawnBaron_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(BaronAuracronManager_eventShouldSpawnBaron_Parms), &Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "ShouldSpawnBaron", Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::BaronAuracronManager_eventShouldSpawnBaron_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::BaronAuracronManager_eventShouldSpawnBaron_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execShouldSpawnBaron)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldSpawnBaron();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function ShouldSpawnBaron ****************************

// ********** Begin Class ABaronAuracronManager Function SpawnBaron ********************************
struct Z_Construct_UFunction_ABaronAuracronManager_SpawnBaron_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Baron Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de spawn e controle do Bar\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de spawn e controle do Bar\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_SpawnBaron_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "SpawnBaron", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_SpawnBaron_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_SpawnBaron_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_SpawnBaron()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_SpawnBaron_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execSpawnBaron)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnBaron();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function SpawnBaron **********************************

// ********** Begin Class ABaronAuracronManager Function SpawnGuardioesPortais *********************
struct Z_Construct_UFunction_ABaronAuracronManager_SpawnGuardioesPortais_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sentinels" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_SpawnGuardioesPortais_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "SpawnGuardioesPortais", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_SpawnGuardioesPortais_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_SpawnGuardioesPortais_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_SpawnGuardioesPortais()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_SpawnGuardioesPortais_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execSpawnGuardioesPortais)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnGuardioesPortais();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function SpawnGuardioesPortais ***********************

// ********** Begin Class ABaronAuracronManager Function SpawnSentinelasCristalinas ****************
struct Z_Construct_UFunction_ABaronAuracronManager_SpawnSentinelasCristalinas_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sentinels" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_SpawnSentinelasCristalinas_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "SpawnSentinelasCristalinas", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_SpawnSentinelasCristalinas_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_SpawnSentinelasCristalinas_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_SpawnSentinelasCristalinas()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_SpawnSentinelasCristalinas_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execSpawnSentinelasCristalinas)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnSentinelasCristalinas();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function SpawnSentinelasCristalinas ******************

// ********** Begin Class ABaronAuracronManager Function StartBaronRotation ************************
struct Z_Construct_UFunction_ABaronAuracronManager_StartBaronRotation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Baron Management" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_StartBaronRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "StartBaronRotation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_StartBaronRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_StartBaronRotation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_StartBaronRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_StartBaronRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execStartBaronRotation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartBaronRotation();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function StartBaronRotation **************************

// ********** Begin Class ABaronAuracronManager Function StartCombat *******************************
struct Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics
{
	struct BaronAuracronManager_eventStartCombat_Parms
	{
		AActor* Attacker;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de combate\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de combate" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Attacker;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::NewProp_Attacker = { "Attacker", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventStartCombat_Parms, Attacker), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::NewProp_Attacker,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "StartCombat", Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::BaronAuracronManager_eventStartCombat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::BaronAuracronManager_eventStartCombat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_StartCombat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_StartCombat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execStartCombat)
{
	P_GET_OBJECT(AActor,Z_Param_Attacker);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartCombat(Z_Param_Attacker);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function StartCombat *********************************

// ********** Begin Class ABaronAuracronManager Function TakeDamage ********************************
struct Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics
{
	struct BaronAuracronManager_eventTakeDamage_Parms
	{
		float DamageAmount;
		FDamageEvent DamageEvent;
		AController* EventInstigator;
		AActor* DamageCauser;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageEvent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageEvent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EventInstigator;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageCauser;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventTakeDamage_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_DamageEvent = { "DamageEvent", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventTakeDamage_Parms, DamageEvent), Z_Construct_UScriptStruct_FDamageEvent, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageEvent_MetaData), NewProp_DamageEvent_MetaData) }; // 2615510189
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_EventInstigator = { "EventInstigator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventTakeDamage_Parms, EventInstigator), Z_Construct_UClass_AController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_DamageCauser = { "DamageCauser", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventTakeDamage_Parms, DamageCauser), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaronAuracronManager_eventTakeDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_DamageEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_EventInstigator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_DamageCauser,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "TakeDamage", Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::BaronAuracronManager_eventTakeDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::BaronAuracronManager_eventTakeDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_TakeDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_TakeDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execTakeDamage)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_STRUCT_REF(FDamageEvent,Z_Param_Out_DamageEvent);
	P_GET_OBJECT(AController,Z_Param_EventInstigator);
	P_GET_OBJECT(AActor,Z_Param_DamageCauser);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->TakeDamage(Z_Param_DamageAmount,Z_Param_Out_DamageEvent,Z_Param_EventInstigator,Z_Param_DamageCauser);
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function TakeDamage **********************************

// ********** Begin Class ABaronAuracronManager Function UpdateBaronHealth *************************
struct Z_Construct_UFunction_ABaronAuracronManager_UpdateBaronHealth_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Baron Management" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_UpdateBaronHealth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "UpdateBaronHealth", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_UpdateBaronHealth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_UpdateBaronHealth_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_UpdateBaronHealth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_UpdateBaronHealth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execUpdateBaronHealth)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateBaronHealth();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function UpdateBaronHealth ***************************

// ********** Begin Class ABaronAuracronManager Function UpdateSentinelRotations *******************
struct Z_Construct_UFunction_ABaronAuracronManager_UpdateSentinelRotations_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sentinels" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_UpdateSentinelRotations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "UpdateSentinelRotations", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_UpdateSentinelRotations_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_UpdateSentinelRotations_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ABaronAuracronManager_UpdateSentinelRotations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_UpdateSentinelRotations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execUpdateSentinelRotations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSentinelRotations();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function UpdateSentinelRotations *********************

// ********** Begin Class ABaronAuracronManager Function ValidateHexagonalGeometry *****************
struct Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics
{
	struct BaronAuracronManager_eventValidateHexagonalGeometry_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((BaronAuracronManager_eventValidateHexagonalGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(BaronAuracronManager_eventValidateHexagonalGeometry_Parms), &Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ABaronAuracronManager, nullptr, "ValidateHexagonalGeometry", Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::BaronAuracronManager_eventValidateHexagonalGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::BaronAuracronManager_eventValidateHexagonalGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaronAuracronManager::execValidateHexagonalGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateHexagonalGeometry();
	P_NATIVE_END;
}
// ********** End Class ABaronAuracronManager Function ValidateHexagonalGeometry *******************

// ********** Begin Class ABaronAuracronManager ****************************************************
void ABaronAuracronManager::StaticRegisterNativesABaronAuracronManager()
{
	UClass* Class = ABaronAuracronManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyTeamBuff", &ABaronAuracronManager::execApplyTeamBuff },
		{ "CalculateHexagonalArea", &ABaronAuracronManager::execCalculateHexagonalArea },
		{ "CalculateHexagonalVertices", &ABaronAuracronManager::execCalculateHexagonalVertices },
		{ "DespawnBaron", &ABaronAuracronManager::execDespawnBaron },
		{ "DistributeRewards", &ABaronAuracronManager::execDistributeRewards },
		{ "DrawDebugHexagon", &ABaronAuracronManager::execDrawDebugHexagon },
		{ "DrawDebugSentinels", &ABaronAuracronManager::execDrawDebugSentinels },
		{ "EndCombat", &ABaronAuracronManager::execEndCombat },
		{ "GetBaronState", &ABaronAuracronManager::execGetBaronState },
		{ "GetClosestEntrancePosition", &ABaronAuracronManager::execGetClosestEntrancePosition },
		{ "GetHexagonalEntrances", &ABaronAuracronManager::execGetHexagonalEntrances },
		{ "GetPlayersInBuffRange", &ABaronAuracronManager::execGetPlayersInBuffRange },
		{ "GetTimeUntilRespawn", &ABaronAuracronManager::execGetTimeUntilRespawn },
		{ "GetTimeUntilSpawn", &ABaronAuracronManager::execGetTimeUntilSpawn },
		{ "InitializeSentinels", &ABaronAuracronManager::execInitializeSentinels },
		{ "IsPositionInHexagon", &ABaronAuracronManager::execIsPositionInHexagon },
		{ "OnBaronDeath", &ABaronAuracronManager::execOnBaronDeath },
		{ "PerformBaronRotation", &ABaronAuracronManager::execPerformBaronRotation },
		{ "RemoveTeamBuff", &ABaronAuracronManager::execRemoveTeamBuff },
		{ "RespawnSentinel", &ABaronAuracronManager::execRespawnSentinel },
		{ "SetBaronState", &ABaronAuracronManager::execSetBaronState },
		{ "ShouldSpawnBaron", &ABaronAuracronManager::execShouldSpawnBaron },
		{ "SpawnBaron", &ABaronAuracronManager::execSpawnBaron },
		{ "SpawnGuardioesPortais", &ABaronAuracronManager::execSpawnGuardioesPortais },
		{ "SpawnSentinelasCristalinas", &ABaronAuracronManager::execSpawnSentinelasCristalinas },
		{ "StartBaronRotation", &ABaronAuracronManager::execStartBaronRotation },
		{ "StartCombat", &ABaronAuracronManager::execStartCombat },
		{ "TakeDamage", &ABaronAuracronManager::execTakeDamage },
		{ "UpdateBaronHealth", &ABaronAuracronManager::execUpdateBaronHealth },
		{ "UpdateSentinelRotations", &ABaronAuracronManager::execUpdateSentinelRotations },
		{ "ValidateHexagonalGeometry", &ABaronAuracronManager::execValidateHexagonalGeometry },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ABaronAuracronManager;
UClass* ABaronAuracronManager::GetPrivateStaticClass()
{
	using TClass = ABaronAuracronManager;
	if (!Z_Registration_Info_UClass_ABaronAuracronManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("BaronAuracronManager"),
			Z_Registration_Info_UClass_ABaronAuracronManager.InnerSingleton,
			StaticRegisterNativesABaronAuracronManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ABaronAuracronManager.InnerSingleton;
}
UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister()
{
	return ABaronAuracronManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ABaronAuracronManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "ABaronAuracronManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes principais\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaronMesh_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatArea_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffArea_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaronData_MetaData[] = {
		{ "Category", "Baron Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados do Bar\xc3\xa3o Auracron\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados do Bar\xc3\xa3o Auracron" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HexagonalCovil_MetaData[] = {
		{ "Category", "Hexagonal Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Geometria hexagonal do covil\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Geometria hexagonal do covil" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SentinelasCristalinas_MetaData[] = {
		{ "Category", "Sentinels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sentinelas e guardi\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sentinelas e guardi\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GuardioesPortais_MetaData[] = {
		{ "Category", "Sentinels" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamBuff_MetaData[] = {
		{ "Category", "Buff System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de buffs\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de buffs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameStartTime_MetaData[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de timing\n" },
#endif
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de timing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentGameTime_MetaData[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/ABaronAuracronManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaronMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CombatArea;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BuffArea;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaronData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HexagonalCovil;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SentinelasCristalinas_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SentinelasCristalinas;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GuardioesPortais_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GuardioesPortais;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TeamBuff;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GameStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentGameTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ABaronAuracronManager_ApplyTeamBuff, "ApplyTeamBuff" }, // 2556364226
		{ &Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalArea, "CalculateHexagonalArea" }, // 1135238692
		{ &Z_Construct_UFunction_ABaronAuracronManager_CalculateHexagonalVertices, "CalculateHexagonalVertices" }, // 847240144
		{ &Z_Construct_UFunction_ABaronAuracronManager_DespawnBaron, "DespawnBaron" }, // 366286894
		{ &Z_Construct_UFunction_ABaronAuracronManager_DistributeRewards, "DistributeRewards" }, // 740545190
		{ &Z_Construct_UFunction_ABaronAuracronManager_DrawDebugHexagon, "DrawDebugHexagon" }, // 2177798065
		{ &Z_Construct_UFunction_ABaronAuracronManager_DrawDebugSentinels, "DrawDebugSentinels" }, // 4119130999
		{ &Z_Construct_UFunction_ABaronAuracronManager_EndCombat, "EndCombat" }, // 434641328
		{ &Z_Construct_UFunction_ABaronAuracronManager_GetBaronState, "GetBaronState" }, // 4224217164
		{ &Z_Construct_UFunction_ABaronAuracronManager_GetClosestEntrancePosition, "GetClosestEntrancePosition" }, // 1422892069
		{ &Z_Construct_UFunction_ABaronAuracronManager_GetHexagonalEntrances, "GetHexagonalEntrances" }, // 195144221
		{ &Z_Construct_UFunction_ABaronAuracronManager_GetPlayersInBuffRange, "GetPlayersInBuffRange" }, // 1881319857
		{ &Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilRespawn, "GetTimeUntilRespawn" }, // 3307531324
		{ &Z_Construct_UFunction_ABaronAuracronManager_GetTimeUntilSpawn, "GetTimeUntilSpawn" }, // 2887407830
		{ &Z_Construct_UFunction_ABaronAuracronManager_InitializeSentinels, "InitializeSentinels" }, // 3929159136
		{ &Z_Construct_UFunction_ABaronAuracronManager_IsPositionInHexagon, "IsPositionInHexagon" }, // 85427317
		{ &Z_Construct_UFunction_ABaronAuracronManager_OnBaronDeath, "OnBaronDeath" }, // 1854262982
		{ &Z_Construct_UFunction_ABaronAuracronManager_PerformBaronRotation, "PerformBaronRotation" }, // 2383892991
		{ &Z_Construct_UFunction_ABaronAuracronManager_RemoveTeamBuff, "RemoveTeamBuff" }, // 4189044177
		{ &Z_Construct_UFunction_ABaronAuracronManager_RespawnSentinel, "RespawnSentinel" }, // 3108564568
		{ &Z_Construct_UFunction_ABaronAuracronManager_SetBaronState, "SetBaronState" }, // 3125008070
		{ &Z_Construct_UFunction_ABaronAuracronManager_ShouldSpawnBaron, "ShouldSpawnBaron" }, // 3386322760
		{ &Z_Construct_UFunction_ABaronAuracronManager_SpawnBaron, "SpawnBaron" }, // 1498527067
		{ &Z_Construct_UFunction_ABaronAuracronManager_SpawnGuardioesPortais, "SpawnGuardioesPortais" }, // **********
		{ &Z_Construct_UFunction_ABaronAuracronManager_SpawnSentinelasCristalinas, "SpawnSentinelasCristalinas" }, // **********
		{ &Z_Construct_UFunction_ABaronAuracronManager_StartBaronRotation, "StartBaronRotation" }, // **********
		{ &Z_Construct_UFunction_ABaronAuracronManager_StartCombat, "StartCombat" }, // **********
		{ &Z_Construct_UFunction_ABaronAuracronManager_TakeDamage, "TakeDamage" }, // **********
		{ &Z_Construct_UFunction_ABaronAuracronManager_UpdateBaronHealth, "UpdateBaronHealth" }, // 781529487
		{ &Z_Construct_UFunction_ABaronAuracronManager_UpdateSentinelRotations, "UpdateSentinelRotations" }, // 445413892
		{ &Z_Construct_UFunction_ABaronAuracronManager_ValidateHexagonalGeometry, "ValidateHexagonalGeometry" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ABaronAuracronManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_BaronMesh = { "BaronMesh", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, BaronMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaronMesh_MetaData), NewProp_BaronMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_CombatArea = { "CombatArea", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, CombatArea), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatArea_MetaData), NewProp_CombatArea_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_BuffArea = { "BuffArea", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, BuffArea), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffArea_MetaData), NewProp_BuffArea_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_BaronData = { "BaronData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, BaronData), Z_Construct_UScriptStruct_FBaronData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaronData_MetaData), NewProp_BaronData_MetaData) }; // 352989431
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_HexagonalCovil = { "HexagonalCovil", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, HexagonalCovil), Z_Construct_UScriptStruct_FHexagonalArea, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HexagonalCovil_MetaData), NewProp_HexagonalCovil_MetaData) }; // 3289984925
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_SentinelasCristalinas_Inner = { "SentinelasCristalinas", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSentinelData, METADATA_PARAMS(0, nullptr) }; // 40284613
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_SentinelasCristalinas = { "SentinelasCristalinas", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, SentinelasCristalinas), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SentinelasCristalinas_MetaData), NewProp_SentinelasCristalinas_MetaData) }; // 40284613
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_GuardioesPortais_Inner = { "GuardioesPortais", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSentinelData, METADATA_PARAMS(0, nullptr) }; // 40284613
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_GuardioesPortais = { "GuardioesPortais", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, GuardioesPortais), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GuardioesPortais_MetaData), NewProp_GuardioesPortais_MetaData) }; // 40284613
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_TeamBuff = { "TeamBuff", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, TeamBuff), Z_Construct_UScriptStruct_FBuffData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamBuff_MetaData), NewProp_TeamBuff_MetaData) }; // 67838597
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_GameStartTime = { "GameStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, GameStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameStartTime_MetaData), NewProp_GameStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_CurrentGameTime = { "CurrentGameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaronAuracronManager, CurrentGameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentGameTime_MetaData), NewProp_CurrentGameTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ABaronAuracronManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_BaronMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_CombatArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_BuffArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_BaronData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_HexagonalCovil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_SentinelasCristalinas_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_SentinelasCristalinas,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_GuardioesPortais_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_GuardioesPortais,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_TeamBuff,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_GameStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaronAuracronManager_Statics::NewProp_CurrentGameTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABaronAuracronManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ABaronAuracronManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABaronAuracronManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ABaronAuracronManager_Statics::ClassParams = {
	&ABaronAuracronManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ABaronAuracronManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ABaronAuracronManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ABaronAuracronManager_Statics::Class_MetaDataParams), Z_Construct_UClass_ABaronAuracronManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ABaronAuracronManager()
{
	if (!Z_Registration_Info_UClass_ABaronAuracronManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ABaronAuracronManager.OuterSingleton, Z_Construct_UClass_ABaronAuracronManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ABaronAuracronManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ABaronAuracronManager);
ABaronAuracronManager::~ABaronAuracronManager() {}
// ********** End Class ABaronAuracronManager ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EBaronState_StaticEnum, TEXT("EBaronState"), &Z_Registration_Info_UEnum_EBaronState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, **********U) },
		{ ESentinelType_StaticEnum, TEXT("ESentinelType"), &Z_Registration_Info_UEnum_ESentinelType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4164581287U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FBaronData::StaticStruct, Z_Construct_UScriptStruct_FBaronData_Statics::NewStructOps, TEXT("BaronData"), &Z_Registration_Info_UScriptStruct_FBaronData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBaronData), 352989431U) },
		{ FHexagonalArea::StaticStruct, Z_Construct_UScriptStruct_FHexagonalArea_Statics::NewStructOps, TEXT("HexagonalArea"), &Z_Registration_Info_UScriptStruct_FHexagonalArea, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHexagonalArea), 3289984925U) },
		{ FSentinelData::StaticStruct, Z_Construct_UScriptStruct_FSentinelData_Statics::NewStructOps, TEXT("SentinelData"), &Z_Registration_Info_UScriptStruct_FSentinelData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSentinelData), 40284613U) },
		{ FBuffData::StaticStruct, Z_Construct_UScriptStruct_FBuffData_Statics::NewStructOps, TEXT("BuffData"), &Z_Registration_Info_UScriptStruct_FBuffData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBuffData), 67838597U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ABaronAuracronManager, ABaronAuracronManager::StaticClass, TEXT("ABaronAuracronManager"), &Z_Registration_Info_UClass_ABaronAuracronManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ABaronAuracronManager), 2651712U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_2549865014(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ABaronAuracronManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
