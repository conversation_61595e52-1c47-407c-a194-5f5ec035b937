// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "ADragonPrismalManager.h"
#include "Engine/DamageEvents.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeADragonPrismalManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager();
AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EDragonElement();
AURA_API UEnum* Z_Construct_UEnum_Aura_EDragonState();
AURA_API UEnum* Z_Construct_UEnum_Aura_EDragonType();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FDragonAttack();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FDragonData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FDragonReward();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FEllipticalArea();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FFlightPath();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AController_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FDamageEvent();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EDragonState **************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDragonState;
static UEnum* EDragonState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDragonState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDragonState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EDragonState, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EDragonState"));
	}
	return Z_Registration_Info_UEnum_EDragonState.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EDragonState>()
{
	return EDragonState_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EDragonState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Ativo" },
		{ "Active.Name", "EDragonState::Active" },
		{ "BlueprintType", "true" },
		{ "Combat.DisplayName", "Em Combate" },
		{ "Combat.Name", "EDragonState::Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para estados e tipos do Drag\xc3\xa3o\n" },
#endif
		{ "Dead.DisplayName", "Morto" },
		{ "Dead.Name", "EDragonState::Dead" },
		{ "Dormant.DisplayName", "Dormindo" },
		{ "Dormant.Name", "EDragonState::Dormant" },
		{ "Flying.DisplayName", "Voando" },
		{ "Flying.Name", "EDragonState::Flying" },
		{ "Landing.DisplayName", "Pousando" },
		{ "Landing.Name", "EDragonState::Landing" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
		{ "Spawning.DisplayName", "Aparecendo" },
		{ "Spawning.Name", "EDragonState::Spawning" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para estados e tipos do Drag\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDragonState::Dormant", (int64)EDragonState::Dormant },
		{ "EDragonState::Spawning", (int64)EDragonState::Spawning },
		{ "EDragonState::Active", (int64)EDragonState::Active },
		{ "EDragonState::Combat", (int64)EDragonState::Combat },
		{ "EDragonState::Flying", (int64)EDragonState::Flying },
		{ "EDragonState::Landing", (int64)EDragonState::Landing },
		{ "EDragonState::Dead", (int64)EDragonState::Dead },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EDragonState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EDragonState",
	"EDragonState",
	Z_Construct_UEnum_Aura_EDragonState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EDragonState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EDragonState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EDragonState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EDragonState()
{
	if (!Z_Registration_Info_UEnum_EDragonState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDragonState.InnerSingleton, Z_Construct_UEnum_Aura_EDragonState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDragonState.InnerSingleton;
}
// ********** End Enum EDragonState ****************************************************************

// ********** Begin Enum EDragonType ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDragonType;
static UEnum* EDragonType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDragonType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDragonType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EDragonType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EDragonType"));
	}
	return Z_Registration_Info_UEnum_EDragonType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EDragonType>()
{
	return EDragonType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EDragonType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cloud.DisplayName", "Drag\xc3\xa3o das Nuvens" },
		{ "Cloud.Name", "EDragonType::Cloud" },
		{ "Infernal.DisplayName", "Drag\xc3\xa3o Infernal" },
		{ "Infernal.Name", "EDragonType::Infernal" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
		{ "Mountain.DisplayName", "Drag\xc3\xa3o da Montanha" },
		{ "Mountain.Name", "EDragonType::Mountain" },
		{ "Ocean.DisplayName", "Drag\xc3\xa3o Oce\xc3\xa2nico" },
		{ "Ocean.Name", "EDragonType::Ocean" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDragonType::Infernal", (int64)EDragonType::Infernal },
		{ "EDragonType::Ocean", (int64)EDragonType::Ocean },
		{ "EDragonType::Mountain", (int64)EDragonType::Mountain },
		{ "EDragonType::Cloud", (int64)EDragonType::Cloud },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EDragonType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EDragonType",
	"EDragonType",
	Z_Construct_UEnum_Aura_EDragonType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EDragonType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EDragonType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EDragonType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EDragonType()
{
	if (!Z_Registration_Info_UEnum_EDragonType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDragonType.InnerSingleton, Z_Construct_UEnum_Aura_EDragonType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDragonType.InnerSingleton;
}
// ********** End Enum EDragonType *****************************************************************

// ********** Begin Enum EDragonElement ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDragonElement;
static UEnum* EDragonElement_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDragonElement.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDragonElement.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EDragonElement, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EDragonElement"));
	}
	return Z_Registration_Info_UEnum_EDragonElement.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EDragonElement>()
{
	return EDragonElement_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EDragonElement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Air.DisplayName", "Ar" },
		{ "Air.Name", "EDragonElement::Air" },
		{ "BlueprintType", "true" },
		{ "Earth.DisplayName", "Terra" },
		{ "Earth.Name", "EDragonElement::Earth" },
		{ "Fire.DisplayName", "Fogo" },
		{ "Fire.Name", "EDragonElement::Fire" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
		{ "Water.DisplayName", "\xc3\x81gua" },
		{ "Water.Name", "EDragonElement::Water" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDragonElement::Fire", (int64)EDragonElement::Fire },
		{ "EDragonElement::Water", (int64)EDragonElement::Water },
		{ "EDragonElement::Earth", (int64)EDragonElement::Earth },
		{ "EDragonElement::Air", (int64)EDragonElement::Air },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EDragonElement_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EDragonElement",
	"EDragonElement",
	Z_Construct_UEnum_Aura_EDragonElement_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EDragonElement_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EDragonElement_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EDragonElement_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EDragonElement()
{
	if (!Z_Registration_Info_UEnum_EDragonElement.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDragonElement.InnerSingleton, Z_Construct_UEnum_Aura_EDragonElement_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDragonElement.InnerSingleton;
}
// ********** End Enum EDragonElement **************************************************************

// ********** Begin ScriptStruct FDragonData *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FDragonData;
class UScriptStruct* FDragonData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FDragonData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FDragonData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDragonData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("DragonData"));
	}
	return Z_Registration_Info_UScriptStruct_FDragonData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FDragonData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para o Drag\xc3\xa3o e sistema de recompensas\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para o Drag\xc3\xa3o e sistema de recompensas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Element_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseHealth_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthScaling_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnTime_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlightHeight_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlightSpeed_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "Dragon" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Element_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Element;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlightHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlightSpeed;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDragonData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, Type), Z_Construct_UEnum_Aura_EDragonType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) }; // 2165795084
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Element_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Element = { "Element", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, Element), Z_Construct_UEnum_Aura_EDragonElement, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Element_MetaData), NewProp_Element_MetaData) }; // **********
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_BaseHealth = { "BaseHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, BaseHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseHealth_MetaData), NewProp_BaseHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_HealthScaling = { "HealthScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, HealthScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthScaling_MetaData), NewProp_HealthScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_SpawnTime = { "SpawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, SpawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnTime_MetaData), NewProp_SpawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_FlightHeight = { "FlightHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, FlightHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlightHeight_MetaData), NewProp_FlightHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_FlightSpeed = { "FlightSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, FlightSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlightSpeed_MetaData), NewProp_FlightSpeed_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonData, CurrentState), Z_Construct_UEnum_Aura_EDragonState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 3515719245
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDragonData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Element_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_Element,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_BaseHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_HealthScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_SpawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_FlightHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_FlightSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonData_Statics::NewProp_CurrentState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDragonData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"DragonData",
	Z_Construct_UScriptStruct_FDragonData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonData_Statics::PropPointers),
	sizeof(FDragonData),
	alignof(FDragonData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDragonData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDragonData()
{
	if (!Z_Registration_Info_UScriptStruct_FDragonData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FDragonData.InnerSingleton, Z_Construct_UScriptStruct_FDragonData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FDragonData.InnerSingleton;
}
// ********** End ScriptStruct FDragonData *********************************************************

// ********** Begin ScriptStruct FEllipticalArea ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FEllipticalArea;
class UScriptStruct* FEllipticalArea::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FEllipticalArea.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FEllipticalArea.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEllipticalArea, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EllipticalArea"));
	}
	return Z_Registration_Info_UScriptStruct_FEllipticalArea.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FEllipticalArea_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SemiMajorAxis_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SemiMinorAxis_MetaData[] = {
		{ "Category", "Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eixo maior (a)\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eixo maior (a)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Eccentricity_MetaData[] = {
		{ "Category", "Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eixo menor (b)\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eixo menor (b)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "Category", "Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Excentricidade da elipse\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Excentricidade da elipse" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Perimeter_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EllipsePoints_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandingZones_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationAngle_MetaData[] = {
		{ "Category", "Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiMajorAxis;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiMinorAxis;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Eccentricity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Area;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Perimeter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EllipsePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EllipsePoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LandingZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LandingZones;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationAngle;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEllipticalArea>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_SemiMajorAxis = { "SemiMajorAxis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, SemiMajorAxis), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SemiMajorAxis_MetaData), NewProp_SemiMajorAxis_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_SemiMinorAxis = { "SemiMinorAxis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, SemiMinorAxis), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SemiMinorAxis_MetaData), NewProp_SemiMinorAxis_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Eccentricity = { "Eccentricity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, Eccentricity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Eccentricity_MetaData), NewProp_Eccentricity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, Area), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Perimeter = { "Perimeter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, Perimeter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Perimeter_MetaData), NewProp_Perimeter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_EllipsePoints_Inner = { "EllipsePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_EllipsePoints = { "EllipsePoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, EllipsePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EllipsePoints_MetaData), NewProp_EllipsePoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_LandingZones_Inner = { "LandingZones", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_LandingZones = { "LandingZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, LandingZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandingZones_MetaData), NewProp_LandingZones_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_RotationAngle = { "RotationAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEllipticalArea, RotationAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationAngle_MetaData), NewProp_RotationAngle_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEllipticalArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_SemiMajorAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_SemiMinorAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Eccentricity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_Perimeter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_EllipsePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_EllipsePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_LandingZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_LandingZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewProp_RotationAngle,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEllipticalArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEllipticalArea_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"EllipticalArea",
	Z_Construct_UScriptStruct_FEllipticalArea_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEllipticalArea_Statics::PropPointers),
	sizeof(FEllipticalArea),
	alignof(FEllipticalArea),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEllipticalArea_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEllipticalArea_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEllipticalArea()
{
	if (!Z_Registration_Info_UScriptStruct_FEllipticalArea.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FEllipticalArea.InnerSingleton, Z_Construct_UScriptStruct_FEllipticalArea_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FEllipticalArea.InnerSingleton;
}
// ********** End ScriptStruct FEllipticalArea *****************************************************

// ********** Begin ScriptStruct FDragonReward *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FDragonReward;
class UScriptStruct* FDragonReward::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FDragonReward.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FDragonReward.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDragonReward, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("DragonReward"));
	}
	return Z_Registration_Info_UScriptStruct_FDragonReward.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FDragonReward_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseGold_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseExperience_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldScaling_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceScaling_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamBonusMultiplier_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoloBonusMultiplier_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxParticipants_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticipationThreshold_MetaData[] = {
		{ "Category", "Reward" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseGold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseExperience;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GoldScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExperienceScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TeamBonusMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SoloBonusMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxParticipants;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ParticipationThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDragonReward>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_BaseGold = { "BaseGold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, BaseGold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseGold_MetaData), NewProp_BaseGold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_BaseExperience = { "BaseExperience", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, BaseExperience), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseExperience_MetaData), NewProp_BaseExperience_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_GoldScaling = { "GoldScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, GoldScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldScaling_MetaData), NewProp_GoldScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_ExperienceScaling = { "ExperienceScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, ExperienceScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceScaling_MetaData), NewProp_ExperienceScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_TeamBonusMultiplier = { "TeamBonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, TeamBonusMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamBonusMultiplier_MetaData), NewProp_TeamBonusMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_SoloBonusMultiplier = { "SoloBonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, SoloBonusMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoloBonusMultiplier_MetaData), NewProp_SoloBonusMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_MaxParticipants = { "MaxParticipants", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, MaxParticipants), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxParticipants_MetaData), NewProp_MaxParticipants_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_ParticipationThreshold = { "ParticipationThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonReward, ParticipationThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticipationThreshold_MetaData), NewProp_ParticipationThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDragonReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_BaseGold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_BaseExperience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_GoldScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_ExperienceScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_TeamBonusMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_SoloBonusMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_MaxParticipants,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonReward_Statics::NewProp_ParticipationThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDragonReward_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"DragonReward",
	Z_Construct_UScriptStruct_FDragonReward_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonReward_Statics::PropPointers),
	sizeof(FDragonReward),
	alignof(FDragonReward),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonReward_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDragonReward_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDragonReward()
{
	if (!Z_Registration_Info_UScriptStruct_FDragonReward.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FDragonReward.InnerSingleton, Z_Construct_UScriptStruct_FDragonReward_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FDragonReward.InnerSingleton;
}
// ********** End ScriptStruct FDragonReward *******************************************************

// ********** Begin ScriptStruct FDragonAttack *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FDragonAttack;
class UScriptStruct* FDragonAttack::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FDragonAttack.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FDragonAttack.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDragonAttack, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("DragonAttack"));
	}
	return Z_Registration_Info_UScriptStruct_FDragonAttack.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FDragonAttack_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackName_MetaData[] = {
		{ "Category", "Attack" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Damage_MetaData[] = {
		{ "Category", "Attack" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Range_MetaData[] = {
		{ "Category", "Attack" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Cooldown_MetaData[] = {
		{ "Category", "Attack" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Element_MetaData[] = {
		{ "Category", "Attack" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAreaAttack_MetaData[] = {
		{ "Category", "Attack" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaRadius_MetaData[] = {
		{ "Category", "Attack" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttackName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Range;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Cooldown;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Element_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Element;
	static void NewProp_bIsAreaAttack_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAreaAttack;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDragonAttack>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_AttackName = { "AttackName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonAttack, AttackName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackName_MetaData), NewProp_AttackName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonAttack, Damage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Damage_MetaData), NewProp_Damage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Range = { "Range", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonAttack, Range), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Range_MetaData), NewProp_Range_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Cooldown = { "Cooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonAttack, Cooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Cooldown_MetaData), NewProp_Cooldown_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Element_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Element = { "Element", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonAttack, Element), Z_Construct_UEnum_Aura_EDragonElement, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Element_MetaData), NewProp_Element_MetaData) }; // **********
void Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_bIsAreaAttack_SetBit(void* Obj)
{
	((FDragonAttack*)Obj)->bIsAreaAttack = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_bIsAreaAttack = { "bIsAreaAttack", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDragonAttack), &Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_bIsAreaAttack_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAreaAttack_MetaData), NewProp_bIsAreaAttack_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_AreaRadius = { "AreaRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragonAttack, AreaRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaRadius_MetaData), NewProp_AreaRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDragonAttack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_AttackName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Range,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Cooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Element_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_Element,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_bIsAreaAttack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragonAttack_Statics::NewProp_AreaRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonAttack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDragonAttack_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"DragonAttack",
	Z_Construct_UScriptStruct_FDragonAttack_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonAttack_Statics::PropPointers),
	sizeof(FDragonAttack),
	alignof(FDragonAttack),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragonAttack_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDragonAttack_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDragonAttack()
{
	if (!Z_Registration_Info_UScriptStruct_FDragonAttack.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FDragonAttack.InnerSingleton, Z_Construct_UScriptStruct_FDragonAttack_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FDragonAttack.InnerSingleton;
}
// ********** End ScriptStruct FDragonAttack *******************************************************

// ********** Begin ScriptStruct FFlightPath *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FFlightPath;
class UScriptStruct* FFlightPath::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FFlightPath.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FFlightPath.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FFlightPath, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("FlightPath"));
	}
	return Z_Registration_Info_UScriptStruct_FFlightPath.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FFlightPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Waypoints_MetaData[] = {
		{ "Category", "Flight" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathLength_MetaData[] = {
		{ "Category", "Flight" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlightDuration_MetaData[] = {
		{ "Category", "Flight" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCircular_MetaData[] = {
		{ "Category", "Flight" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWaypointIndex_MetaData[] = {
		{ "Category", "Flight" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Waypoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Waypoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlightDuration;
	static void NewProp_bIsCircular_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCircular;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentWaypointIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FFlightPath>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_Waypoints_Inner = { "Waypoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_Waypoints = { "Waypoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFlightPath, Waypoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Waypoints_MetaData), NewProp_Waypoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_PathLength = { "PathLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFlightPath, PathLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathLength_MetaData), NewProp_PathLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_FlightDuration = { "FlightDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFlightPath, FlightDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlightDuration_MetaData), NewProp_FlightDuration_MetaData) };
void Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_bIsCircular_SetBit(void* Obj)
{
	((FFlightPath*)Obj)->bIsCircular = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_bIsCircular = { "bIsCircular", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FFlightPath), &Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_bIsCircular_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCircular_MetaData), NewProp_bIsCircular_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_CurrentWaypointIndex = { "CurrentWaypointIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFlightPath, CurrentWaypointIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWaypointIndex_MetaData), NewProp_CurrentWaypointIndex_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FFlightPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_Waypoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_Waypoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_PathLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_FlightDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_bIsCircular,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFlightPath_Statics::NewProp_CurrentWaypointIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFlightPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FFlightPath_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"FlightPath",
	Z_Construct_UScriptStruct_FFlightPath_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFlightPath_Statics::PropPointers),
	sizeof(FFlightPath),
	alignof(FFlightPath),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFlightPath_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FFlightPath_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FFlightPath()
{
	if (!Z_Registration_Info_UScriptStruct_FFlightPath.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FFlightPath.InnerSingleton, Z_Construct_UScriptStruct_FFlightPath_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FFlightPath.InnerSingleton;
}
// ********** End ScriptStruct FFlightPath *********************************************************

// ********** Begin Class ADragonPrismalManager Function AdvanceToNextWaypoint *********************
struct Z_Construct_UFunction_ADragonPrismalManager_AdvanceToNextWaypoint_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flight System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_AdvanceToNextWaypoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "AdvanceToNextWaypoint", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_AdvanceToNextWaypoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_AdvanceToNextWaypoint_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_AdvanceToNextWaypoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_AdvanceToNextWaypoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execAdvanceToNextWaypoint)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdvanceToNextWaypoint();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function AdvanceToNextWaypoint ***********************

// ********** Begin Class ADragonPrismalManager Function CalculateAndDistributeRewards *************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateAndDistributeRewards_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de recompensas\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de recompensas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateAndDistributeRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateAndDistributeRewards", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateAndDistributeRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateAndDistributeRewards_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateAndDistributeRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateAndDistributeRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateAndDistributeRewards)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CalculateAndDistributeRewards();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateAndDistributeRewards ***************

// ********** Begin Class ADragonPrismalManager Function CalculateEccentricity *********************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics
{
	struct DragonPrismalManager_eventCalculateEccentricity_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateEccentricity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateEccentricity", Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::DragonPrismalManager_eventCalculateEccentricity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::DragonPrismalManager_eventCalculateEccentricity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateEccentricity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateEccentricity();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateEccentricity ***********************

// ********** Begin Class ADragonPrismalManager Function CalculateEllipticalArea *******************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics
{
	struct DragonPrismalManager_eventCalculateEllipticalArea_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateEllipticalArea_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateEllipticalArea", Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::DragonPrismalManager_eventCalculateEllipticalArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::DragonPrismalManager_eventCalculateEllipticalArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateEllipticalArea)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateEllipticalArea();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateEllipticalArea *********************

// ********** Begin Class ADragonPrismalManager Function CalculateEllipticalGeometry ***************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalGeometry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para geometria el\xc3\xadptica\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para geometria el\xc3\xadptica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateEllipticalGeometry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalGeometry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateEllipticalGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CalculateEllipticalGeometry();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateEllipticalGeometry *****************

// ********** Begin Class ADragonPrismalManager Function CalculateEllipticalPerimeter **************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics
{
	struct DragonPrismalManager_eventCalculateEllipticalPerimeter_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateEllipticalPerimeter_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateEllipticalPerimeter", Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::DragonPrismalManager_eventCalculateEllipticalPerimeter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::DragonPrismalManager_eventCalculateEllipticalPerimeter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateEllipticalPerimeter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateEllipticalPerimeter();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateEllipticalPerimeter ****************

// ********** Begin Class ADragonPrismalManager Function CalculateFlightPathLength *****************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics
{
	struct DragonPrismalManager_eventCalculateFlightPathLength_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flight System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateFlightPathLength_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateFlightPathLength", Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::DragonPrismalManager_eventCalculateFlightPathLength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::DragonPrismalManager_eventCalculateFlightPathLength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateFlightPathLength)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateFlightPathLength();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateFlightPathLength *******************

// ********** Begin Class ADragonPrismalManager Function CalculateLandingZones *********************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics
{
	struct DragonPrismalManager_eventCalculateLandingZones_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateLandingZones_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateLandingZones", Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::DragonPrismalManager_eventCalculateLandingZones_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::DragonPrismalManager_eventCalculateLandingZones_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateLandingZones)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateLandingZones();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateLandingZones ***********************

// ********** Begin Class ADragonPrismalManager Function CalculateParticipationPercentage **********
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics
{
	struct DragonPrismalManager_eventCalculateParticipationPercentage_Parms
	{
		AActor* Participant;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Participant;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::NewProp_Participant = { "Participant", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateParticipationPercentage_Parms, Participant), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateParticipationPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::NewProp_Participant,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateParticipationPercentage", Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::DragonPrismalManager_eventCalculateParticipationPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::DragonPrismalManager_eventCalculateParticipationPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateParticipationPercentage)
{
	P_GET_OBJECT(AActor,Z_Param_Participant);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateParticipationPercentage(Z_Param_Participant);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateParticipationPercentage ************

// ********** Begin Class ADragonPrismalManager Function CalculateSoloBonus ************************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics
{
	struct DragonPrismalManager_eventCalculateSoloBonus_Parms
	{
		AActor* SoloPlayer;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SoloPlayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::NewProp_SoloPlayer = { "SoloPlayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateSoloBonus_Parms, SoloPlayer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateSoloBonus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::NewProp_SoloPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateSoloBonus", Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::DragonPrismalManager_eventCalculateSoloBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::DragonPrismalManager_eventCalculateSoloBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateSoloBonus)
{
	P_GET_OBJECT(AActor,Z_Param_SoloPlayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateSoloBonus(Z_Param_SoloPlayer);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateSoloBonus **************************

// ********** Begin Class ADragonPrismalManager Function CalculateTeamBonus ************************
struct Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics
{
	struct DragonPrismalManager_eventCalculateTeamBonus_Parms
	{
		TArray<AActor*> TeamMembers;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamMembers_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TeamMembers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TeamMembers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::NewProp_TeamMembers_Inner = { "TeamMembers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::NewProp_TeamMembers = { "TeamMembers", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateTeamBonus_Parms, TeamMembers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamMembers_MetaData), NewProp_TeamMembers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventCalculateTeamBonus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::NewProp_TeamMembers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::NewProp_TeamMembers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "CalculateTeamBonus", Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::DragonPrismalManager_eventCalculateTeamBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::DragonPrismalManager_eventCalculateTeamBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execCalculateTeamBonus)
{
	P_GET_TARRAY_REF(AActor*,Z_Param_Out_TeamMembers);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTeamBonus(Z_Param_Out_TeamMembers);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function CalculateTeamBonus **************************

// ********** Begin Class ADragonPrismalManager Function DespawnDragon *****************************
struct Z_Construct_UFunction_ADragonPrismalManager_DespawnDragon_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Management" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_DespawnDragon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "DespawnDragon", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_DespawnDragon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_DespawnDragon_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_DespawnDragon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_DespawnDragon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execDespawnDragon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DespawnDragon();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function DespawnDragon *******************************

// ********** Begin Class ADragonPrismalManager Function DrawDebugEllipse **************************
struct Z_Construct_UFunction_ADragonPrismalManager_DrawDebugEllipse_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_DrawDebugEllipse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "DrawDebugEllipse", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_DrawDebugEllipse_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_DrawDebugEllipse_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_DrawDebugEllipse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_DrawDebugEllipse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execDrawDebugEllipse)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugEllipse();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function DrawDebugEllipse ****************************

// ********** Begin Class ADragonPrismalManager Function DrawDebugFlightPath ***********************
struct Z_Construct_UFunction_ADragonPrismalManager_DrawDebugFlightPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_DrawDebugFlightPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "DrawDebugFlightPath", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_DrawDebugFlightPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_DrawDebugFlightPath_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_DrawDebugFlightPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_DrawDebugFlightPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execDrawDebugFlightPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugFlightPath();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function DrawDebugFlightPath *************************

// ********** Begin Class ADragonPrismalManager Function DrawDebugLandingZones *********************
struct Z_Construct_UFunction_ADragonPrismalManager_DrawDebugLandingZones_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_DrawDebugLandingZones_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "DrawDebugLandingZones", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_DrawDebugLandingZones_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_DrawDebugLandingZones_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_DrawDebugLandingZones()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_DrawDebugLandingZones_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execDrawDebugLandingZones)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugLandingZones();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function DrawDebugLandingZones ***********************

// ********** Begin Class ADragonPrismalManager Function EndCombat *********************************
struct Z_Construct_UFunction_ADragonPrismalManager_EndCombat_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_EndCombat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "EndCombat", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_EndCombat_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_EndCombat_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_EndCombat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_EndCombat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execEndCombat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EndCombat();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function EndCombat ***********************************

// ********** Begin Class ADragonPrismalManager Function ExecuteAreaAttack *************************
struct Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics
{
	struct DragonPrismalManager_eventExecuteAreaAttack_Parms
	{
		FDragonAttack Attack;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Attack_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Attack;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::NewProp_Attack = { "Attack", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventExecuteAreaAttack_Parms, Attack), Z_Construct_UScriptStruct_FDragonAttack, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Attack_MetaData), NewProp_Attack_MetaData) }; // 4206001970
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::NewProp_Attack,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "ExecuteAreaAttack", Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::DragonPrismalManager_eventExecuteAreaAttack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::DragonPrismalManager_eventExecuteAreaAttack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execExecuteAreaAttack)
{
	P_GET_STRUCT_REF(FDragonAttack,Z_Param_Out_Attack);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteAreaAttack(Z_Param_Out_Attack);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function ExecuteAreaAttack ***************************

// ********** Begin Class ADragonPrismalManager Function ExecuteBreathAttack ***********************
struct Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics
{
	struct DragonPrismalManager_eventExecuteBreathAttack_Parms
	{
		FDragonAttack Attack;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Attack_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Attack;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::NewProp_Attack = { "Attack", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventExecuteBreathAttack_Parms, Attack), Z_Construct_UScriptStruct_FDragonAttack, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Attack_MetaData), NewProp_Attack_MetaData) }; // 4206001970
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::NewProp_Attack,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "ExecuteBreathAttack", Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::DragonPrismalManager_eventExecuteBreathAttack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::DragonPrismalManager_eventExecuteBreathAttack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execExecuteBreathAttack)
{
	P_GET_STRUCT_REF(FDragonAttack,Z_Param_Out_Attack);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteBreathAttack(Z_Param_Out_Attack);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function ExecuteBreathAttack *************************

// ********** Begin Class ADragonPrismalManager Function GenerateEllipsePoints *********************
struct Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics
{
	struct DragonPrismalManager_eventGenerateEllipsePoints_Parms
	{
		int32 NumPoints;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
		{ "CPP_Default_NumPoints", "64" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::NewProp_NumPoints = { "NumPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGenerateEllipsePoints_Parms, NumPoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGenerateEllipsePoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::NewProp_NumPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GenerateEllipsePoints", Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::DragonPrismalManager_eventGenerateEllipsePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::DragonPrismalManager_eventGenerateEllipsePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGenerateEllipsePoints)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NumPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateEllipsePoints(Z_Param_NumPoints);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GenerateEllipsePoints ***********************

// ********** Begin Class ADragonPrismalManager Function GenerateFlightPath ************************
struct Z_Construct_UFunction_ADragonPrismalManager_GenerateFlightPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flight System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de voo\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de voo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GenerateFlightPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GenerateFlightPath", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GenerateFlightPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GenerateFlightPath_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GenerateFlightPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GenerateFlightPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGenerateFlightPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateFlightPath();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GenerateFlightPath **************************

// ********** Begin Class ADragonPrismalManager Function GetClosestPointOnEllipse ******************
struct Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics
{
	struct DragonPrismalManager_eventGetClosestPointOnEllipse_Parms
	{
		FVector FromPosition;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FromPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FromPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::NewProp_FromPosition = { "FromPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGetClosestPointOnEllipse_Parms, FromPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FromPosition_MetaData), NewProp_FromPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGetClosestPointOnEllipse_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::NewProp_FromPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GetClosestPointOnEllipse", Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::DragonPrismalManager_eventGetClosestPointOnEllipse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::DragonPrismalManager_eventGetClosestPointOnEllipse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGetClosestPointOnEllipse)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_FromPosition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetClosestPointOnEllipse(Z_Param_Out_FromPosition);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GetClosestPointOnEllipse ********************

// ********** Begin Class ADragonPrismalManager Function GetDragonState ****************************
struct Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics
{
	struct DragonPrismalManager_eventGetDragonState_Parms
	{
		EDragonState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGetDragonState_Parms, ReturnValue), Z_Construct_UEnum_Aura_EDragonState, METADATA_PARAMS(0, nullptr) }; // 3515719245
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GetDragonState", Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::DragonPrismalManager_eventGetDragonState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::DragonPrismalManager_eventGetDragonState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GetDragonState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GetDragonState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGetDragonState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EDragonState*)Z_Param__Result=P_THIS->GetDragonState();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GetDragonState ******************************

// ********** Begin Class ADragonPrismalManager Function GetEligibleParticipants *******************
struct Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics
{
	struct DragonPrismalManager_eventGetEligibleParticipants_Parms
	{
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGetEligibleParticipants_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GetEligibleParticipants", Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::DragonPrismalManager_eventGetEligibleParticipants_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::DragonPrismalManager_eventGetEligibleParticipants_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGetEligibleParticipants)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetEligibleParticipants();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GetEligibleParticipants *********************

// ********** Begin Class ADragonPrismalManager Function GetNextWaypoint ***************************
struct Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics
{
	struct DragonPrismalManager_eventGetNextWaypoint_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flight System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGetNextWaypoint_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GetNextWaypoint", Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::DragonPrismalManager_eventGetNextWaypoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::DragonPrismalManager_eventGetNextWaypoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGetNextWaypoint)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetNextWaypoint();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GetNextWaypoint *****************************

// ********** Begin Class ADragonPrismalManager Function GetTimeUntilRespawn ***********************
struct Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics
{
	struct DragonPrismalManager_eventGetTimeUntilRespawn_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGetTimeUntilRespawn_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GetTimeUntilRespawn", Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::DragonPrismalManager_eventGetTimeUntilRespawn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::DragonPrismalManager_eventGetTimeUntilRespawn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGetTimeUntilRespawn)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeUntilRespawn();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GetTimeUntilRespawn *************************

// ********** Begin Class ADragonPrismalManager Function GetTimeUntilSpawn *************************
struct Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics
{
	struct DragonPrismalManager_eventGetTimeUntilSpawn_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGetTimeUntilSpawn_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GetTimeUntilSpawn", Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::DragonPrismalManager_eventGetTimeUntilSpawn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::DragonPrismalManager_eventGetTimeUntilSpawn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGetTimeUntilSpawn)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTimeUntilSpawn();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GetTimeUntilSpawn ***************************

// ********** Begin Class ADragonPrismalManager Function GiveRewardToParticipant *******************
struct Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics
{
	struct DragonPrismalManager_eventGiveRewardToParticipant_Parms
	{
		AActor* Participant;
		float GoldAmount;
		float ExperienceAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Participant;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GoldAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExperienceAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::NewProp_Participant = { "Participant", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGiveRewardToParticipant_Parms, Participant), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::NewProp_GoldAmount = { "GoldAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGiveRewardToParticipant_Parms, GoldAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::NewProp_ExperienceAmount = { "ExperienceAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventGiveRewardToParticipant_Parms, ExperienceAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::NewProp_Participant,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::NewProp_GoldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::NewProp_ExperienceAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "GiveRewardToParticipant", Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::DragonPrismalManager_eventGiveRewardToParticipant_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::DragonPrismalManager_eventGiveRewardToParticipant_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execGiveRewardToParticipant)
{
	P_GET_OBJECT(AActor,Z_Param_Participant);
	P_GET_PROPERTY(FFloatProperty,Z_Param_GoldAmount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExperienceAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GiveRewardToParticipant(Z_Param_Participant,Z_Param_GoldAmount,Z_Param_ExperienceAmount);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function GiveRewardToParticipant *********************

// ********** Begin Class ADragonPrismalManager Function IsPositionInEllipse ***********************
struct Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics
{
	struct DragonPrismalManager_eventIsPositionInEllipse_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Elliptical Geometry" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventIsPositionInEllipse_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DragonPrismalManager_eventIsPositionInEllipse_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragonPrismalManager_eventIsPositionInEllipse_Parms), &Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "IsPositionInEllipse", Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::DragonPrismalManager_eventIsPositionInEllipse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::DragonPrismalManager_eventIsPositionInEllipse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execIsPositionInEllipse)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInEllipse(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function IsPositionInEllipse *************************

// ********** Begin Class ADragonPrismalManager Function LandDragon ********************************
struct Z_Construct_UFunction_ADragonPrismalManager_LandDragon_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Management" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_LandDragon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "LandDragon", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_LandDragon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_LandDragon_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_LandDragon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_LandDragon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execLandDragon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LandDragon();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function LandDragon **********************************

// ********** Begin Class ADragonPrismalManager Function OnDragonDeath *****************************
struct Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics
{
	struct DragonPrismalManager_eventOnDragonDeath_Parms
	{
		AActor* Killer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventOnDragonDeath_Parms, Killer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::NewProp_Killer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "OnDragonDeath", Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::DragonPrismalManager_eventOnDragonDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::DragonPrismalManager_eventOnDragonDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execOnDragonDeath)
{
	P_GET_OBJECT(AActor,Z_Param_Killer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnDragonDeath(Z_Param_Killer);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function OnDragonDeath *******************************

// ********** Begin Class ADragonPrismalManager Function PerformAttack *****************************
struct Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics
{
	struct DragonPrismalManager_eventPerformAttack_Parms
	{
		int32 AttackIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_AttackIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::NewProp_AttackIndex = { "AttackIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventPerformAttack_Parms, AttackIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::NewProp_AttackIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "PerformAttack", Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::DragonPrismalManager_eventPerformAttack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::DragonPrismalManager_eventPerformAttack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_PerformAttack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_PerformAttack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execPerformAttack)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_AttackIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformAttack(Z_Param_AttackIndex);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function PerformAttack *******************************

// ********** Begin Class ADragonPrismalManager Function SetDragonElement **************************
struct Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics
{
	struct DragonPrismalManager_eventSetDragonElement_Parms
	{
		EDragonElement NewElement;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Type" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewElement_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewElement;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::NewProp_NewElement_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::NewProp_NewElement = { "NewElement", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventSetDragonElement_Parms, NewElement), Z_Construct_UEnum_Aura_EDragonElement, METADATA_PARAMS(0, nullptr) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::NewProp_NewElement_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::NewProp_NewElement,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "SetDragonElement", Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::DragonPrismalManager_eventSetDragonElement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::DragonPrismalManager_eventSetDragonElement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execSetDragonElement)
{
	P_GET_ENUM(EDragonElement,Z_Param_NewElement);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDragonElement(EDragonElement(Z_Param_NewElement));
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function SetDragonElement ****************************

// ********** Begin Class ADragonPrismalManager Function SetDragonState ****************************
struct Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics
{
	struct DragonPrismalManager_eventSetDragonState_Parms
	{
		EDragonState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventSetDragonState_Parms, NewState), Z_Construct_UEnum_Aura_EDragonState, METADATA_PARAMS(0, nullptr) }; // 3515719245
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "SetDragonState", Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::DragonPrismalManager_eventSetDragonState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::DragonPrismalManager_eventSetDragonState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_SetDragonState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_SetDragonState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execSetDragonState)
{
	P_GET_ENUM(EDragonState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDragonState(EDragonState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function SetDragonState ******************************

// ********** Begin Class ADragonPrismalManager Function SetDragonType *****************************
struct Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics
{
	struct DragonPrismalManager_eventSetDragonType_Parms
	{
		EDragonType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Type" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de elementos e tipos\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de elementos e tipos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventSetDragonType_Parms, NewType), Z_Construct_UEnum_Aura_EDragonType, METADATA_PARAMS(0, nullptr) }; // 2165795084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "SetDragonType", Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::DragonPrismalManager_eventSetDragonType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::DragonPrismalManager_eventSetDragonType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_SetDragonType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_SetDragonType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execSetDragonType)
{
	P_GET_ENUM(EDragonType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDragonType(EDragonType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function SetDragonType *******************************

// ********** Begin Class ADragonPrismalManager Function ShouldSpawnDragon *************************
struct Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics
{
	struct DragonPrismalManager_eventShouldSpawnDragon_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de timing e estado\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de timing e estado" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DragonPrismalManager_eventShouldSpawnDragon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragonPrismalManager_eventShouldSpawnDragon_Parms), &Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "ShouldSpawnDragon", Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::DragonPrismalManager_eventShouldSpawnDragon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::DragonPrismalManager_eventShouldSpawnDragon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execShouldSpawnDragon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldSpawnDragon();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function ShouldSpawnDragon ***************************

// ********** Begin Class ADragonPrismalManager Function SpawnDragon *******************************
struct Z_Construct_UFunction_ADragonPrismalManager_SpawnDragon_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de spawn e controle do Drag\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de spawn e controle do Drag\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_SpawnDragon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "SpawnDragon", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_SpawnDragon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_SpawnDragon_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_SpawnDragon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_SpawnDragon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execSpawnDragon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnDragon();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function SpawnDragon *********************************

// ********** Begin Class ADragonPrismalManager Function StartCombat *******************************
struct Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics
{
	struct DragonPrismalManager_eventStartCombat_Parms
	{
		AActor* Attacker;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de combate\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de combate" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Attacker;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::NewProp_Attacker = { "Attacker", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventStartCombat_Parms, Attacker), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::NewProp_Attacker,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "StartCombat", Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::DragonPrismalManager_eventStartCombat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::DragonPrismalManager_eventStartCombat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_StartCombat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_StartCombat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execStartCombat)
{
	P_GET_OBJECT(AActor,Z_Param_Attacker);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartCombat(Z_Param_Attacker);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function StartCombat *********************************

// ********** Begin Class ADragonPrismalManager Function StartDragonFlight *************************
struct Z_Construct_UFunction_ADragonPrismalManager_StartDragonFlight_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Management" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_StartDragonFlight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "StartDragonFlight", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_StartDragonFlight_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_StartDragonFlight_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_StartDragonFlight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_StartDragonFlight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execStartDragonFlight)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartDragonFlight();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function StartDragonFlight ***************************

// ********** Begin Class ADragonPrismalManager Function TakeDamage ********************************
struct Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics
{
	struct DragonPrismalManager_eventTakeDamage_Parms
	{
		float DamageAmount;
		FDamageEvent DamageEvent;
		AController* EventInstigator;
		AActor* DamageCauser;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageEvent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageEvent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EventInstigator;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageCauser;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventTakeDamage_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_DamageEvent = { "DamageEvent", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventTakeDamage_Parms, DamageEvent), Z_Construct_UScriptStruct_FDamageEvent, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageEvent_MetaData), NewProp_DamageEvent_MetaData) }; // 2615510189
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_EventInstigator = { "EventInstigator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventTakeDamage_Parms, EventInstigator), Z_Construct_UClass_AController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_DamageCauser = { "DamageCauser", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventTakeDamage_Parms, DamageCauser), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventTakeDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_DamageEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_EventInstigator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_DamageCauser,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "TakeDamage", Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::DragonPrismalManager_eventTakeDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::DragonPrismalManager_eventTakeDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_TakeDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_TakeDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execTakeDamage)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_STRUCT_REF(FDamageEvent,Z_Param_Out_DamageEvent);
	P_GET_OBJECT(AController,Z_Param_EventInstigator);
	P_GET_OBJECT(AActor,Z_Param_DamageCauser);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->TakeDamage(Z_Param_DamageAmount,Z_Param_Out_DamageEvent,Z_Param_EventInstigator,Z_Param_DamageCauser);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function TakeDamage **********************************

// ********** Begin Class ADragonPrismalManager Function UpdateAttacksForElement *******************
struct Z_Construct_UFunction_ADragonPrismalManager_UpdateAttacksForElement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Type" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_UpdateAttacksForElement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "UpdateAttacksForElement", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_UpdateAttacksForElement_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_UpdateAttacksForElement_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_UpdateAttacksForElement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_UpdateAttacksForElement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execUpdateAttacksForElement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAttacksForElement();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function UpdateAttacksForElement *********************

// ********** Begin Class ADragonPrismalManager Function UpdateDragonHealth ************************
struct Z_Construct_UFunction_ADragonPrismalManager_UpdateDragonHealth_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dragon Management" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_UpdateDragonHealth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "UpdateDragonHealth", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_UpdateDragonHealth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_UpdateDragonHealth_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ADragonPrismalManager_UpdateDragonHealth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_UpdateDragonHealth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execUpdateDragonHealth)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDragonHealth();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function UpdateDragonHealth **************************

// ********** Begin Class ADragonPrismalManager Function UpdateFlightMovement **********************
struct Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics
{
	struct DragonPrismalManager_eventUpdateFlightMovement_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flight System" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragonPrismalManager_eventUpdateFlightMovement_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "UpdateFlightMovement", Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::DragonPrismalManager_eventUpdateFlightMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::DragonPrismalManager_eventUpdateFlightMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execUpdateFlightMovement)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFlightMovement(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function UpdateFlightMovement ************************

// ********** Begin Class ADragonPrismalManager Function ValidateEllipticalGeometry ****************
struct Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics
{
	struct DragonPrismalManager_eventValidateEllipticalGeometry_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DragonPrismalManager_eventValidateEllipticalGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragonPrismalManager_eventValidateEllipticalGeometry_Parms), &Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ADragonPrismalManager, nullptr, "ValidateEllipticalGeometry", Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::DragonPrismalManager_eventValidateEllipticalGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::DragonPrismalManager_eventValidateEllipticalGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ADragonPrismalManager::execValidateEllipticalGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateEllipticalGeometry();
	P_NATIVE_END;
}
// ********** End Class ADragonPrismalManager Function ValidateEllipticalGeometry ******************

// ********** Begin Class ADragonPrismalManager ****************************************************
void ADragonPrismalManager::StaticRegisterNativesADragonPrismalManager()
{
	UClass* Class = ADragonPrismalManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AdvanceToNextWaypoint", &ADragonPrismalManager::execAdvanceToNextWaypoint },
		{ "CalculateAndDistributeRewards", &ADragonPrismalManager::execCalculateAndDistributeRewards },
		{ "CalculateEccentricity", &ADragonPrismalManager::execCalculateEccentricity },
		{ "CalculateEllipticalArea", &ADragonPrismalManager::execCalculateEllipticalArea },
		{ "CalculateEllipticalGeometry", &ADragonPrismalManager::execCalculateEllipticalGeometry },
		{ "CalculateEllipticalPerimeter", &ADragonPrismalManager::execCalculateEllipticalPerimeter },
		{ "CalculateFlightPathLength", &ADragonPrismalManager::execCalculateFlightPathLength },
		{ "CalculateLandingZones", &ADragonPrismalManager::execCalculateLandingZones },
		{ "CalculateParticipationPercentage", &ADragonPrismalManager::execCalculateParticipationPercentage },
		{ "CalculateSoloBonus", &ADragonPrismalManager::execCalculateSoloBonus },
		{ "CalculateTeamBonus", &ADragonPrismalManager::execCalculateTeamBonus },
		{ "DespawnDragon", &ADragonPrismalManager::execDespawnDragon },
		{ "DrawDebugEllipse", &ADragonPrismalManager::execDrawDebugEllipse },
		{ "DrawDebugFlightPath", &ADragonPrismalManager::execDrawDebugFlightPath },
		{ "DrawDebugLandingZones", &ADragonPrismalManager::execDrawDebugLandingZones },
		{ "EndCombat", &ADragonPrismalManager::execEndCombat },
		{ "ExecuteAreaAttack", &ADragonPrismalManager::execExecuteAreaAttack },
		{ "ExecuteBreathAttack", &ADragonPrismalManager::execExecuteBreathAttack },
		{ "GenerateEllipsePoints", &ADragonPrismalManager::execGenerateEllipsePoints },
		{ "GenerateFlightPath", &ADragonPrismalManager::execGenerateFlightPath },
		{ "GetClosestPointOnEllipse", &ADragonPrismalManager::execGetClosestPointOnEllipse },
		{ "GetDragonState", &ADragonPrismalManager::execGetDragonState },
		{ "GetEligibleParticipants", &ADragonPrismalManager::execGetEligibleParticipants },
		{ "GetNextWaypoint", &ADragonPrismalManager::execGetNextWaypoint },
		{ "GetTimeUntilRespawn", &ADragonPrismalManager::execGetTimeUntilRespawn },
		{ "GetTimeUntilSpawn", &ADragonPrismalManager::execGetTimeUntilSpawn },
		{ "GiveRewardToParticipant", &ADragonPrismalManager::execGiveRewardToParticipant },
		{ "IsPositionInEllipse", &ADragonPrismalManager::execIsPositionInEllipse },
		{ "LandDragon", &ADragonPrismalManager::execLandDragon },
		{ "OnDragonDeath", &ADragonPrismalManager::execOnDragonDeath },
		{ "PerformAttack", &ADragonPrismalManager::execPerformAttack },
		{ "SetDragonElement", &ADragonPrismalManager::execSetDragonElement },
		{ "SetDragonState", &ADragonPrismalManager::execSetDragonState },
		{ "SetDragonType", &ADragonPrismalManager::execSetDragonType },
		{ "ShouldSpawnDragon", &ADragonPrismalManager::execShouldSpawnDragon },
		{ "SpawnDragon", &ADragonPrismalManager::execSpawnDragon },
		{ "StartCombat", &ADragonPrismalManager::execStartCombat },
		{ "StartDragonFlight", &ADragonPrismalManager::execStartDragonFlight },
		{ "TakeDamage", &ADragonPrismalManager::execTakeDamage },
		{ "UpdateAttacksForElement", &ADragonPrismalManager::execUpdateAttacksForElement },
		{ "UpdateDragonHealth", &ADragonPrismalManager::execUpdateDragonHealth },
		{ "UpdateFlightMovement", &ADragonPrismalManager::execUpdateFlightMovement },
		{ "ValidateEllipticalGeometry", &ADragonPrismalManager::execValidateEllipticalGeometry },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ADragonPrismalManager;
UClass* ADragonPrismalManager::GetPrivateStaticClass()
{
	using TClass = ADragonPrismalManager;
	if (!Z_Registration_Info_UClass_ADragonPrismalManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("DragonPrismalManager"),
			Z_Registration_Info_UClass_ADragonPrismalManager.InnerSingleton,
			StaticRegisterNativesADragonPrismalManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ADragonPrismalManager.InnerSingleton;
}
UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister()
{
	return ADragonPrismalManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ADragonPrismalManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "ADragonPrismalManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes principais\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonMesh_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatArea_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionArea_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardArea_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoproPrismalParticles_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes de efeitos visuais e sonoros\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de efeitos visuais e sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FuriaCrescenteParticles_MetaData[] = {
		{ "Category", "Effects" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonAudioComponent_MetaData[] = {
		{ "Category", "Effects" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatAudioComponent_MetaData[] = {
		{ "Category", "Effects" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonData_MetaData[] = {
		{ "Category", "Dragon Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados do Drag\xc3\xa3o Prismal\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados do Drag\xc3\xa3o Prismal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EllipticalCovil_MetaData[] = {
		{ "Category", "Elliptical Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Geometria el\xc3\xadptica do covil\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Geometria el\xc3\xadptica do covil" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardSystem_MetaData[] = {
		{ "Category", "Reward System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de recompensas\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de recompensas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoproPrismalEffect_MetaData[] = {
		{ "Category", "Effects Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Assets de efeitos visuais e sonoros\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Assets de efeitos visuais e sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FuriaCrescenteEffect_MetaData[] = {
		{ "Category", "Effects Assets" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrystalShatterEffect_MetaData[] = {
		{ "Category", "Effects Assets" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoproPrismalSound_MetaData[] = {
		{ "Category", "Effects Assets" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FuriaCrescenteSound_MetaData[] = {
		{ "Category", "Effects Assets" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonRoarSound_MetaData[] = {
		{ "Category", "Effects Assets" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrystalBreakSound_MetaData[] = {
		{ "Category", "Effects Assets" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonAttacks_MetaData[] = {
		{ "Category", "Combat System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de ataques\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de ataques" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlightPath_MetaData[] = {
		{ "Category", "Flight System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de voo\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de voo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameStartTime_MetaData[] = {
		{ "Category", "Timing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de timing\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de timing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentGameTime_MetaData[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatParticipants_MetaData[] = {
		{ "Category", "Combat Tracking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Participantes do combate\n" },
#endif
		{ "ModuleRelativePath", "Public/ADragonPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Participantes do combate" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragonMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CombatArea;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DetectionArea;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RewardArea;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SoproPrismalParticles;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FuriaCrescenteParticles;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragonAudioComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CombatAudioComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DragonData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EllipticalCovil;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RewardSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SoproPrismalEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FuriaCrescenteEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CrystalShatterEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SoproPrismalSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FuriaCrescenteSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragonRoarSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CrystalBreakSound;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DragonAttacks_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DragonAttacks;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlightPath;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GameStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentGameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CombatParticipants_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CombatParticipants_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CombatParticipants;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ADragonPrismalManager_AdvanceToNextWaypoint, "AdvanceToNextWaypoint" }, // 606269234
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateAndDistributeRewards, "CalculateAndDistributeRewards" }, // 1126486308
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateEccentricity, "CalculateEccentricity" }, // 3999556844
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalArea, "CalculateEllipticalArea" }, // 2461068507
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalGeometry, "CalculateEllipticalGeometry" }, // 3729811017
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateEllipticalPerimeter, "CalculateEllipticalPerimeter" }, // 2455300464
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateFlightPathLength, "CalculateFlightPathLength" }, // 1117771884
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateLandingZones, "CalculateLandingZones" }, // 2536436113
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateParticipationPercentage, "CalculateParticipationPercentage" }, // 339395519
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateSoloBonus, "CalculateSoloBonus" }, // 210658509
		{ &Z_Construct_UFunction_ADragonPrismalManager_CalculateTeamBonus, "CalculateTeamBonus" }, // 829206127
		{ &Z_Construct_UFunction_ADragonPrismalManager_DespawnDragon, "DespawnDragon" }, // 1549767276
		{ &Z_Construct_UFunction_ADragonPrismalManager_DrawDebugEllipse, "DrawDebugEllipse" }, // 2237470922
		{ &Z_Construct_UFunction_ADragonPrismalManager_DrawDebugFlightPath, "DrawDebugFlightPath" }, // 3059978589
		{ &Z_Construct_UFunction_ADragonPrismalManager_DrawDebugLandingZones, "DrawDebugLandingZones" }, // 644133185
		{ &Z_Construct_UFunction_ADragonPrismalManager_EndCombat, "EndCombat" }, // 2164076983
		{ &Z_Construct_UFunction_ADragonPrismalManager_ExecuteAreaAttack, "ExecuteAreaAttack" }, // 2565908164
		{ &Z_Construct_UFunction_ADragonPrismalManager_ExecuteBreathAttack, "ExecuteBreathAttack" }, // 4196196147
		{ &Z_Construct_UFunction_ADragonPrismalManager_GenerateEllipsePoints, "GenerateEllipsePoints" }, // 3588344134
		{ &Z_Construct_UFunction_ADragonPrismalManager_GenerateFlightPath, "GenerateFlightPath" }, // 2038588174
		{ &Z_Construct_UFunction_ADragonPrismalManager_GetClosestPointOnEllipse, "GetClosestPointOnEllipse" }, // 2076897470
		{ &Z_Construct_UFunction_ADragonPrismalManager_GetDragonState, "GetDragonState" }, // 357770379
		{ &Z_Construct_UFunction_ADragonPrismalManager_GetEligibleParticipants, "GetEligibleParticipants" }, // 3441774790
		{ &Z_Construct_UFunction_ADragonPrismalManager_GetNextWaypoint, "GetNextWaypoint" }, // 2488373055
		{ &Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilRespawn, "GetTimeUntilRespawn" }, // 3866915854
		{ &Z_Construct_UFunction_ADragonPrismalManager_GetTimeUntilSpawn, "GetTimeUntilSpawn" }, // 2552264096
		{ &Z_Construct_UFunction_ADragonPrismalManager_GiveRewardToParticipant, "GiveRewardToParticipant" }, // 3692001660
		{ &Z_Construct_UFunction_ADragonPrismalManager_IsPositionInEllipse, "IsPositionInEllipse" }, // 1647789026
		{ &Z_Construct_UFunction_ADragonPrismalManager_LandDragon, "LandDragon" }, // 2855851544
		{ &Z_Construct_UFunction_ADragonPrismalManager_OnDragonDeath, "OnDragonDeath" }, // 3711209812
		{ &Z_Construct_UFunction_ADragonPrismalManager_PerformAttack, "PerformAttack" }, // 879827346
		{ &Z_Construct_UFunction_ADragonPrismalManager_SetDragonElement, "SetDragonElement" }, // 2861062897
		{ &Z_Construct_UFunction_ADragonPrismalManager_SetDragonState, "SetDragonState" }, // 2173153241
		{ &Z_Construct_UFunction_ADragonPrismalManager_SetDragonType, "SetDragonType" }, // 1898951800
		{ &Z_Construct_UFunction_ADragonPrismalManager_ShouldSpawnDragon, "ShouldSpawnDragon" }, // 3129021890
		{ &Z_Construct_UFunction_ADragonPrismalManager_SpawnDragon, "SpawnDragon" }, // **********
		{ &Z_Construct_UFunction_ADragonPrismalManager_StartCombat, "StartCombat" }, // 761509056
		{ &Z_Construct_UFunction_ADragonPrismalManager_StartDragonFlight, "StartDragonFlight" }, // **********
		{ &Z_Construct_UFunction_ADragonPrismalManager_TakeDamage, "TakeDamage" }, // **********
		{ &Z_Construct_UFunction_ADragonPrismalManager_UpdateAttacksForElement, "UpdateAttacksForElement" }, // **********
		{ &Z_Construct_UFunction_ADragonPrismalManager_UpdateDragonHealth, "UpdateDragonHealth" }, // 333259158
		{ &Z_Construct_UFunction_ADragonPrismalManager_UpdateFlightMovement, "UpdateFlightMovement" }, // **********
		{ &Z_Construct_UFunction_ADragonPrismalManager_ValidateEllipticalGeometry, "ValidateEllipticalGeometry" }, // 348206517
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ADragonPrismalManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonMesh = { "DragonMesh", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, DragonMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonMesh_MetaData), NewProp_DragonMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatArea = { "CombatArea", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, CombatArea), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatArea_MetaData), NewProp_CombatArea_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DetectionArea = { "DetectionArea", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, DetectionArea), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionArea_MetaData), NewProp_DetectionArea_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_RewardArea = { "RewardArea", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, RewardArea), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardArea_MetaData), NewProp_RewardArea_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_SoproPrismalParticles = { "SoproPrismalParticles", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, SoproPrismalParticles), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoproPrismalParticles_MetaData), NewProp_SoproPrismalParticles_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FuriaCrescenteParticles = { "FuriaCrescenteParticles", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, FuriaCrescenteParticles), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FuriaCrescenteParticles_MetaData), NewProp_FuriaCrescenteParticles_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonAudioComponent = { "DragonAudioComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, DragonAudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonAudioComponent_MetaData), NewProp_DragonAudioComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatAudioComponent = { "CombatAudioComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, CombatAudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatAudioComponent_MetaData), NewProp_CombatAudioComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonData = { "DragonData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, DragonData), Z_Construct_UScriptStruct_FDragonData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonData_MetaData), NewProp_DragonData_MetaData) }; // 305536386
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_EllipticalCovil = { "EllipticalCovil", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, EllipticalCovil), Z_Construct_UScriptStruct_FEllipticalArea, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EllipticalCovil_MetaData), NewProp_EllipticalCovil_MetaData) }; // 2748584346
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_RewardSystem = { "RewardSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, RewardSystem), Z_Construct_UScriptStruct_FDragonReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardSystem_MetaData), NewProp_RewardSystem_MetaData) }; // 1014814633
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_SoproPrismalEffect = { "SoproPrismalEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, SoproPrismalEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoproPrismalEffect_MetaData), NewProp_SoproPrismalEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FuriaCrescenteEffect = { "FuriaCrescenteEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, FuriaCrescenteEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FuriaCrescenteEffect_MetaData), NewProp_FuriaCrescenteEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CrystalShatterEffect = { "CrystalShatterEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, CrystalShatterEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrystalShatterEffect_MetaData), NewProp_CrystalShatterEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_SoproPrismalSound = { "SoproPrismalSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, SoproPrismalSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoproPrismalSound_MetaData), NewProp_SoproPrismalSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FuriaCrescenteSound = { "FuriaCrescenteSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, FuriaCrescenteSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FuriaCrescenteSound_MetaData), NewProp_FuriaCrescenteSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonRoarSound = { "DragonRoarSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, DragonRoarSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonRoarSound_MetaData), NewProp_DragonRoarSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CrystalBreakSound = { "CrystalBreakSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, CrystalBreakSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrystalBreakSound_MetaData), NewProp_CrystalBreakSound_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonAttacks_Inner = { "DragonAttacks", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FDragonAttack, METADATA_PARAMS(0, nullptr) }; // 4206001970
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonAttacks = { "DragonAttacks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, DragonAttacks), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonAttacks_MetaData), NewProp_DragonAttacks_MetaData) }; // 4206001970
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FlightPath = { "FlightPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, FlightPath), Z_Construct_UScriptStruct_FFlightPath, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlightPath_MetaData), NewProp_FlightPath_MetaData) }; // 1473748430
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_GameStartTime = { "GameStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, GameStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameStartTime_MetaData), NewProp_GameStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CurrentGameTime = { "CurrentGameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, CurrentGameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentGameTime_MetaData), NewProp_CurrentGameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatParticipants_ValueProp = { "CombatParticipants", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatParticipants_Key_KeyProp = { "CombatParticipants_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatParticipants = { "CombatParticipants", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ADragonPrismalManager, CombatParticipants), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatParticipants_MetaData), NewProp_CombatParticipants_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ADragonPrismalManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DetectionArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_RewardArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_SoproPrismalParticles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FuriaCrescenteParticles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonAudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatAudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_EllipticalCovil,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_RewardSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_SoproPrismalEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FuriaCrescenteEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CrystalShatterEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_SoproPrismalSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FuriaCrescenteSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonRoarSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CrystalBreakSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonAttacks_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_DragonAttacks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_FlightPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_GameStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CurrentGameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatParticipants_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatParticipants_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ADragonPrismalManager_Statics::NewProp_CombatParticipants,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ADragonPrismalManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ADragonPrismalManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ADragonPrismalManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ADragonPrismalManager_Statics::ClassParams = {
	&ADragonPrismalManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ADragonPrismalManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ADragonPrismalManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ADragonPrismalManager_Statics::Class_MetaDataParams), Z_Construct_UClass_ADragonPrismalManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ADragonPrismalManager()
{
	if (!Z_Registration_Info_UClass_ADragonPrismalManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ADragonPrismalManager.OuterSingleton, Z_Construct_UClass_ADragonPrismalManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ADragonPrismalManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ADragonPrismalManager);
ADragonPrismalManager::~ADragonPrismalManager() {}
// ********** End Class ADragonPrismalManager ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EDragonState_StaticEnum, TEXT("EDragonState"), &Z_Registration_Info_UEnum_EDragonState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3515719245U) },
		{ EDragonType_StaticEnum, TEXT("EDragonType"), &Z_Registration_Info_UEnum_EDragonType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2165795084U) },
		{ EDragonElement_StaticEnum, TEXT("EDragonElement"), &Z_Registration_Info_UEnum_EDragonElement, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, **********U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FDragonData::StaticStruct, Z_Construct_UScriptStruct_FDragonData_Statics::NewStructOps, TEXT("DragonData"), &Z_Registration_Info_UScriptStruct_FDragonData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDragonData), 305536386U) },
		{ FEllipticalArea::StaticStruct, Z_Construct_UScriptStruct_FEllipticalArea_Statics::NewStructOps, TEXT("EllipticalArea"), &Z_Registration_Info_UScriptStruct_FEllipticalArea, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEllipticalArea), 2748584346U) },
		{ FDragonReward::StaticStruct, Z_Construct_UScriptStruct_FDragonReward_Statics::NewStructOps, TEXT("DragonReward"), &Z_Registration_Info_UScriptStruct_FDragonReward, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDragonReward), 1014814633U) },
		{ FDragonAttack::StaticStruct, Z_Construct_UScriptStruct_FDragonAttack_Statics::NewStructOps, TEXT("DragonAttack"), &Z_Registration_Info_UScriptStruct_FDragonAttack, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDragonAttack), 4206001970U) },
		{ FFlightPath::StaticStruct, Z_Construct_UScriptStruct_FFlightPath_Statics::NewStructOps, TEXT("FlightPath"), &Z_Registration_Info_UScriptStruct_FFlightPath, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FFlightPath), 1473748430U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ADragonPrismalManager, ADragonPrismalManager::StaticClass, TEXT("ADragonPrismalManager"), &Z_Registration_Info_UClass_ADragonPrismalManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ADragonPrismalManager), 626049748U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_1428386730(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ADragonPrismalManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
