#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Math/UnrealMathUtility.h"
#include "TimerManager.h"
#include "AMinionWaveManager.generated.h"

// Forward declarations
class ALaneManager;
class ABaronAuracronManager;
class ADragonPrismalManager;
class AWallCollisionManager;
class ARiverPrismalManager;

// Constantes para o sistema de minions
#define MAX_MINIONS_PER_WAVE 12        // Máximo de minions por onda
#define MINION_SPAWN_INTERVAL 30.0f    // 30 segundos entre ondas
#define MINION_SPEED 350.0f            // 3.5 m/s velocidade base
#define MINION_SIZE 100.0f             // 1 metro de tamanho
#define MINION_HEALTH 100.0f           // Vida base dos minions
#define MINION_DAMAGE 25.0f            // Dano base dos minions
#define PATHFINDING_GRID_SIZE 100.0f   // 1 metro por célula do grid
#define ASTAR_MAX_ITERATIONS 1000      // Máximo de iterações do A*
#define WAVE_SCALING_FACTOR 1.1f       // Fator de escalonamento por onda
#define MINION_COLLISION_RADIUS 50.0f  // Raio de colisão dos minions
#define FORMATION_SPACING 150.0f       // Espaçamento em formação

// Enums para o sistema de minions
UENUM(BlueprintType)
enum class EMinionType : uint8
{
    Melee        UMETA(DisplayName = "Melee"),
    Ranged       UMETA(DisplayName = "Ranged"),
    Caster       UMETA(DisplayName = "Caster"),
    Tank         UMETA(DisplayName = "Tank"),
    Support      UMETA(DisplayName = "Support"),
    Siege        UMETA(DisplayName = "Siege"),
    Super        UMETA(DisplayName = "Super")
};

UENUM(BlueprintType)
enum class EWaveType : uint8
{
    Normal       UMETA(DisplayName = "Normal"),
    Elite        UMETA(DisplayName = "Elite"),
    Boss         UMETA(DisplayName = "Boss"),
    Siege        UMETA(DisplayName = "Siege"),
    Cannon       UMETA(DisplayName = "Cannon"),
    Super        UMETA(DisplayName = "Super")
};

UENUM(BlueprintType)
enum class EMinionState : uint8
{
    Spawning     UMETA(DisplayName = "Spawning"),
    Moving       UMETA(DisplayName = "Moving"),
    Fighting     UMETA(DisplayName = "Fighting"),
    Retreating   UMETA(DisplayName = "Retreating"),
    Dead         UMETA(DisplayName = "Dead"),
    Waiting      UMETA(DisplayName = "Waiting")
};

UENUM(BlueprintType)
enum class ELaneType : uint8
{
    Top          UMETA(DisplayName = "Top"),
    Middle       UMETA(DisplayName = "Middle"),
    Bottom       UMETA(DisplayName = "Bottom")
};

UENUM(BlueprintType)
enum class EFormationType : uint8
{
    Line         UMETA(DisplayName = "Line"),
    Column       UMETA(DisplayName = "Column"),
    Wedge        UMETA(DisplayName = "Wedge"),
    Box          UMETA(DisplayName = "Box"),
    Scattered    UMETA(DisplayName = "Scattered")
};

UENUM(BlueprintType)
enum class EPathfindingAlgorithm : uint8
{
    AStar        UMETA(DisplayName = "A*"),
    Dijkstra     UMETA(DisplayName = "Dijkstra"),
    BFS          UMETA(DisplayName = "Breadth-First Search"),
    DFS          UMETA(DisplayName = "Depth-First Search"),
    JPS          UMETA(DisplayName = "Jump Point Search")
};

// Estruturas de dados para minions
USTRUCT(BlueprintType)
struct FMinionData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    EMinionType Type;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    FVector Velocity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    FRotator Rotation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    EMinionState State;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    float Health;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    float MaxHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    float Damage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    float Speed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    float AttackRange;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    float AttackSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    ELaneType AssignedLane;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    TArray<FVector> PathWaypoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    int32 CurrentWaypointIndex;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    AActor* Target;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    float LastAttackTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    bool bIsElite;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    int32 WaveNumber;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Minion Data")
    int32 MinionID;

    FMinionData()
    {
        Type = EMinionType::Melee;
        Position = FVector::ZeroVector;
        Velocity = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        State = EMinionState::Spawning;
        Health = MINION_HEALTH;
        MaxHealth = MINION_HEALTH;
        Damage = MINION_DAMAGE;
        Speed = MINION_SPEED;
        AttackRange = 200.0f;
        AttackSpeed = 1.0f;
        AssignedLane = ELaneType::Middle;
        CurrentWaypointIndex = 0;
        Target = nullptr;
        LastAttackTime = 0.0f;
        bIsElite = false;
        WaveNumber = 1;
        MinionID = 0;
    }
};

USTRUCT(BlueprintType)
struct FWaveData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    int32 WaveNumber;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    EWaveType WaveType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    TArray<EMinionType> MinionComposition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    int32 TotalMinions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    float SpawnInterval;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    float WaveStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    float WaveEndTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    ELaneType PrimaryLane;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    EFormationType Formation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    float HealthMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    float DamageMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    float SpeedMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    bool bHasSuperMinion;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    bool bIsActive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Data")
    TArray<FVector> SpawnPositions;

    FWaveData()
    {
        WaveNumber = 1;
        WaveType = EWaveType::Normal;
        TotalMinions = 6;
        SpawnInterval = 1.0f;
        WaveStartTime = 0.0f;
        WaveEndTime = 0.0f;
        PrimaryLane = ELaneType::Middle;
        Formation = EFormationType::Line;
        HealthMultiplier = 1.0f;
        DamageMultiplier = 1.0f;
        SpeedMultiplier = 1.0f;
        bHasSuperMinion = false;
        bIsActive = false;
    }
};

USTRUCT(BlueprintType)
struct FPathfindingNode
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FVector2D GridCoordinate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float GCost; // Custo do início até este nó

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float HCost; // Heurística (custo estimado até o objetivo)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float FCost; // GCost + HCost

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    bool bIsWalkable;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    bool bIsInOpenSet;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    bool bIsInClosedSet;

    // Parent node index (-1 if no parent)
    int32 ParentIndex;

    // Neighbor node indices
    TArray<int32> NeighborIndices;

    // Ponteiros para nós vizinhos (para compatibilidade com código existente)
    TArray<FPathfindingNode*> Neighbors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float MovementCost;

    FPathfindingNode()
    {
        Position = FVector::ZeroVector;
        GridCoordinate = FVector2D::ZeroVector;
        GCost = 0.0f;
        HCost = 0.0f;
        FCost = 0.0f;
        bIsWalkable = true;
        bIsInOpenSet = false;
        bIsInClosedSet = false;
        ParentIndex = -1;
        MovementCost = 1.0f;
    }
};

USTRUCT(BlueprintType)
struct FPathfindingGrid
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    TArray<FPathfindingNode> Nodes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 GridWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 GridHeight;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    float CellSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    FVector GridOrigin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    FVector GridBounds;

    FPathfindingGrid()
    {
        GridWidth = 100;
        GridHeight = 100;
        CellSize = PATHFINDING_GRID_SIZE;
        GridOrigin = FVector::ZeroVector;
        GridBounds = FVector::ZeroVector;
    }
};

USTRUCT(BlueprintType)
struct FMinionFormation
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    EFormationType FormationType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    TArray<FVector> Positions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    FVector CenterPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    FRotator Orientation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    float Spacing;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    int32 MaxUnits;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    bool bMaintainFormation;

    FMinionFormation()
    {
        FormationType = EFormationType::Line;
        CenterPosition = FVector::ZeroVector;
        Orientation = FRotator::ZeroRotator;
        Spacing = FORMATION_SPACING;
        MaxUnits = MAX_MINIONS_PER_WAVE;
        bMaintainFormation = true;
    }
};

USTRUCT(BlueprintType)
struct FMinionSpawnPoint
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    FRotator Rotation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    ELaneType LaneType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    bool bIsActive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    float CooldownTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    float LastSpawnTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    int32 MaxConcurrentSpawns;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Point")
    int32 CurrentSpawnCount;

    FMinionSpawnPoint()
    {
        Position = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        LaneType = ELaneType::Middle;
        bIsActive = true;
        CooldownTime = 1.0f;
        LastSpawnTime = 0.0f;
        MaxConcurrentSpawns = 3;
        CurrentSpawnCount = 0;
    }
};

/**
 * Gerenciador do sistema de ondas de minions com pathfinding A* avançado
 * Controla spawn, movimento, combate e formações de minions
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API AMinionWaveManager : public AActor
{
    GENERATED_BODY()

public:
    AMinionWaveManager();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Componentes principais
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USceneComponent* RootSceneComponent;

    // Configurações do sistema de ondas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Settings")
    float WaveInterval;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Settings")
    int32 CurrentWaveNumber;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Settings")
    int32 MaxWaves;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Settings")
    bool bAutoStartWaves;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Settings")
    bool bInfiniteWaves;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave Settings")
    float WaveScalingFactor;

    // Configurações de pathfinding
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding Settings")
    EPathfindingAlgorithm PathfindingAlgorithm;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding Settings")
    float GridCellSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding Settings")
    int32 MaxPathfindingIterations;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding Settings")
    bool bUseDynamicPathfinding;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding Settings")
    bool bAvoidOtherMinions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding Settings")
    float PathUpdateInterval;

    // Arrays de dados
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Minion Data")
    TArray<FMinionData> ActiveMinions;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Wave Data")
    TArray<FWaveData> WaveQueue;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Wave Data")
    FWaveData CurrentWave;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Spawn Data")
    TArray<FMinionSpawnPoint> SpawnPoints;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Pathfinding Data")
    FPathfindingGrid NavigationGrid;

    // Referências para outros managers
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager References")
    ALaneManager* LaneManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager References")
    ABaronAuracronManager* BaronManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager References")
    ADragonPrismalManager* DragonManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager References")
    AWallCollisionManager* WallManager;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager References")
    ARiverPrismalManager* RiverManager;

    // Materiais e meshes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TMap<EMinionType, UStaticMesh*> MinionMeshes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    TMap<EMinionType, UMaterialInterface*> MinionMaterials;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    UStaticMesh* SpawnEffectMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Assets")
    UMaterialInterface* SpawnEffectMaterial;

    // Configurações de debug
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowPathfindingGrid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowMinionPaths;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowFormations;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowSpawnPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDetailedLogging;

public:
    // Funções principais do sistema de ondas
    UFUNCTION(BlueprintCallable, Category = "Wave System")
    void InitializeWaveSystem();

    UFUNCTION(BlueprintCallable, Category = "Wave System")
    void StartNextWave();

    UFUNCTION(BlueprintCallable, Category = "Wave System")
    void StopCurrentWave();

    UFUNCTION(BlueprintCallable, Category = "Wave System")
    void PauseWaveSystem();

    UFUNCTION(BlueprintCallable, Category = "Wave System")
    void ResumeWaveSystem();

    UFUNCTION(BlueprintCallable, Category = "Wave System")
    void ResetWaveSystem();

    // Funções de geração de ondas
    UFUNCTION(BlueprintCallable, Category = "Wave Generation")
    FWaveData GenerateWaveData(int32 WaveNumber, EWaveType WaveType);

    UFUNCTION(BlueprintCallable, Category = "Wave Generation")
    TArray<EMinionType> GenerateMinionComposition(int32 WaveNumber, EWaveType WaveType);

    UFUNCTION(BlueprintCallable, Category = "Wave Generation")
    void CalculateWaveScaling(int32 WaveNumber, float& HealthMultiplier, float& DamageMultiplier, float& SpeedMultiplier);

    UFUNCTION(BlueprintCallable, Category = "Wave Generation")
    void SetupWaveFormation(FWaveData& WaveData);

    // Funções de spawn de minions
    UFUNCTION(BlueprintCallable, Category = "Minion Spawning")
    int32 SpawnMinion(EMinionType Type, const FVector& Position, ELaneType Lane);

    UFUNCTION(BlueprintCallable, Category = "Minion Spawning")
    void SpawnWaveMinions(const FWaveData& WaveData);

    UFUNCTION(BlueprintCallable, Category = "Minion Spawning")
    void DestroyMinion(int32 MinionID);

    UFUNCTION(BlueprintCallable, Category = "Minion Spawning")
    void DestroyAllMinions();

    // Sistema de pathfinding A*
    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    void InitializePathfindingGrid();

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<FVector> FindPath(const FVector& StartPos, const FVector& EndPos, bool bAvoidUnits = true);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<FVector> FindPathAStar(const FVector& StartPos, const FVector& EndPos);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<FVector> FindPathDijkstra(const FVector& StartPos, const FVector& EndPos);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    void UpdateNavigationGrid();

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    bool IsPositionWalkable(const FVector& Position);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    FVector2D WorldToGridCoordinate(const FVector& WorldPosition);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    FVector GridToWorldPosition(const FVector2D& GridCoordinate);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    bool GetNodeAtGridPosition(int32 X, int32 Y, FPathfindingNode& OutNode);

    // Funções internas movidas para private devido ao uso de ponteiros

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<int32> GetNeighborNodeIndices(int32 NodeIndex);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    float CalculateHeuristic(const FVector& StartPos, const FVector& EndPos);

    // Função movida para private

    // Funções de movimento e comportamento
    UFUNCTION(BlueprintCallable, Category = "Minion Behavior")
    void UpdateMinionMovement(FMinionData& Minion, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Minion Behavior")
    void UpdateMinionBehavior(FMinionData& Minion, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Minion Behavior")
    void UpdateMinionCombat(FMinionData& Minion, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Minion Behavior")
    AActor* FindNearestTarget(const FMinionData& Minion);

    UFUNCTION(BlueprintCallable, Category = "Minion Behavior")
    bool CanMinionAttack(const FMinionData& Minion);

    UFUNCTION(BlueprintCallable, Category = "Minion Behavior")
    void ExecuteMinionAttack(FMinionData& Minion, AActor* Target);

    // Sistema de formações
    UFUNCTION(BlueprintCallable, Category = "Formation System")
    FMinionFormation CreateFormation(EFormationType Type, const FVector& CenterPos, int32 UnitCount);

    UFUNCTION(BlueprintCallable, Category = "Formation System")
    TArray<FVector> CalculateLineFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing);

    UFUNCTION(BlueprintCallable, Category = "Formation System")
    TArray<FVector> CalculateColumnFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing);

    UFUNCTION(BlueprintCallable, Category = "Formation System")
    TArray<FVector> CalculateWedgeFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing);

    UFUNCTION(BlueprintCallable, Category = "Formation System")
    TArray<FVector> CalculateBoxFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing);

    UFUNCTION(BlueprintCallable, Category = "Formation System")
    void UpdateFormationPositions(FMinionFormation& Formation, const FVector& NewCenterPos, const FRotator& NewOrientation);

    UFUNCTION(BlueprintCallable, Category = "Formation System")
    void AssignMinionsToFormation(const FMinionFormation& Formation, TArray<int32>& MinionIDs);

    // Funções de spawn points
    UFUNCTION(BlueprintCallable, Category = "Spawn Points")
    void InitializeSpawnPoints();

    UFUNCTION(BlueprintCallable, Category = "Spawn Points")
    int32 AddSpawnPoint(const FVector& Position, const FRotator& Rotation, ELaneType LaneType);

    UFUNCTION(BlueprintCallable, Category = "Spawn Points")
    void RemoveSpawnPoint(int32 SpawnPointIndex);

    UFUNCTION(BlueprintCallable, Category = "Spawn Points")
    bool GetAvailableSpawnPoint(ELaneType LaneType, FMinionSpawnPoint& OutSpawnPoint);

    UFUNCTION(BlueprintCallable, Category = "Spawn Points")
    void UpdateSpawnPointCooldowns(float DeltaTime);

    // Funções de validação e otimização
    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateWaveSystem();

    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidatePathfindingGrid();

    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateMinionData(const FMinionData& Minion);

    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void OptimizePathfinding();

    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void CullDistantMinions();

    UFUNCTION(BlueprintCallable, Category = "Optimization")
    void UpdateMinionLOD();

    // Funções de debug
    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugPathfindingGrid();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugMinionPaths();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugFormations();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugSpawnPoints();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void LogWaveStatistics();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void LogMinionStatistics();

    // Getters
    UFUNCTION(BlueprintCallable, Category = "Getters")
    int32 GetActiveMinionCount() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    int32 GetCurrentWaveNumber() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    float GetWaveProgress() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    TArray<FMinionData> GetMinionsInLane(ELaneType Lane) const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    TArray<FMinionData> GetMinionsInRadius(const FVector& Center, float Radius) const;

    FMinionData* GetMinionByID(int32 MinionID);

    // Setters
    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetWaveInterval(float NewInterval);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetPathfindingAlgorithm(EPathfindingAlgorithm NewAlgorithm);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetGridCellSize(float NewCellSize);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetWaveScalingFactor(float NewScalingFactor);

private:
    // Variáveis internas
    bool bIsInitialized;
    bool bWaveSystemActive;
    bool bWaveSystemPaused;
    float LastWaveStartTime;
    float LastPathUpdateTime;
    int32 NextMinionID;
    FTimerHandle WaveSpawnTimer;
    FTimerHandle PathUpdateTimer;
    FTimerHandle MinionUpdateTimer;
    
    // Cache de performance
    TMap<int32, TArray<FVector>> MinionPathCache;
    TMap<FVector2D, bool> WalkabilityCache;
    TArray<FPathfindingNode*> OpenSet;
    TArray<FPathfindingNode*> ClosedSet;
    
    // Funções auxiliares internas
    void InitializeDefaultWaves();
    void SetupDefaultSpawnPoints();
    void LoadMinionAssets();
    void UpdateWaveTimer(float DeltaTime);
    void ProcessMinionSpawning();
    void UpdateAllMinions(float DeltaTime);
    void CleanupDeadMinions();
    
    // Funções de pathfinding internas
    void ResetPathfindingNodes();
    FPathfindingNode* GetLowestFCostNode(const TArray<FPathfindingNode*>& NodeList);
    TArray<FVector> ReconstructPath(FPathfindingNode* EndNode);
    void UpdateObstacles();
    int32 GetNodeIndex(FPathfindingNode* Node);
    FPathfindingNode* GetNodeAtGridPosition(int32 X, int32 Y);
    TArray<FPathfindingNode*> GetNeighborNodes(FPathfindingNode* Node);
    float CalculateMovementCost(FPathfindingNode* FromNode, FPathfindingNode* ToNode);
    
    // Funções de otimização internas
    bool ShouldUpdateMinionPath(const FMinionData& Minion);
    void BatchUpdatePaths();
    void UpdateMinionCollisionAvoidance(FMinionData& Minion);
    
    // Funções de utilidade
    EMinionType GetRandomMinionType(int32 WaveNumber);
    ELaneType GetRandomLane();
    FVector GetLaneDirection(ELaneType Lane);
    FVector GetLaneEndPosition(ELaneType Lane);
    void ApplyWaveScaling(FMinionData& Minion, int32 WaveNumber);
};