#include "ABaronAuracronManager.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/SceneComponent.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "Math/UnrealMathUtility.h"
#include "TimerManager.h"
#include "Engine/World.h"
#include "Logging/LogMacros.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "AbilitySystemComponent.h"
#include "GameplayTagContainer.h"
#include "GameplayEffect.h"
#include "AttributeSet.h"
#include "Kismet/KismetSystemLibrary.h"
#include "GameFramework/PlayerState.h"

// Define log category para performance tracking
DEFINE_LOG_CATEGORY_STATIC(LogBaronAuracron, Log, All);

ABaronAuracronManager::ABaronAuracronManager()
{
    PrimaryActorTick.bCanEverTick = true;

    // Configurar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Configurar mesh do Barão
    BaronMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("BaronMesh"));
    BaronMesh->SetupAttachment(RootComponent);
    BaronMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    BaronMesh->SetCollisionResponseToAllChannels(ECR_Block);

    // Configurar área de combate (1200 UU conforme documentação)
    CombatArea = CreateDefaultSubobject<USphereComponent>(TEXT("CombatArea"));
    CombatArea->SetupAttachment(RootComponent);
    CombatArea->SetSphereRadius(COMBAT_AREA_RADIUS);
    CombatArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    CombatArea->SetCollisionResponseToAllChannels(ECR_Ignore);
    CombatArea->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Configurar área de buff (1200 UU conforme documentação)
    BuffArea = CreateDefaultSubobject<USphereComponent>(TEXT("BuffArea"));
    BuffArea->SetupAttachment(RootComponent);
    BuffArea->SetSphereRadius(BUFF_AREA_RADIUS);
    BuffArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    BuffArea->SetCollisionResponseToAllChannels(ECR_Ignore);
    BuffArea->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Inicializar dados padrão conforme documentação
    BaronData = FBaronData();
    BaronData.BaseHealth = BASE_BARON_HEALTH; // 5000
    BaronData.HealthScaling = BARON_HEALTH_SCALING; // 300 por minuto
    BaronData.Position = FVector(0.0f, -4800.0f, 0.0f); // Centro conforme doc
    
    HexagonalCovil = FHexagonalArea();
    HexagonalCovil.Center = BaronData.Position;
    HexagonalCovil.Radius = HEXAGON_RADIUS; // 700 UU
    
    TeamBuff = FBuffData();
    TeamBuff.DamageBonus = TEAM_BUFF_DAMAGE; // 20%
    TeamBuff.SpeedBonus = TEAM_BUFF_SPEED; // 15%
    TeamBuff.Duration = BUFF_DURATION; // 180s
    
    GameStartTime = 0.0f;
    CurrentGameTime = 0.0f;

    // Configurar posição inicial no centro do hexágono
    SetActorLocation(BaronData.Position);
}

void ABaronAuracronManager::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogBaronAuracron, Log, TEXT("Inicializando Baron Auracron Manager"));

    // Inicializar geometria hexagonal com validação
    CalculateHexagonalVertices();
    
    // Validar geometria hexagonal na inicialização
    if (!ValidateHexagonalGeometry())
    {
        UE_LOG(LogBaronAuracron, Error, TEXT("Falha na validação da geometria hexagonal!"));
    }
    
    // Inicializar sentinelas com logs
    InitializeSentinels();
    
    // Configurar timers
    GameStartTime = GetWorld()->GetTimeSeconds();
    
    // Configurar timer de spawn do Barão
    GetWorld()->GetTimerManager().SetTimer(
        BaronSpawnTimer,
        this,
        &ABaronAuracronManager::CheckSpawnConditions,
        1.0f, // Verificar a cada segundo
        true
    );
    
    // Configurar timer de rotação hexagonal (60s conforme documentação)
    GetWorld()->GetTimerManager().SetTimer(
        SentinelRotationTimer,
        this,
        &ABaronAuracronManager::UpdateSentinelRotations,
        HEXAGON_ROTATION_INTERVAL, // 60.0f segundos
        true
    );

    // Log das áreas configuradas
    UE_LOG(LogBaronAuracron, Log, TEXT("Área de combate configurada: raio %.1f UU"), COMBAT_AREA_RADIUS);
    UE_LOG(LogBaronAuracron, Log, TEXT("Área de buff configurada: raio %.1f UU"), BUFF_AREA_RADIUS);
    UE_LOG(LogBaronAuracron, Log, TEXT("Baron Auracron Manager inicializado na posição: %s"), *BaronData.Position.ToString());
}

void ABaronAuracronManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Atualizar tempo de jogo (otimizado)
    UpdateGameTime(DeltaTime);
    
    // Cache para evitar múltiplas verificações
    const bool bShouldSpawn = BaronData.CurrentState == EBaronState::Dormant && ShouldSpawnBaron();
    
    // Log de spawn apenas quando necessário
    if (bShouldSpawn)
    {
        UE_LOG(LogBaronAuracron, Log, TEXT("Baron ready to spawn at game time: %.1f seconds"), CurrentGameTime);
    }
    
    UpdateBaronBehavior(DeltaTime);
    
    // Atualizar sentinelas com menos frequência para performance
    static float SentinelUpdateTimer = 0.0f;
    SentinelUpdateTimer += DeltaTime;
    if (SentinelUpdateTimer >= 0.1f) // Atualizar a cada 100ms
    {
        UpdateSentinelBehavior(SentinelUpdateTimer);
        SentinelUpdateTimer = 0.0f;
    }
}

void ABaronAuracronManager::UpdateGameTime(float DeltaTime)
{
    CurrentGameTime = GetWorld()->GetTimeSeconds() - GameStartTime;
}

void ABaronAuracronManager::CheckSpawnConditions()
{
    if (BaronData.CurrentState == EBaronState::Dormant && ShouldSpawnBaron())
    {
        SpawnBaron();
    }
}

void ABaronAuracronManager::UpdateBaronBehavior(float DeltaTime)
{
    switch (BaronData.CurrentState)
    {
        case EBaronState::Spawning:
            // Implementar lógica de spawn baseada no estado atual
            if (BaronData.CurrentState == EBaronState::Spawning)
            {
                const float SpawnProgress = FMath::Clamp((GetWorld()->GetTimeSeconds() - BaronData.LastStateChangeTime) / 3.0f, 0.0f, 1.0f);
                
                if (SpawnProgress >= 1.0f)
                {
                    BaronData.CurrentState = EBaronState::Active;
                    BaronData.LastStateChangeTime = GetWorld()->GetTimeSeconds();
                    
                    UE_LOG(LogBaronAuracron, Log, TEXT("Baron spawn completed. State changed to Active."));
                    
                    // Ativar componentes do Baron
                    if (BaronData.BaronActor.Get())
                    {
                        BaronData.BaronActor->SetActorHiddenInGame(false);
                        BaronData.BaronActor->SetActorEnableCollision(true);
                    }
                }
                else
                {
                    // Atualizar efeitos visuais de spawn
                    if (BaronData.BaronActor.Get())
                    {
                        const FVector SpawnScale = FVector(SpawnProgress);
                        BaronData.BaronActor->SetActorScale3D(SpawnScale);
                    }
                }
            }
            break;
            
        case EBaronState::Active:
            // Verificar se deve iniciar rotação
            if (!GetWorld()->GetTimerManager().IsTimerActive(BaronRotationTimer))
            {
                StartBaronRotation();
            }
            break;
            
        case EBaronState::Combat:
            // Implementar lógica de combate ativo
            if (BaronData.bInCombat)
            {
                const float CombatDuration = GetWorld()->GetTimeSeconds() - BaronData.CombatStartTime;
                
                // Verificar se há jogadores na área de combate
                TArray<AActor*> PlayersInRange;
                UKismetSystemLibrary::SphereOverlapActors(
                    GetWorld(),
                    BaronData.BaronActor.Get() ? BaronData.BaronActor->GetActorLocation() : GetActorLocation(),
                    COMBAT_AREA_RADIUS,
                    TArray<TEnumAsByte<EObjectTypeQuery>>(),
                    APawn::StaticClass(),
                    TArray<AActor*>(),
                    PlayersInRange
                );
                
                if (PlayersInRange.Num() == 0)
                {
                    // Sem jogadores na área - encerrar combate após 5 segundos
                    if (CombatDuration > 5.0f)
                    {
                        EndCombat();
                        UE_LOG(LogBaronAuracron, Log, TEXT("Combat ended - no players in range for 5 seconds"));
                    }
                }
                else
                {
                    // Atualizar comportamento de combate
                    UpdateCombatBehavior(CombatDuration, PlayersInRange.Num());
                }
            }
            break;
            
        case EBaronState::Rotating:
        {
            // Implementar lógica de rotação do hexágono
            const float CurrentTime = GetWorld()->GetTimeSeconds();
            const float TimeSinceLastRotation = CurrentTime - BaronData.LastRotationTime;
            
            if (TimeSinceLastRotation >= HEXAGON_ROTATION_INTERVAL)
            {
                // Calcular nova rotação baseada no tempo
                const float RotationSpeed = 30.0f; // graus por segundo
                const float NewRotation = BaronData.CurrentRotation + (RotationSpeed * TimeSinceLastRotation);
                BaronData.CurrentRotation = FMath::Fmod(NewRotation, 360.0f);
                BaronData.LastRotationTime = CurrentTime;
                
                // Aplicar rotação aos componentes visuais
                if (HexagonMeshComponent)
                {
                    const FRotator NewRotator = FRotator(0.0f, BaronData.CurrentRotation, 0.0f);
                    HexagonMeshComponent->SetWorldRotation(NewRotator);
                }
                
                // Atualizar posições das sentinelas baseado na nova rotação
                UpdateSentinelPositionsForRotation(BaronData.CurrentRotation);
                
                UE_LOG(LogBaronAuracron, VeryVerbose, TEXT("Hexagon rotated to %.2f degrees"), BaronData.CurrentRotation);
            }
            break;
        }
            
        default:
            break;
    }
}

void ABaronAuracronManager::UpdateSentinelBehavior(float DeltaTime)
{
    // Atualizar comportamento das sentinelas cristalinas
    for (int32 i = 0; i < SentinelasCristalinas.Num(); i++)
    {
        if (SentinelasCristalinas[i].bIsAlive)
        {
            // Implementar lógica de comportamento das sentinelas
        for (int32 j = 0; j < SentinelData.Num(); ++j)
        {
            auto& Sentinel = SentinelData[j];
            if (!Sentinel.SentinelActor.Get())
            {
                continue;
            }
            
            const float CurrentTime = GetWorld()->GetTimeSeconds();
            
            // Verificar se a sentinela deve atacar jogadores próximos
            TArray<AActor*> NearbyPlayers;
            UKismetSystemLibrary::SphereOverlapActors(
                GetWorld(),
                Sentinel.SentinelActor->GetActorLocation(),
                SENTINEL_ATTACK_RANGE,
                TArray<TEnumAsByte<EObjectTypeQuery>>(),
                APawn::StaticClass(),
                TArray<AActor*>(),
                NearbyPlayers
            );
            
            if (NearbyPlayers.Num() > 0 && (CurrentTime - Sentinel.LastAttackTime) >= SENTINEL_ATTACK_COOLDOWN)
            {
                // Atacar o jogador mais próximo
                AActor* ClosestPlayer = nullptr;
                float ClosestDistance = FLT_MAX;
                
                for (AActor* Player : NearbyPlayers)
                {
                    const float Distance = FVector::Dist(Sentinel.SentinelActor->GetActorLocation(), Player->GetActorLocation());
                    if (Distance < ClosestDistance)
                    {
                        ClosestDistance = Distance;
                        ClosestPlayer = Player;
                    }
                }
                
                if (ClosestPlayer)
                {
                    // Executar ataque da sentinela
                    ExecuteSentinelAttack(i, ClosestPlayer);
                    Sentinel.LastAttackTime = CurrentTime;
                    
                    UE_LOG(LogBaronAuracron, Log, TEXT("Sentinel %d attacked player at distance %.2f"), i, ClosestDistance);
                }
            }
            
            // Atualizar rotação da sentinela para olhar para jogadores próximos
            if (NearbyPlayers.Num() > 0)
            {
                const FVector TargetLocation = NearbyPlayers[0]->GetActorLocation();
                const FVector SentinelLocation = Sentinel.SentinelActor->GetActorLocation();
                const FVector Direction = (TargetLocation - SentinelLocation).GetSafeNormal();
                const FRotator TargetRotation = Direction.Rotation();
                
                Sentinel.SentinelActor->SetActorRotation(FMath::RInterpTo(
                    Sentinel.SentinelActor->GetActorRotation(),
                    TargetRotation,
                    GetWorld()->GetDeltaSeconds(),
                    2.0f
                ));
            }
        }
        }
    }
    
    // Atualizar comportamento dos guardiões
    for (int32 i = 0; i < GuardioesPortais.Num(); i++)
    {
        if (GuardioesPortais[i].bIsAlive)
        {
            // Implementar lógica de comportamento dos guardiões
        for (int32 k = 0; k < GuardianData.Num(); ++k)
        {
            auto& Guardian = GuardianData[k];
            if (!Guardian.GuardianActor.Get())
            {
                continue;
            }
            
            const float CurrentTime = GetWorld()->GetTimeSeconds();
            
            // Verificar jogadores tentando usar portais próximos
            TArray<AActor*> PlayersNearPortal;
            UKismetSystemLibrary::SphereOverlapActors(
                GetWorld(),
                Guardian.GuardianActor->GetActorLocation(),
                GUARDIAN_DETECTION_RANGE,
                TArray<TEnumAsByte<EObjectTypeQuery>>(),
                APawn::StaticClass(),
                TArray<AActor*>(),
                PlayersNearPortal
            );
            
            if (PlayersNearPortal.Num() > 0)
            {
                // Ativar modo de defesa do guardião
                if (!Guardian.bIsDefending)
                {
                    Guardian.bIsDefending = true;
                    Guardian.DefenseStartTime = CurrentTime;
                    
                    UE_LOG(LogBaronAuracron, Log, TEXT("Guardian %d activated defense mode - %d players detected"), i, PlayersNearPortal.Num());
                }
                
                // Atacar jogadores se estiverem muito próximos
                for (AActor* Player : PlayersNearPortal)
                {
                    const float DistanceToPlayer = FVector::Dist(Guardian.GuardianActor->GetActorLocation(), Player->GetActorLocation());
                    
                    if (DistanceToPlayer <= GUARDIAN_ATTACK_RANGE && (CurrentTime - Guardian.LastAttackTime) >= GUARDIAN_ATTACK_COOLDOWN)
                    {
                        // Executar ataque do guardião
                        ExecuteGuardianAttack(i, Player);
                        Guardian.LastAttackTime = CurrentTime;
                        
                        UE_LOG(LogBaronAuracron, Log, TEXT("Guardian %d attacked player at distance %.2f"), i, DistanceToPlayer);
                    }
                }
                
                // Rotacionar para o jogador mais próximo
                AActor* ClosestPlayer = PlayersNearPortal[0];
                float ClosestDistance = FVector::Dist(Guardian.GuardianActor->GetActorLocation(), ClosestPlayer->GetActorLocation());
                
                for (AActor* Player : PlayersNearPortal)
                {
                    const float Distance = FVector::Dist(Guardian.GuardianActor->GetActorLocation(), Player->GetActorLocation());
                    if (Distance < ClosestDistance)
                    {
                        ClosestDistance = Distance;
                        ClosestPlayer = Player;
                    }
                }
                
                const FVector Direction = (ClosestPlayer->GetActorLocation() - Guardian.GuardianActor->GetActorLocation()).GetSafeNormal();
                const FRotator TargetRotation = Direction.Rotation();
                
                Guardian.GuardianActor->SetActorRotation(FMath::RInterpTo(
                    Guardian.GuardianActor->GetActorRotation(),
                    TargetRotation,
                    GetWorld()->GetDeltaSeconds(),
                    3.0f
                ));
            }
            else if (Guardian.bIsDefending)
            {
                // Desativar modo de defesa após 3 segundos sem jogadores
                if ((CurrentTime - Guardian.DefenseStartTime) >= 3.0f)
                {
                    Guardian.bIsDefending = false;
                    UE_LOG(LogBaronAuracron, Log, TEXT("Guardian %d deactivated defense mode"), i);
                }
            }
        }
        }
    }
}

// Funções de geometria hexagonal
void ABaronAuracronManager::CalculateHexagonalVertices()
{
    HexagonalCovil.Vertices.Empty();
    
    for (int32 i = 0; i < 6; i++)
    {
        FVector Vertex = CalculateHexagonVertex(i, HexagonalCovil.Center, HexagonalCovil.Radius);
        HexagonalCovil.Vertices.Add(Vertex);
    }
    
    // Calcular área
    HexagonalCovil.Area = CalculateHexagonalArea();
    
    // Calcular posições das entradas
    HexagonalCovil.EntrancePositions = GetHexagonalEntrances();
    
    UE_LOG(LogTemp, Warning, TEXT("Hexágono calculado - Área: %f, Vértices: %d"), 
           HexagonalCovil.Area, HexagonalCovil.Vertices.Num());
}

FVector ABaronAuracronManager::CalculateHexagonVertex(int32 VertexIndex, const FVector& Center, float Radius) const
{
    float AngleRadians = FMath::DegreesToRadians(VertexIndex * DEGREES_PER_VERTEX);
    float X = Center.X + Radius * FMath::Cos(AngleRadians);
    float Y = Center.Y + Radius * FMath::Sin(AngleRadians);
    return FVector(X, Y, Center.Z);
}

float ABaronAuracronManager::CalculateHexagonalArea() const
{
    return HEXAGON_AREA_MULTIPLIER * FMath::Pow(HexagonalCovil.Radius, 2);
}

TArray<FVector> ABaronAuracronManager::GetHexagonalEntrances() const
{
    TArray<FVector> Entrances;
    
    // Calcular entradas no meio de cada lado do hexágono
    for (int32 i = 0; i < 6; i++)
    {
        int32 NextIndex = (i + 1) % 6;
        FVector MidPoint = (HexagonalCovil.Vertices[i] + HexagonalCovil.Vertices[NextIndex]) * 0.5f;
        Entrances.Add(MidPoint);
    }
    
    return Entrances;
}

bool ABaronAuracronManager::IsPositionInHexagon(const FVector& Position) const
{
    return IsPointInsideHexagon(Position, HexagonalCovil.Vertices);
}

bool ABaronAuracronManager::IsPointInsideHexagon(const FVector& Point, const TArray<FVector>& HexVertices) const
{
    if (HexVertices.Num() != 6) return false;
    
    // Usar algoritmo de ray casting para determinar se o ponto está dentro do hexágono
    int32 Intersections = 0;
    
    for (int32 i = 0; i < 6; i++)
    {
        int32 NextIndex = (i + 1) % 6;
        
        FVector V1 = HexVertices[i];
        FVector V2 = HexVertices[NextIndex];
        
        // Verificar interseção com raio horizontal
        if (((V1.Y > Point.Y) != (V2.Y > Point.Y)) &&
            (Point.X < (V2.X - V1.X) * (Point.Y - V1.Y) / (V2.Y - V1.Y) + V1.X))
        {
            Intersections++;
        }
    }
    
    return (Intersections % 2) == 1;
}

FVector ABaronAuracronManager::GetClosestEntrancePosition(const FVector& FromPosition) const
{
    if (HexagonalCovil.EntrancePositions.Num() == 0)
    {
        return HexagonalCovil.Center;
    }
    
    FVector ClosestEntrance = HexagonalCovil.EntrancePositions[0];
    float MinDistance = FVector::Dist(FromPosition, ClosestEntrance);
    
    for (int32 i = 1; i < HexagonalCovil.EntrancePositions.Num(); i++)
    {
        float Distance = FVector::Dist(FromPosition, HexagonalCovil.EntrancePositions[i]);
        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            ClosestEntrance = HexagonalCovil.EntrancePositions[i];
        }
    }
    
    return ClosestEntrance;
}

float ABaronAuracronManager::CalculateDistanceToHexagonEdge(const FVector& Point) const
{
    float MinDistance = FLT_MAX;
    
    for (int32 i = 0; i < HexagonalCovil.Vertices.Num(); i++)
    {
        int32 NextIndex = (i + 1) % HexagonalCovil.Vertices.Num();
        
        // Calcular distância do ponto à linha entre dois vértices
        FVector LineStart = HexagonalCovil.Vertices[i];
        FVector LineEnd = HexagonalCovil.Vertices[NextIndex];
        
        float Distance = FMath::PointDistToLine(Point, LineEnd - LineStart, LineStart);
        MinDistance = FMath::Min(MinDistance, Distance);
    }
    
    return MinDistance;
}

// Funções de spawn e controle do Barão
void ABaronAuracronManager::SpawnBaron()
{
    // Validação de segurança otimizada
    if (BaronData.CurrentState != EBaronState::Dormant)
    {
        UE_LOG(LogBaronAuracron, Warning, TEXT("Tentativa de spawn do Barão quando não está dormant"));
        return;
    }
    
    SetBaronState(EBaronState::Spawning);
    
    // Calcular health baseado no tempo de jogo - otimizado
    const float GameTimeMinutes = CurrentGameTime * 0.0166667f; // 1/60 pré-calculado
    const float NewHealth = BaronData.BaseHealth + (BaronData.HealthScaling * GameTimeMinutes);
    
    // Atualizar dados do Barão atomicamente
    BaronData.CurrentHealth = NewHealth;
    
    // Posicionar o Barão no centro do hexágono
    SetActorLocation(HexagonalCovil.Center);
    
    // Tornar visível
    BaronMesh->SetVisibility(true);
    BaronMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    
    SetBaronState(EBaronState::Active);
    
    UE_LOG(LogBaronAuracron, Log, TEXT("Barão Auracron spawnou com %.0f HP após %.1f minutos de jogo (Escalamento: +%.0f HP)"), 
           NewHealth, GameTimeMinutes, (BaronData.HealthScaling * GameTimeMinutes));
}

void ABaronAuracronManager::DespawnBaron()
{
    SetBaronState(EBaronState::Dormant);
    
    // Tornar invisível
    BaronMesh->SetVisibility(false);
    BaronMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    
    // Limpar timers
    GetWorld()->GetTimerManager().ClearTimer(BaronRotationTimer);
    
    UE_LOG(LogTemp, Warning, TEXT("Barão Auracron despawnou"));
}

void ABaronAuracronManager::UpdateBaronHealth()
{
    float GameTimeMinutes = CurrentGameTime / 60.0f;
    BaronData.CurrentHealth = BaronData.BaseHealth + (BaronData.HealthScaling * GameTimeMinutes);
}

void ABaronAuracronManager::StartBaronRotation()
{
    if (BaronData.CurrentState == EBaronState::Active)
    {
        // Rotação hexagonal a cada 60 segundos conforme documentação
        GetWorld()->GetTimerManager().SetTimer(
            BaronRotationTimer,
            this,
            &ABaronAuracronManager::PerformBaronRotation,
            HEXAGON_ROTATION_INTERVAL, // 60.0f segundos
            true
        );
    }
}

void ABaronAuracronManager::PerformBaronRotation()
{
    if (BaronData.CurrentState != EBaronState::Active)
    {
        return;
    }
    
    SetBaronState(EBaronState::Rotating);
    
    // Rotação hexagonal: 60 graus (360/6) para manter geometria hexagonal
    FRotator CurrentRotation = GetActorRotation();
    FRotator NewRotation = CurrentRotation + FRotator(0.0f, 60.0f, 0.0f);
    SetActorRotation(NewRotation);
    
    // Voltar ao estado ativo após rotação
    FTimerHandle RotationCompleteTimer;
    GetWorld()->GetTimerManager().SetTimer(
        RotationCompleteTimer,
        [this]()
        {
            SetBaronState(EBaronState::Active);
        },
        2.0f, // 2 segundos para completar rotação hexagonal
        false
    );
    
    UE_LOG(LogTemp, Warning, TEXT("Barão Auracron rotacionou hexagonalmente para: %s"), *NewRotation.ToString());
}

// Funções de combate
void ABaronAuracronManager::StartCombat(AActor* Attacker)
{
    if (BaronData.CurrentState == EBaronState::Active)
    {
        SetBaronState(EBaronState::Combat);
        UE_LOG(LogTemp, Warning, TEXT("Combate iniciado com o Barão Auracron"));
    }
}

void ABaronAuracronManager::EndCombat()
{
    if (BaronData.CurrentState == EBaronState::Combat)
    {
        SetBaronState(EBaronState::Active);
        UE_LOG(LogTemp, Warning, TEXT("Combate com o Barão Auracron terminou"));
    }
}

float ABaronAuracronManager::TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser)
{
    if (BaronData.CurrentState == EBaronState::Dead || BaronData.CurrentState == EBaronState::Dormant)
    {
        return 0.0f;
    }
    
    // Chamar implementação da classe base
    float ActualDamage = Super::TakeDamage(DamageAmount, DamageEvent, EventInstigator, DamageCauser);
    
    BaronData.CurrentHealth -= ActualDamage;
    
    UE_LOG(LogTemp, Warning, TEXT("Barão Auracron recebeu %f de dano. Vida restante: %f"), 
           ActualDamage, BaronData.CurrentHealth);
    
    if (BaronData.CurrentHealth <= 0.0f)
    {
        OnBaronDeath(DamageCauser);
    }
    
    return ActualDamage;
}

void ABaronAuracronManager::OnBaronDeath(AActor* Killer)
{
    // Validação de segurança
    if (BaronData.CurrentState == EBaronState::Dead || BaronData.CurrentState == EBaronState::Dormant)
    {
        UE_LOG(LogBaronAuracron, Warning, TEXT("Tentativa de morte do Barão quando já está morto ou dormant"));
        return;
    }
    
    SetBaronState(EBaronState::Dead);
    
    // Determinar time vencedor com validação
    int32 WinningTeam = 1; // Simplificado - deveria verificar o time do Killer
    
    if (WinningTeam <= 0)
    {
        UE_LOG(LogBaronAuracron, Error, TEXT("Team ID inválido para morte do Barão: %d"), WinningTeam);
        return;
    }
    
    const float DeathTime = CurrentGameTime;
    
    // Log detalhado da morte
    UE_LOG(LogBaronAuracron, Log, TEXT("Barão Auracron derrotado pelo Time %d aos %.1f segundos de jogo"), 
           WinningTeam, DeathTime);
    
    // Aplicar buff ao time vencedor com validação
    ApplyTeamBuff(WinningTeam);
    
    // Distribuir recompensas
    DistributeRewards(WinningTeam);
    
    // Programar respawn (6 minutos = 360s conforme documentação)
    FTimerHandle RespawnTimer;
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            RespawnTimer,
            [this]()
            {
                DespawnBaron();
                // Resetar para permitir novo spawn após o tempo de respawn
                FTimerHandle DelayedSpawnTimer;
                GetWorld()->GetTimerManager().SetTimer(
                    DelayedSpawnTimer,
                    [this]()
                    {
                        SetBaronState(EBaronState::Dormant);
                    },
                    BARON_RESPAWN_TIME, // 360.0f segundos
                    false
                );
            },
            5.0f, // 5 segundos para mostrar morte
            false
        );
    }
    
    UE_LOG(LogBaronAuracron, Log, TEXT("Respawn do Barão programado para %.0f segundos (%.1f minutos)"), 
           BARON_RESPAWN_TIME, BARON_RESPAWN_TIME / 60.0f);
}

// Funções de sentinelas
void ABaronAuracronManager::InitializeSentinels()
{
    // Inicializar 4 sentinelas cristalinas conforme documentação
    // Posições: (±3000, ±2400) com raio 400 UU
    SentinelasCristalinas.Empty();
    
    TArray<FVector> SentinelPositions = {
        FVector(3000.0f, 2400.0f, 0.0f),
        FVector(-3000.0f, 2400.0f, 0.0f),
        FVector(3000.0f, -2400.0f, 0.0f),
        FVector(-3000.0f, -2400.0f, 0.0f)
    };
    
    for (int32 i = 0; i < 4; i++)
    {
        FSentinelData Sentinel;
        Sentinel.Type = ESentinelType::Cristalina;
        Sentinel.Position = SentinelPositions[i];
        Sentinel.Radius = SENTINEL_RADIUS; // 400 UU
        Sentinel.bIsAlive = true;
        Sentinel.RotationSpeed = 1.0f; // Graus por segundo
        
        SentinelasCristalinas.Add(Sentinel);
    }
    
    // Inicializar 2 guardiões dos portais conforme documentação
    // Posições: (±4500, 0) com raio 300 UU
    GuardioesPortais.Empty();
    
    TArray<FVector> GuardianPositions = {
        FVector(4500.0f, 0.0f, 0.0f),
        FVector(-4500.0f, 0.0f, 0.0f)
    };
    
    for (int32 i = 0; i < 2; i++)
    {
        FGuardianData Guardian;
        Guardian.Position = GuardianPositions[i];
        Guardian.OriginalPosition = GuardianPositions[i];
        Guardian.Radius = GUARDIAN_RADIUS; // 300 UU
        Guardian.bIsAlive = true;
        Guardian.RotationSpeed = 0.5f; // Graus por segundo
        Guardian.Health = 1000.0f;
        Guardian.RespawnTime = 30.0f;
        Guardian.LastAttackTime = 0.0f;
        Guardian.AttackCooldown = 2.0f;
        Guardian.bDefenseMode = false;
        Guardian.DefenseModeStartTime = 0.0f;
        Guardian.bIsDefending = false;
        Guardian.DefenseStartTime = 0.0f;

        GuardioesPortais.Add(Guardian);
    }
    
    SpawnSentinelasCristalinas();
    SpawnGuardioesPortais();
}

void ABaronAuracronManager::SpawnSentinelasCristalinas()
{
    for (int32 i = 0; i < SentinelasCristalinas.Num(); i++)
    {
        SentinelasCristalinas[i].bIsAlive = true;
        UE_LOG(LogTemp, Warning, TEXT("Sentinela Cristalina %d spawnou na posição: %s"), 
               i, *SentinelasCristalinas[i].Position.ToString());
    }
}

void ABaronAuracronManager::SpawnGuardioesPortais()
{
    for (int32 i = 0; i < GuardioesPortais.Num(); i++)
    {
        GuardioesPortais[i].bIsAlive = true;
        UE_LOG(LogTemp, Warning, TEXT("Guardião dos Portais %d spawnou na posição: %s"), 
               i, *GuardioesPortais[i].Position.ToString());
    }
}

void ABaronAuracronManager::UpdateSentinelRotations()
{
    // Rotacionar sentinelas cristalinas
    for (int32 i = 0; i < SentinelasCristalinas.Num(); i++)
    {
        if (SentinelasCristalinas[i].bIsAlive)
        {
            FVector NewPosition = RotatePointAroundCenter(
                SentinelasCristalinas[i].Position,
                HexagonalCovil.Center,
                SentinelasCristalinas[i].RotationSpeed
            );
            SentinelasCristalinas[i].Position = NewPosition;
        }
    }
    
    // Rotacionar guardiões dos portais
    for (int32 i = 0; i < GuardioesPortais.Num(); i++)
    {
        if (GuardioesPortais[i].bIsAlive)
        {
            FVector NewPosition = RotatePointAroundCenter(
                GuardioesPortais[i].Position,
                HexagonalCovil.Center,
                GuardioesPortais[i].RotationSpeed
            );
            GuardioesPortais[i].Position = NewPosition;
        }
    }
}

FVector ABaronAuracronManager::RotatePointAroundCenter(const FVector& Point, const FVector& Center, float AngleDegrees) const
{
    float AngleRadians = FMath::DegreesToRadians(AngleDegrees);
    float CosAngle = FMath::Cos(AngleRadians);
    float SinAngle = FMath::Sin(AngleRadians);
    
    FVector RelativePoint = Point - Center;
    
    float NewX = RelativePoint.X * CosAngle - RelativePoint.Y * SinAngle;
    float NewY = RelativePoint.X * SinAngle + RelativePoint.Y * CosAngle;
    
    return Center + FVector(NewX, NewY, RelativePoint.Z);
}

void ABaronAuracronManager::RespawnSentinel(int32 SentinelIndex, ESentinelType Type)
{
    if (Type == ESentinelType::Cristalina && SentinelIndex < SentinelasCristalinas.Num())
    {
        SentinelasCristalinas[SentinelIndex].bIsAlive = true;
        UE_LOG(LogTemp, Warning, TEXT("Sentinela Cristalina %d respawnou"), SentinelIndex);
    }
    else if (Type == ESentinelType::Guardian && SentinelIndex < GuardioesPortais.Num())
    {
        GuardioesPortais[SentinelIndex].bIsAlive = true;
        UE_LOG(LogTemp, Warning, TEXT("Guardião dos Portais %d respawnou"), SentinelIndex);
    }
}

// Sistema de buffs
void ABaronAuracronManager::ApplyTeamBuff(int32 TeamID)
{
    // Validação robusta do TeamID
    if (TeamID <= 0)
    {
        UE_LOG(LogBaronAuracron, Error, TEXT("TeamID inválido para aplicação de buff: %d"), TeamID);
        return;
    }
    
    // Verificar se já existe um buff ativo
    if (TeamBuff.TeamID > 0)
    {
        UE_LOG(LogBaronAuracron, Warning, TEXT("Substituindo buff ativo do Time %d pelo Time %d"), 
               TeamBuff.TeamID, TeamID);
    }
    
    // Configurar buff atomicamente
    TeamBuff.TeamID = TeamID;
    
    TArray<AActor*> PlayersInRange = GetPlayersInBuffRange();
    
    for (AActor* Player : PlayersInRange)
    {
        // Aplicar buff aos jogadores do time vencedor
        // Implementação específica dependeria do sistema de jogadores
        UE_LOG(LogBaronAuracron, Log, TEXT("Buff aplicado ao jogador: %s"), *Player->GetName());
    }
    
    // Configurar timer com validação
    if (UWorld* World = GetWorld())
    {
        // Limpar timer anterior se existir
        World->GetTimerManager().ClearTimer(BuffDurationTimer);
        
        // Configurar novo timer para remover buff
        World->GetTimerManager().SetTimer(
            BuffDurationTimer,
            [this, TeamID]()
            {
                RemoveTeamBuff(TeamID);
            },
            BUFF_DURATION,
            false
        );
    }
    
    UE_LOG(LogBaronAuracron, Log, TEXT("Buff do Barão aplicado ao time %d: +%.1f%% dano, +%.1f%% velocidade por %.0f segundos"), 
           TeamID, TeamBuff.DamageBonus * 100.0f, TeamBuff.SpeedBonus * 100.0f, BUFF_DURATION);
}

void ABaronAuracronManager::RemoveTeamBuff(int32 TeamID)
{
    if (TeamBuff.TeamID > 0)
    {
        TArray<AActor*> PlayersInRange = GetPlayersInBuffRange();

        for (AActor* Player : PlayersInRange)
        {
            if (APawn* PlayerPawn = Cast<APawn>(Player))
            {
                // Remover buff de ataque
                if (UAbilitySystemComponent* ASC = PlayerPawn->FindComponentByClass<UAbilitySystemComponent>())
                {
                    // Remover efeito de buff de ataque (25% de dano extra)
                    FGameplayTagContainer TagsToRemove;
                    TagsToRemove.AddTag(FGameplayTag::RequestGameplayTag(FName("Buff.Baron.AttackDamage")));
                    ASC->RemoveActiveEffectsWithTags(TagsToRemove);
                    
                    UE_LOG(LogBaronAuracron, Log, TEXT("Removed attack buff from player: %s"), *Player->GetName());
                }
                
                // Remover buff visual se existir
                if (UStaticMeshComponent* BuffVisual = PlayerPawn->FindComponentByClass<UStaticMeshComponent>())
                {
                    if (BuffVisual->GetName().Contains(TEXT("BaronBuff")))
                    {
                        BuffVisual->SetVisibility(false);
                    }
                }
            }
        }
        
        // Resetar dados do buff
        TeamBuff.TeamID = 0;
        TeamBuff.BuffStartTime = 0.0f;
        
        UE_LOG(LogBaronAuracron, Log, TEXT("Baron team buff removed from all players"));
    }
    else
    {
        UE_LOG(LogBaronAuracron, Warning, TEXT("Attempted to remove buff but no active buff found"));
    }
}

TArray<AActor*> ABaronAuracronManager::GetPlayersInBuffRange() const
{
    TArray<AActor*> PlayersInRange;
    
    // Buscar todos os pawns na área de buff
    TArray<AActor*> OverlappingActors;
    BuffArea->GetOverlappingActors(OverlappingActors, APawn::StaticClass());
    
    for (AActor* Actor : OverlappingActors)
    {
        if (APawn* Pawn = Cast<APawn>(Actor))
        {
            PlayersInRange.Add(Pawn);
        }
    }
    
    return PlayersInRange;
}

void ABaronAuracronManager::DistributeRewards(int32 WinningTeam)
{
    // Calcular recompensas baseadas no tempo de jogo
    float GameTimeMinutes = CurrentGameTime / 60.0f;
    float GoldReward = 300.0f + (GameTimeMinutes * 10.0f);
    float ExperienceReward = 800.0f + (GameTimeMinutes * 25.0f);
    
    TArray<AActor*> PlayersInRange = GetPlayersInBuffRange();
    
    for (AActor* Player : PlayersInRange)
    {
        // Implementar distribuição real de recompensas aos jogadores do time vencedor
        if (APawn* PlayerPawn = Cast<APawn>(Player))
        {
            // Verificar se o jogador pertence ao time vencedor
            if (APlayerController* PC = Cast<APlayerController>(PlayerPawn->GetController()))
            {
                // Assumindo que existe um componente ou interface para verificar team ID
                int32 PlayerTeamID = GetPlayerTeamID(PC);
                
                if (PlayerTeamID == WinningTeam)
                {
                    // Distribuir ouro
                    if (UAttributeSet* AttributeSet = PlayerPawn->FindComponentByClass<UAttributeSet>())
                    {
                        // Adicionar ouro usando Gameplay Ability System
                        if (UAbilitySystemComponent* ASC = PlayerPawn->FindComponentByClass<UAbilitySystemComponent>())
                        {
                            FGameplayEffectSpecHandle GoldEffectSpec = ASC->MakeOutgoingSpec(
                                GoldRewardEffect,
                                1.0f,
                                ASC->MakeEffectContext()
                            );
                            
                            if (GoldEffectSpec.IsValid())
                            {
                                GoldEffectSpec.Data->SetSetByCallerMagnitude(
                                    FGameplayTag::RequestGameplayTag(FName("Reward.Gold")),
                                    GoldReward
                                );
                                ASC->ApplyGameplayEffectSpecToSelf(*GoldEffectSpec.Data.Get());
                            }
                        }
                    }
                    
                    // Distribuir experiência
                    if (UAbilitySystemComponent* ASC = PlayerPawn->FindComponentByClass<UAbilitySystemComponent>())
                    {
                        FGameplayEffectSpecHandle XPEffectSpec = ASC->MakeOutgoingSpec(
                            ExperienceRewardEffect,
                            1.0f,
                            ASC->MakeEffectContext()
                        );
                        
                        if (XPEffectSpec.IsValid())
                        {
                            XPEffectSpec.Data->SetSetByCallerMagnitude(
                                FGameplayTag::RequestGameplayTag(FName("Reward.Experience")),
                                ExperienceReward
                            );
                            ASC->ApplyGameplayEffectSpecToSelf(*XPEffectSpec.Data.Get());
                        }
                    }
                    
                    // Log detalhado da distribuição
                    UE_LOG(LogBaronAuracron, Log, TEXT("Rewards distributed to player %s (Team %d): %.0f gold, %.0f XP"), 
                           *Player->GetName(), PlayerTeamID, GoldReward, ExperienceReward);
                    
                    // Efeito visual de recompensa
                    SpawnRewardEffect(PlayerPawn->GetActorLocation());
                }
                else
                {
                    UE_LOG(LogBaronAuracron, VeryVerbose, TEXT("Player %s (Team %d) not eligible for rewards - winning team is %d"), 
                           *Player->GetName(), PlayerTeamID, WinningTeam);
                }
            }
        }
        UE_LOG(LogTemp, Warning, TEXT("Recompensas distribuídas: %f ouro, %f XP para %s"), 
               GoldReward, ExperienceReward, *Player->GetName());
    }
}

// Funções de validação e debug
bool ABaronAuracronManager::ValidateHexagonalGeometry() const
{
    if (HexagonalCovil.Vertices.Num() != 6)
    {
        UE_LOG(LogTemp, Error, TEXT("Hexágono deve ter exatamente 6 vértices"));
        return false;
    }
    
    // Verificar se todos os lados têm o mesmo comprimento
    float ExpectedSideLength = HexagonalCovil.Radius;
    for (int32 i = 0; i < 6; i++)
    {
        int32 NextIndex = (i + 1) % 6;
        float SideLength = FVector::Dist(HexagonalCovil.Vertices[i], HexagonalCovil.Vertices[NextIndex]);
        
        if (FMath::Abs(SideLength - ExpectedSideLength) > 1.0f) // Tolerância de 1 UU
        {
            UE_LOG(LogTemp, Error, TEXT("Lado %d tem comprimento incorreto: %f (esperado: %f)"), 
                   i, SideLength, ExpectedSideLength);
            return false;
        }
    }
    
    return true;
}

void ABaronAuracronManager::DrawDebugHexagon() const
{
    if (!GetWorld()) return;
    
    // Desenhar vértices do hexágono
    for (int32 i = 0; i < HexagonalCovil.Vertices.Num(); i++)
    {
        int32 NextIndex = (i + 1) % HexagonalCovil.Vertices.Num();
        
        DrawDebugLine(
            GetWorld(),
            HexagonalCovil.Vertices[i],
            HexagonalCovil.Vertices[NextIndex],
            FColor::Red,
            false,
            1.0f,
            0,
            5.0f
        );
    }
    
    // Desenhar centro
    DrawDebugSphere(
        GetWorld(),
        HexagonalCovil.Center,
        50.0f,
        12,
        FColor::Yellow,
        false,
        1.0f
    );
    
    // Desenhar entradas
    for (const FVector& Entrance : HexagonalCovil.EntrancePositions)
    {
        DrawDebugSphere(
            GetWorld(),
            Entrance,
            25.0f,
            8,
            FColor::Green,
            false,
            1.0f
        );
    }
}

void ABaronAuracronManager::DrawDebugSentinels() const
{
    if (!GetWorld()) return;
    
    // Desenhar sentinelas cristalinas
    for (const FSentinelData& Sentinel : SentinelasCristalinas)
    {
        FColor Color = Sentinel.bIsAlive ? FColor::Blue : FLinearColor::Gray.ToFColor(true);
        DrawDebugSphere(
            GetWorld(),
            Sentinel.Position,
            Sentinel.Radius,
            12,
            Color,
            false,
            1.0f
        );
    }
    
    // Desenhar guardiões dos portais
    for (const FGuardianData& Guardian : GuardioesPortais)
    {
        FColor Color = Guardian.bIsAlive ? FColor::Purple : FLinearColor::Gray.ToFColor(true);
        DrawDebugSphere(
            GetWorld(),
            Guardian.Position,
            Guardian.Radius,
            12,
            Color,
            false,
            1.0f
        );
    }
}

// Funções de timing e estado
bool ABaronAuracronManager::ShouldSpawnBaron() const
{
    // Spawn em 20 minutos (1200s) conforme documentação
    return CurrentGameTime >= BARON_SPAWN_TIME && BaronData.CurrentState == EBaronState::Dormant;
}

float ABaronAuracronManager::GetTimeUntilSpawn() const
{
    return FMath::Max(0.0f, BARON_SPAWN_TIME - CurrentGameTime);
}

float ABaronAuracronManager::GetTimeUntilRespawn() const
{
    if (BaronData.CurrentState == EBaronState::Dead)
    {
        return BARON_RESPAWN_TIME; // Simplificado
    }
    return 0.0f;
}

void ABaronAuracronManager::SetBaronState(EBaronState NewState)
{
    EBaronState OldState = BaronData.CurrentState;
    BaronData.CurrentState = NewState;
    
    UE_LOG(LogBaronAuracron, Log, TEXT("Estado do Barão mudou de %d para %d"), 
           (int32)OldState, (int32)NewState);
}

// Funções auxiliares implementadas
void ABaronAuracronManager::UpdateCombatBehavior(float CombatDuration, int32 PlayerCount)
{
    if (!BaronData.BaronActor.Get())
    {
        return;
    }
    
    // Aumentar agressividade baseado na duração do combate
    const float AggressionMultiplier = 1.0f + (CombatDuration * 0.1f);
    
    // Ativar habilidades especiais baseado no número de jogadores
    if (PlayerCount >= 3 && CombatDuration > 30.0f)
    {
        // Ativar habilidade de área após 30 segundos com 3+ jogadores
        TriggerBaronAreaAbility();
    }
    
    UE_LOG(LogBaronAuracron, VeryVerbose, TEXT("Combat behavior updated: Duration=%.1fs, Players=%d, Aggression=%.2fx"), 
           CombatDuration, PlayerCount, AggressionMultiplier);
}

void ABaronAuracronManager::ExecuteSentinelAttack(int32 SentinelIndex, AActor* Target)
{
    if (SentinelIndex >= SentinelData.Num() || !Target)
    {
        return;
    }
    
    auto& Sentinel = SentinelData[SentinelIndex];
    if (!Sentinel.SentinelActor.Get())
    {
        return;
    }
    
    // Aplicar dano ao alvo
    if (APawn* TargetPawn = Cast<APawn>(Target))
    {
        if (UAbilitySystemComponent* ASC = TargetPawn->FindComponentByClass<UAbilitySystemComponent>())
        {
            // Aplicar efeito de dano da sentinela
            FGameplayEffectSpecHandle DamageSpec = ASC->MakeOutgoingSpec(
                SentinelDamageEffect,
                1.0f,
                ASC->MakeEffectContext()
            );
            
            if (DamageSpec.IsValid())
            {
                const float SentinelDamage = 150.0f; // Dano base da sentinela
                DamageSpec.Data->SetSetByCallerMagnitude(
                    FGameplayTag::RequestGameplayTag(FName("Damage.Sentinel")),
                    SentinelDamage
                );
                ASC->ApplyGameplayEffectSpecToSelf(*DamageSpec.Data.Get());
            }
        }
    }
    
    // Efeito visual do ataque
    SpawnSentinelAttackEffect(Sentinel.SentinelActor->GetActorLocation(), Target->GetActorLocation());
}

void ABaronAuracronManager::ExecuteGuardianAttack(int32 GuardianIndex, AActor* Target)
{
    if (GuardianIndex >= GuardianData.Num() || !Target)
    {
        return;
    }
    
    auto& Guardian = GuardianData[GuardianIndex];
    if (!Guardian.GuardianActor.Get())
    {
        return;
    }
    
    // Aplicar dano mais forte que sentinelas
    if (APawn* TargetPawn = Cast<APawn>(Target))
    {
        if (UAbilitySystemComponent* ASC = TargetPawn->FindComponentByClass<UAbilitySystemComponent>())
        {
            FGameplayEffectSpecHandle DamageSpec = ASC->MakeOutgoingSpec(
                GuardianDamageEffect,
                1.0f,
                ASC->MakeEffectContext()
            );
            
            if (DamageSpec.IsValid())
            {
                const float GuardianDamage = 250.0f; // Dano maior que sentinelas
                DamageSpec.Data->SetSetByCallerMagnitude(
                    FGameplayTag::RequestGameplayTag(FName("Damage.Guardian")),
                    GuardianDamage
                );
                ASC->ApplyGameplayEffectSpecToSelf(*DamageSpec.Data.Get());
            }
        }
    }
    
    // Efeito visual do ataque do guardião
    SpawnGuardianAttackEffect(Guardian.GuardianActor->GetActorLocation(), Target->GetActorLocation());
}

void ABaronAuracronManager::UpdateSentinelPositionsForRotation(float NewRotation)
{
    const float RadianRotation = FMath::DegreesToRadians(NewRotation);
    
    for (int32 i = 0; i < SentinelData.Num(); ++i)
    {
        if (SentinelData[i].SentinelActor.Get())
        {
            // Calcular nova posição baseada na rotação
            const FVector OriginalOffset = SentinelData[i].OriginalPosition - GetActorLocation();
            const FVector RotatedOffset = OriginalOffset.RotateAngleAxis(NewRotation, FVector::UpVector);
            const FVector NewPosition = GetActorLocation() + RotatedOffset;
            
            SentinelData[i].SentinelActor->SetActorLocation(NewPosition);
        }
    }
}

int32 ABaronAuracronManager::GetPlayerTeamID(APlayerController* PlayerController) const
{
    if (!PlayerController)
    {
        return -1;
    }
    
    // Implementação simplificada - assumindo que existe uma interface ou componente de team
    // Em um projeto real, isso seria obtido do sistema de teams do jogo
    if (APawn* PlayerPawn = PlayerController->GetPawn())
    {
        // Buscar componente de team ou usar PlayerState
        if (APlayerState* PS = PlayerController->GetPlayerState<APlayerState>())
        {
            // Assumindo que PlayerState tem informação de team
            // Implementação robusta para obter Team ID usando reflection
            UFunction* GetTeamIdFunc = PS->GetClass()->FindFunctionByName(TEXT("GetTeamId"));
            if (GetTeamIdFunc)
            {
                int32 TeamId = 0;
                PS->ProcessEvent(GetTeamIdFunc, &TeamId);
                return TeamId;
            }

            // Fallback: usar propriedade de team se existir (UE 5.6 API)
            FProperty* TeamProperty = PS->GetClass()->FindPropertyByName(TEXT("TeamId"));
            if (!TeamProperty)
            {
                TeamProperty = PS->GetClass()->FindPropertyByName(TEXT("Team"));
            }

            if (TeamProperty)
            {
                if (FIntProperty* IntProp = CastField<FIntProperty>(TeamProperty))
                {
                    return IntProp->GetPropertyValue_InContainer(PS);
                }
                else if (FByteProperty* ByteProp = CastField<FByteProperty>(TeamProperty))
                {
                    return ByteProp->GetPropertyValue_InContainer(PS);
                }
            }

            // Fallback final: usar PlayerController para determinar team
            if (APlayerController* PC = Cast<APlayerController>(PS->GetOwner()))
            {
                // Team baseado no ID do player (par = team 1, ímpar = team 2)
                return (PC->GetUniqueID() % 2) + 1;
            }
        }
    }
    
    return 1; // Fallback para team 1
}

void ABaronAuracronManager::TriggerBaronAreaAbility()
{
    if (!BaronData.BaronActor.Get())
    {
        return;
    }
    
    // Implementar habilidade de área do Baron
    const FVector BaronLocation = BaronData.BaronActor->GetActorLocation();
    const float AreaRadius = 800.0f;
    
    // Encontrar todos os jogadores na área
    TArray<AActor*> PlayersInArea;
    UKismetSystemLibrary::SphereOverlapActors(
        GetWorld(),
        BaronLocation,
        AreaRadius,
        TArray<TEnumAsByte<EObjectTypeQuery>>(),
        APawn::StaticClass(),
        TArray<AActor*>(),
        PlayersInArea
    );
    
    // Aplicar dano de área
    for (AActor* Player : PlayersInArea)
    {
        if (APawn* PlayerPawn = Cast<APawn>(Player))
        {
            if (UAbilitySystemComponent* ASC = PlayerPawn->FindComponentByClass<UAbilitySystemComponent>())
            {
                // Aplicar efeito de dano de área
                FGameplayEffectSpecHandle AreaDamageSpec = ASC->MakeOutgoingSpec(
                    BaronAreaDamageEffect,
                    1.0f,
                    ASC->MakeEffectContext()
                );
                
                if (AreaDamageSpec.IsValid())
                {
                    const float AreaDamage = 300.0f;
                    AreaDamageSpec.Data->SetSetByCallerMagnitude(
                        FGameplayTag::RequestGameplayTag(FName("Damage.Baron.Area")),
                        AreaDamage
                    );
                    ASC->ApplyGameplayEffectSpecToSelf(*AreaDamageSpec.Data.Get());
                }
            }
        }
    }
    
    // Efeito visual da habilidade de área
    SpawnBaronAreaEffect(BaronLocation, AreaRadius);
    
    UE_LOG(LogBaronAuracron, Log, TEXT("Baron triggered area ability affecting %d players"), PlayersInArea.Num());
}

void ABaronAuracronManager::SpawnRewardEffect(const FVector& Location)
{
    // Implementar efeito visual de recompensa
    if (RewardEffectClass)
    {
        GetWorld()->SpawnActor<AActor>(RewardEffectClass, Location, FRotator::ZeroRotator);
    }
}

void ABaronAuracronManager::SpawnSentinelAttackEffect(const FVector& From, const FVector& To)
{
    // Implementar efeito visual do ataque da sentinela
    if (SentinelAttackEffectClass)
    {
        FVector MidPoint = (From + To) * 0.5f;
        GetWorld()->SpawnActor<AActor>(SentinelAttackEffectClass, MidPoint, FRotator::ZeroRotator);
    }
}

void ABaronAuracronManager::SpawnGuardianAttackEffect(const FVector& From, const FVector& To)
{
    // Implementar efeito visual do ataque do guardião
    if (GuardianAttackEffectClass)
    {
        FVector MidPoint = (From + To) * 0.5f;
        GetWorld()->SpawnActor<AActor>(GuardianAttackEffectClass, MidPoint, FRotator::ZeroRotator);
    }
}

void ABaronAuracronManager::SpawnBaronAreaEffect(const FVector& Center, float Radius)
{
    // Implementar efeito visual da habilidade de área do Baron
    if (BaronAreaEffectClass)
    {
        AActor* AreaEffect = GetWorld()->SpawnActor<AActor>(BaronAreaEffectClass, Center, FRotator::ZeroRotator);
        if (AreaEffect)
        {
            AreaEffect->SetActorScale3D(FVector(Radius / 100.0f)); // Escalar baseado no raio
        }
    }
}