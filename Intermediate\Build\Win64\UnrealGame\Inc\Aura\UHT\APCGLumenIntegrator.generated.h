// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "APCGLumenIntegrator.h"

#ifdef AURA_APCGLumenIntegrator_generated_h
#error "APCGLumenIntegrator.generated.h already included, missing '#pragma once' in APCGLumenIntegrator.h"
#endif
#define AURA_APCGLumenIntegrator_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APCGNaniteOptimizer;
class APCGWorldPartitionManager;
class UActorComponent;
class ULightComponent;
class UStaticMeshComponent;
enum class EPCGLumenQuality : uint8;
enum class EPCGLumenReflectionMode : uint8;
enum class EPCGLumenUpdateMode : uint8;
struct FLinearColor;
struct FPCGLumenLightData;
struct FPCGLumenPerformanceStats;
struct FPCGLumenSurfaceData;

// ********** Begin ScriptStruct FPCGLumenConfig ***************************************************
#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_91_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGLumenConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGLumenConfig;
// ********** End ScriptStruct FPCGLumenConfig *****************************************************

// ********** Begin ScriptStruct FPCGLumenLightData ************************************************
#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_141_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGLumenLightData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGLumenLightData;
// ********** End ScriptStruct FPCGLumenLightData **************************************************

// ********** Begin ScriptStruct FPCGLumenSurfaceData **********************************************
#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_187_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGLumenSurfaceData;
// ********** End ScriptStruct FPCGLumenSurfaceData ************************************************

// ********** Begin ScriptStruct FPCGLumenPerformanceStats *****************************************
#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_229_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGLumenPerformanceStats;
// ********** End ScriptStruct FPCGLumenPerformanceStats *******************************************

// ********** Begin Class APCGLumenIntegrator ******************************************************
#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_276_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnMeshComponentDestroyed); \
	DECLARE_FUNCTION(execOnLightComponentDestroyed); \
	DECLARE_FUNCTION(execSynchronizeWithPCGSystem); \
	DECLARE_FUNCTION(execIntegrateWithWorldPartition); \
	DECLARE_FUNCTION(execIntegrateWithNanite); \
	DECLARE_FUNCTION(execSetAdaptiveQuality); \
	DECLARE_FUNCTION(execOptimizeForPerformance); \
	DECLARE_FUNCTION(execGetPerformanceStats); \
	DECLARE_FUNCTION(execSetMaxReflectionBounces); \
	DECLARE_FUNCTION(execUpdateReflectionCaptures); \
	DECLARE_FUNCTION(execSetReflectionMode); \
	DECLARE_FUNCTION(execForceGlobalIlluminationUpdate); \
	DECLARE_FUNCTION(execSetUpdateMode); \
	DECLARE_FUNCTION(execSetLumenQuality); \
	DECLARE_FUNCTION(execSetReflectionIntensity); \
	DECLARE_FUNCTION(execSetGlobalIlluminationIntensity); \
	DECLARE_FUNCTION(execGetActiveSurfaces); \
	DECLARE_FUNCTION(execSetEmissiveSurface); \
	DECLARE_FUNCTION(execUpdateSurfaceData); \
	DECLARE_FUNCTION(execUnregisterSurface); \
	DECLARE_FUNCTION(execRegisterSurface); \
	DECLARE_FUNCTION(execGetActiveLights); \
	DECLARE_FUNCTION(execSetGlobalLightIntensity); \
	DECLARE_FUNCTION(execUpdateLightData); \
	DECLARE_FUNCTION(execUnregisterLight); \
	DECLARE_FUNCTION(execRegisterLight);


AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister();

#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_276_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPCGLumenIntegrator(); \
	friend struct Z_Construct_UClass_APCGLumenIntegrator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister(); \
public: \
	DECLARE_CLASS2(APCGLumenIntegrator, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_APCGLumenIntegrator_NoRegister) \
	DECLARE_SERIALIZER(APCGLumenIntegrator)


#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_276_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	APCGLumenIntegrator(APCGLumenIntegrator&&) = delete; \
	APCGLumenIntegrator(const APCGLumenIntegrator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APCGLumenIntegrator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APCGLumenIntegrator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APCGLumenIntegrator) \
	NO_API virtual ~APCGLumenIntegrator();


#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_273_PROLOG
#define FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_276_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_276_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_276_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h_276_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class APCGLumenIntegrator;

// ********** End Class APCGLumenIntegrator ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h

// ********** Begin Enum EPCGLumenQuality **********************************************************
#define FOREACH_ENUM_EPCGLUMENQUALITY(op) \
	op(EPCGLumenQuality::Low) \
	op(EPCGLumenQuality::Medium) \
	op(EPCGLumenQuality::High) \
	op(EPCGLumenQuality::Epic) \
	op(EPCGLumenQuality::Cinematic) 

enum class EPCGLumenQuality : uint8;
template<> struct TIsUEnumClass<EPCGLumenQuality> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGLumenQuality>();
// ********** End Enum EPCGLumenQuality ************************************************************

// ********** Begin Enum EPCGLumenLightType ********************************************************
#define FOREACH_ENUM_EPCGLUMENLIGHTTYPE(op) \
	op(EPCGLumenLightType::Directional) \
	op(EPCGLumenLightType::Point) \
	op(EPCGLumenLightType::Spot) \
	op(EPCGLumenLightType::Sky) \
	op(EPCGLumenLightType::Area) \
	op(EPCGLumenLightType::Emissive) 

enum class EPCGLumenLightType : uint8;
template<> struct TIsUEnumClass<EPCGLumenLightType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGLumenLightType>();
// ********** End Enum EPCGLumenLightType **********************************************************

// ********** Begin Enum EPCGLumenUpdateMode *******************************************************
#define FOREACH_ENUM_EPCGLUMENUPDATEMODE(op) \
	op(EPCGLumenUpdateMode::Static) \
	op(EPCGLumenUpdateMode::Dynamic) \
	op(EPCGLumenUpdateMode::Adaptive) \
	op(EPCGLumenUpdateMode::OnDemand) 

enum class EPCGLumenUpdateMode : uint8;
template<> struct TIsUEnumClass<EPCGLumenUpdateMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGLumenUpdateMode>();
// ********** End Enum EPCGLumenUpdateMode *********************************************************

// ********** Begin Enum EPCGLumenReflectionMode ***************************************************
#define FOREACH_ENUM_EPCGLUMENREFLECTIONMODE(op) \
	op(EPCGLumenReflectionMode::ScreenSpace) \
	op(EPCGLumenReflectionMode::RayTraced) \
	op(EPCGLumenReflectionMode::Hybrid) \
	op(EPCGLumenReflectionMode::Disabled) 

enum class EPCGLumenReflectionMode : uint8;
template<> struct TIsUEnumClass<EPCGLumenReflectionMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGLumenReflectionMode>();
// ********** End Enum EPCGLumenReflectionMode *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
