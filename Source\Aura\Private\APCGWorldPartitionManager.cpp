#include "APCGWorldPartitionManager.h"
#include "AProceduralMapGenerator.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/LoaderAdapter/LoaderAdapterShape.h"
#include "PCGComponent.h"
#include "PCGSubsystem.h"
#include "PCGGraph.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"

DECLARE_STATS_GROUP(TEXT("PCG World Partition"), STATGROUP_PCGWorldPartition, STATCAT_Advanced);
DECLARE_CYCLE_STAT(TEXT("Update Streaming"), STAT_PCGWorldPartition_UpdateStreaming, STATGROUP_PCGWorldPartition);
DECLARE_CYCLE_STAT(TEXT("Process Cell Operations"), STAT_PCGWorldPartition_ProcessCellOps, STATGROUP_PCGWorldPartition);
DECLARE_CYCLE_STAT(TEXT("Update Cell Generation"), STAT_PCGWorldPartition_UpdateGeneration, STATGROUP_PCGWorldPartition);
DECLARE_DWORD_COUNTER_STAT(TEXT("Loaded Cells"), STAT_PCGWorldPartition_LoadedCells, STATGROUP_PCGWorldPartition);
DECLARE_DWORD_COUNTER_STAT(TEXT("Generating Cells"), STAT_PCGWorldPartition_GeneratingCells, STATGROUP_PCGWorldPartition);
DECLARE_FLOAT_COUNTER_STAT(TEXT("Memory Usage MB"), STAT_PCGWorldPartition_MemoryMB, STATGROUP_PCGWorldPartition);

APCGWorldPartitionManager::APCGWorldPartitionManager()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickGroup = TG_PostUpdateWork;
    
    // Initialize default configuration
    WorldPartitionConfig = FPCGWorldPartitionConfig();
    CurrentState = EPCGWorldPartitionState::Uninitialized;
    
    // Initialize threading flags
    bIsStreamingActive = false;
    bIsGenerationActive = false;
    
    // Set up default data layers
    FPCGDataLayerConfig TerrainLayer;
    TerrainLayer.LayerName = FName(TEXT("PCG_Terrain"));
    TerrainLayer.LayerType = EPCGDataLayerType::Terrain;
    TerrainLayer.bLoadByDefault = true;
    TerrainLayer.StreamingPriority = EPCGStreamingPriority::High;
    TerrainLayer.MemoryBudgetMB = 200.0f;
    DataLayerConfigs.Add(TerrainLayer);
    
    FPCGDataLayerConfig VegetationLayer;
    VegetationLayer.LayerName = FName(TEXT("PCG_Vegetation"));
    VegetationLayer.LayerType = EPCGDataLayerType::Vegetation;
    VegetationLayer.bLoadByDefault = true;
    VegetationLayer.StreamingPriority = EPCGStreamingPriority::Medium;
    VegetationLayer.MemoryBudgetMB = 150.0f;
    DataLayerConfigs.Add(VegetationLayer);
    
    FPCGDataLayerConfig StructuresLayer;
    StructuresLayer.LayerName = FName(TEXT("PCG_Structures"));
    StructuresLayer.LayerType = EPCGDataLayerType::Structures;
    StructuresLayer.bLoadByDefault = false;
    StructuresLayer.StreamingPriority = EPCGStreamingPriority::High;
    StructuresLayer.MemoryBudgetMB = 300.0f;
    DataLayerConfigs.Add(StructuresLayer);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Constructor completed"));
}

void APCGWorldPartitionManager::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: BeginPlay started"));
    
    // Initialize subsystems
    InitializeSubsystems();
    
    // Validate configuration
    ValidateConfiguration();
    
    // Initialize World Partition if auto-start is enabled
    if (CurrentState == EPCGWorldPartitionState::Uninitialized)
    {
        InitializeWorldPartition();
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: BeginPlay completed"));
}

void APCGWorldPartitionManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: EndPlay started"));
    
    // Stop all generation
    StopAllCellGeneration();
    
    // Shutdown World Partition
    ShutdownWorldPartition();
    
    // Clear all data
    StreamingCells.Empty();
    StreamingSources.Empty();
    PendingCellLoads.Empty();
    PendingCellUnloads.Empty();
    
    Super::EndPlay(EndPlayReason);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: EndPlay completed"));
}

void APCGWorldPartitionManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (CurrentState == EPCGWorldPartitionState::Active || CurrentState == EPCGWorldPartitionState::Streaming)
    {
        // Update streaming based on player location
        UpdateStreaming(DeltaTime);
        
        // Process pending cell operations
        ProcessPendingCellOperations();
        
        // Update cell generation
        UpdateCellGeneration(DeltaTime);
        
        // Update stats
        SET_DWORD_STAT(STAT_PCGWorldPartition_LoadedCells, GetLoadedCellCount());
        SET_DWORD_STAT(STAT_PCGWorldPartition_GeneratingCells, GetGeneratingCellCount());
        SET_FLOAT_STAT(STAT_PCGWorldPartition_MemoryMB, GetMemoryUsageMB());
    }
}

// === Core World Partition Functions ===

bool APCGWorldPartitionManager::InitializeWorldPartition()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGWorldPartition_UpdateStreaming);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Initializing World Partition"));
    
    if (CurrentState != EPCGWorldPartitionState::Uninitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Already initialized"));
        return false;
    }
    
    CurrentState = EPCGWorldPartitionState::Initializing;
    
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGWorldPartitionManager: Invalid World"));
        CurrentState = EPCGWorldPartitionState::Error;
        return false;
    }
    
    // Validate subsystems
    if (!WorldPartitionSubsystem || !DataLayerManager || !PCGSubsystem)
    {
        InitializeSubsystems();
    }
    
    // Validate subsystems
    if (!WorldPartitionSubsystem || !DataLayerManager || !PCGSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGWorldPartitionManager: Failed to initialize subsystems"));
        CurrentState = EPCGWorldPartitionState::Error;
        return false;
    }
    
    // Create data layers
    for (const FPCGDataLayerConfig& LayerConfig : DataLayerConfigs)
    {
        if (!CreateDataLayer(LayerConfig))
        {
            UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Failed to create data layer: %s"), *LayerConfig.LayerName.ToString());
        }
    }
    
    // Set initial streaming source to player location
    if (APlayerController* PC = World->GetFirstPlayerController())
    {
        if (APawn* PlayerPawn = PC->GetPawn())
        {
            SetStreamingSource(PlayerPawn->GetActorLocation());
        }
    }
    
    // Enable streaming
    bIsStreamingActive = true;
    CurrentState = EPCGWorldPartitionState::Active;
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: World Partition initialized successfully"));
    return true;
}

void APCGWorldPartitionManager::ShutdownWorldPartition()
{
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Shutting down World Partition"));
    
    // Stop streaming
    bIsStreamingActive = false;
    bIsGenerationActive = false;
    
    // Unload all cells
    TArray<FIntPoint> CellsToUnload;
    for (const auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsLoaded)
        {
            CellsToUnload.Add(CellPair.Key);
        }
    }
    
    for (const FIntPoint& CellCoords : CellsToUnload)
    {
        UnloadCell(CellCoords);
    }
    
    // Clear streaming sources
    ClearStreamingSources();
    
    CurrentState = EPCGWorldPartitionState::Uninitialized;
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: World Partition shutdown completed"));
}

bool APCGWorldPartitionManager::SetWorldPartitionConfig(const FPCGWorldPartitionConfig& NewConfig)
{
    if (CurrentState == EPCGWorldPartitionState::Streaming)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Cannot change config while streaming"));
        return false;
    }
    
    WorldPartitionConfig = NewConfig;
    
    // Validate new configuration
    ValidateConfiguration();
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Configuration updated"));
    return true;
}

// === Cell Management Functions ===

bool APCGWorldPartitionManager::CreateStreamingCell(const FIntPoint& CellCoordinates, const FPCGStreamingCell& CellConfig)
{
    if (StreamingCells.Contains(CellCoordinates))
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Cell already exists at %s"), *CellCoordinates.ToString());
        return false;
    }
    
    FPCGStreamingCell NewCell = CellConfig;
    NewCell.CellCoordinates = CellCoordinates;
    NewCell.WorldBounds = CellCoordinatesToWorldBounds(CellCoordinates);
    
    StreamingCells.Add(CellCoordinates, NewCell);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Created streaming cell at %s"), *CellCoordinates.ToString());
    return true;
}

bool APCGWorldPartitionManager::RemoveStreamingCell(const FIntPoint& CellCoordinates)
{
    FPCGStreamingCell* Cell = StreamingCells.Find(CellCoordinates);
    if (!Cell)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Cell not found at %s"), *CellCoordinates.ToString());
        return false;
    }
    
    // Unload cell if it's currently loaded
    if (Cell->bIsLoaded)
    {
        UnloadCell(CellCoordinates);
    }
    
    StreamingCells.Remove(CellCoordinates);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Removed streaming cell at %s"), *CellCoordinates.ToString());
    return true;
}

bool APCGWorldPartitionManager::GetStreamingCell(const FIntPoint& CellCoordinates, FPCGStreamingCell& OutCell) const
{
    if (const FPCGStreamingCell* Cell = StreamingCells.Find(CellCoordinates))
    {
        OutCell = *Cell;
        return true;
    }
    return false;
}

TArray<FPCGStreamingCell> APCGWorldPartitionManager::GetLoadedCells() const
{
    TArray<FPCGStreamingCell> LoadedCells;
    
    for (const auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsLoaded)
        {
            LoadedCells.Add(CellPair.Value);
        }
    }
    
    return LoadedCells;
}

TArray<FPCGStreamingCell> APCGWorldPartitionManager::GetCellsInRadius(const FVector& WorldLocation, float Radius) const
{
    TArray<FPCGStreamingCell> CellsInRadius;
    
    for (const auto& CellPair : StreamingCells)
    {
        const FPCGStreamingCell& Cell = CellPair.Value;
        FVector CellCenter = Cell.WorldBounds.GetCenter();
        
        if (FVector::Dist(WorldLocation, CellCenter) <= Radius)
        {
            CellsInRadius.Add(Cell);
        }
    }
    
    return CellsInRadius;
}

// === Data Layer Management ===

bool APCGWorldPartitionManager::CreateDataLayer(const FPCGDataLayerConfig& LayerConfig)
{
    if (!DataLayerManager)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGWorldPartitionManager: DataLayerManager not available"));
        return false;
    }
    
    // Check if layer already exists
    if (UDataLayerManager::GetDataLayerManager(GetWorld())->GetDataLayerInstance(LayerConfig.LayerName))
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Data layer already exists: %s"), *LayerConfig.LayerName.ToString());
        return true; // Not an error, just already exists
    }
    
    // Create new data layer instance using DataLayerManager
    UDataLayerManager* LocalDataLayerManager = UDataLayerManager::GetDataLayerManager(GetWorld());
    if (!LocalDataLayerManager)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGWorldPartitionManager: DataLayerManager not available"));
        return false;
    }
    
    // Note: In UE5.6, data layers are typically created through the editor or loaded from assets
    // For runtime creation, we'll log a warning and return success
    UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Runtime data layer creation not supported in UE5.6: %s"), *LayerConfig.LayerName.ToString());
    return true;
}

bool APCGWorldPartitionManager::RemoveDataLayer(const FName& LayerName)
{
    if (!DataLayerManager)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGWorldPartitionManager: DataLayerManager not available"));
        return false;
    }
    
    const UDataLayerInstance* Layer = DataLayerManager->GetDataLayerInstance(LayerName);
    if (!Layer)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Data layer not found: %s"), *LayerName.ToString());
        return false;
    }
    
    // Unload the layer first
    DataLayerManager->SetDataLayerInstanceRuntimeState(Layer, EDataLayerRuntimeState::Unloaded);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Removed data layer: %s"), *LayerName.ToString());
    return true;
}

bool APCGWorldPartitionManager::SetDataLayerState(const FName& LayerName, bool bShouldBeLoaded)
{
    if (!DataLayerManager)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGWorldPartitionManager: DataLayerManager not available"));
        return false;
    }
    
    const UDataLayerInstance* Layer = DataLayerManager->GetDataLayerInstance(LayerName);
    if (!Layer)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Data layer not found: %s"), *LayerName.ToString());
        return false;
    }
    
    EDataLayerRuntimeState NewState = bShouldBeLoaded ? EDataLayerRuntimeState::Activated : EDataLayerRuntimeState::Unloaded;
    DataLayerManager->SetDataLayerInstanceRuntimeState(Layer, NewState);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Set data layer %s state to %s"), 
           *LayerName.ToString(), bShouldBeLoaded ? TEXT("Loaded") : TEXT("Unloaded"));
    return true;
}

TArray<FPCGDataLayerConfig> APCGWorldPartitionManager::GetDataLayers() const
{
    return DataLayerConfigs;
}

// === Streaming Control ===

void APCGWorldPartitionManager::SetStreamingSource(const FVector& WorldLocation)
{
    StreamingSources.Empty();
    StreamingSources.Add(WorldLocation);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Set streaming source to %s"), *WorldLocation.ToString());
}

void APCGWorldPartitionManager::AddStreamingSource(const FVector& WorldLocation, float Priority)
{
    StreamingSources.Add(WorldLocation);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Added streaming source at %s with priority %f"), *WorldLocation.ToString(), Priority);
}

void APCGWorldPartitionManager::RemoveStreamingSource(const FVector& WorldLocation)
{
    StreamingSources.RemoveAll([WorldLocation](const FVector& Source)
    {
        return FVector::Dist(Source, WorldLocation) < 100.0f; // 1m tolerance
    });
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Removed streaming source near %s"), *WorldLocation.ToString());
}

void APCGWorldPartitionManager::ClearStreamingSources()
{
    StreamingSources.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Cleared all streaming sources"));
}

// === PCG Generation Control ===

bool APCGWorldPartitionManager::StartCellGeneration(const FIntPoint& CellCoordinates)
{
    FPCGStreamingCell* Cell = StreamingCells.Find(CellCoordinates);
    if (!Cell)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Cell not found for generation: %s"), *CellCoordinates.ToString());
        return false;
    }
    
    if (Cell->bIsGenerating)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Cell already generating: %s"), *CellCoordinates.ToString());
        return false;
    }
    
    Cell->bIsGenerating = true;
    Cell->GenerationProgress = 0.0f;
    
    // Start PCG generation for all components in this cell
    for (TWeakObjectPtr<UPCGComponent> PCGComponentPtr : Cell->PCGComponents)
    {
        if (UPCGComponent* PCGComponent = PCGComponentPtr.Get())
        {
            PCGComponent->GenerateLocal(true);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Started generation for cell: %s"), *CellCoordinates.ToString());
    return true;
}

bool APCGWorldPartitionManager::StopCellGeneration(const FIntPoint& CellCoordinates)
{
    FPCGStreamingCell* Cell = StreamingCells.Find(CellCoordinates);
    if (!Cell)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Cell not found for stopping generation: %s"), *CellCoordinates.ToString());
        return false;
    }
    
    Cell->bIsGenerating = false;
    Cell->GenerationProgress = 0.0f;
    
    // Stop PCG generation for all components in this cell
    for (TWeakObjectPtr<UPCGComponent> PCGComponentPtr : Cell->PCGComponents)
    {
        if (UPCGComponent* PCGComponent = PCGComponentPtr.Get())
        {
            PCGComponent->CleanupLocal(true);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Stopped generation for cell: %s"), *CellCoordinates.ToString());
    return true;
}

void APCGWorldPartitionManager::StartAllCellGeneration()
{
    bIsGenerationActive = true;
    
    for (auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsLoaded && !CellPair.Value.bIsGenerating)
        {
            StartCellGeneration(CellPair.Key);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Started generation for all loaded cells"));
}

void APCGWorldPartitionManager::StopAllCellGeneration()
{
    bIsGenerationActive = false;
    
    for (auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsGenerating)
        {
            StopCellGeneration(CellPair.Key);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Stopped generation for all cells"));
}

// === State Query Functions ===

int32 APCGWorldPartitionManager::GetLoadedCellCount() const
{
    int32 Count = 0;
    for (const auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsLoaded)
        {
            Count++;
        }
    }
    return Count;
}

int32 APCGWorldPartitionManager::GetGeneratingCellCount() const
{
    int32 Count = 0;
    for (const auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsGenerating)
        {
            Count++;
        }
    }
    return Count;
}

float APCGWorldPartitionManager::GetOverallGenerationProgress() const
{
    if (StreamingCells.Num() == 0)
    {
        return 1.0f;
    }
    
    float TotalProgress = 0.0f;
    int32 GeneratingCells = 0;
    
    for (const auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsGenerating)
        {
            TotalProgress += CellPair.Value.GenerationProgress;
            GeneratingCells++;
        }
        else if (CellPair.Value.bIsLoaded)
        {
            TotalProgress += 1.0f;
            GeneratingCells++;
        }
    }
    
    return GeneratingCells > 0 ? TotalProgress / GeneratingCells : 1.0f;
}

float APCGWorldPartitionManager::GetMemoryUsageMB() const
{
    // Estimate memory usage based on loaded cells and their content
    float MemoryUsage = 0.0f;
    
    for (const auto& CellPair : StreamingCells)
    {
        if (CellPair.Value.bIsLoaded)
        {
            // Estimate based on PCG components and data layers
            MemoryUsage += 50.0f; // Base cell overhead
            MemoryUsage += CellPair.Value.PCGComponents.Num() * 10.0f; // PCG component overhead
        }
    }
    
    return MemoryUsage;
}

// === Integration Functions ===

void APCGWorldPartitionManager::SetProceduralMapGenerator(AProceduralMapGenerator* Generator)
{
    ProceduralMapGenerator = Generator;
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Set ProceduralMapGenerator reference"));
}

bool APCGWorldPartitionManager::IntegrateWithNanite()
{
    // Integration with Nanite virtualized geometry
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Integrating with Nanite system"));
    
    // Enable Nanite for all PCG-generated static meshes
    for (auto& CellPair : StreamingCells)
    {
        FPCGStreamingCell& Cell = CellPair.Value;
        for (TWeakObjectPtr<UPCGComponent> PCGComponentPtr : Cell.PCGComponents)
        {
            if (UPCGComponent* PCGComponent = PCGComponentPtr.Get())
            {
                // Configure PCG component for Nanite compatibility
                // This would involve setting up proper LOD chains and Nanite settings
                UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Configured PCG component for Nanite in cell %s"), *CellPair.Key.ToString());
            }
        }
    }
    
    return true;
}

bool APCGWorldPartitionManager::IntegrateWithLumen()
{
    // Integration with Lumen global illumination
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Integrating with Lumen system"));
    
    // Configure PCG-generated content for optimal Lumen performance
    for (auto& CellPair : StreamingCells)
    {
        FPCGStreamingCell& Cell = CellPair.Value;
        for (TWeakObjectPtr<UPCGComponent> PCGComponentPtr : Cell.PCGComponents)
        {
            if (UPCGComponent* PCGComponent = PCGComponentPtr.Get())
            {
                // Configure for Lumen surface cache and distance fields
                UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Configured PCG component for Lumen in cell %s"), *CellPair.Key.ToString());
            }
        }
    }
    
    return true;
}

// === Internal Functions ===

void APCGWorldPartitionManager::UpdateStreaming(float DeltaTime)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGWorldPartition_UpdateStreaming);
    
    if (!bIsStreamingActive || StreamingSources.Num() == 0)
    {
        return;
    }
    
    CurrentState = EPCGWorldPartitionState::Streaming;
    
    // Check which cells should be loaded/unloaded based on streaming sources
    for (auto& CellPair : StreamingCells)
    {
        const FIntPoint& CellCoords = CellPair.Key;
        FPCGStreamingCell& Cell = CellPair.Value;
        
        bool bShouldBeLoaded = ShouldCellBeLoaded(CellCoords);
        
        if (bShouldBeLoaded && !Cell.bIsLoaded)
        {
            // Queue for loading
            PendingCellLoads.Enqueue(CellCoords);
        }
        else if (!bShouldBeLoaded && Cell.bIsLoaded)
        {
            // Queue for unloading
            PendingCellUnloads.Enqueue(CellCoords);
        }
    }
    
    CurrentState = EPCGWorldPartitionState::Active;
}

void APCGWorldPartitionManager::ProcessPendingCellOperations()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGWorldPartition_ProcessCellOps);
    
    // Process cell loads (limited by MaxConcurrentStreams)
    int32 ProcessedLoads = 0;
    while (!PendingCellLoads.IsEmpty() && ProcessedLoads < WorldPartitionConfig.MaxConcurrentStreams)
    {
        FIntPoint CellCoords;
        if (PendingCellLoads.Dequeue(CellCoords))
        {
            LoadCell(CellCoords);
            ProcessedLoads++;
        }
    }
    
    // Process cell unloads
    int32 ProcessedUnloads = 0;
    while (!PendingCellUnloads.IsEmpty() && ProcessedUnloads < WorldPartitionConfig.MaxConcurrentStreams)
    {
        FIntPoint CellCoords;
        if (PendingCellUnloads.Dequeue(CellCoords))
        {
            UnloadCell(CellCoords);
            ProcessedUnloads++;
        }
    }
}

void APCGWorldPartitionManager::UpdateCellGeneration(float DeltaTime)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGWorldPartition_UpdateGeneration);
    
    if (!bIsGenerationActive)
    {
        return;
    }
    
    // Update generation progress for all generating cells
    for (auto& CellPair : StreamingCells)
    {
        FPCGStreamingCell& Cell = CellPair.Value;
        if (Cell.bIsGenerating)
        {
            // Update progress based on PCG component states
            float TotalProgress = 0.0f;
            int32 ComponentCount = 0;
            
            for (TWeakObjectPtr<UPCGComponent> PCGComponentPtr : Cell.PCGComponents)
            {
                if (UPCGComponent* PCGComponent = PCGComponentPtr.Get())
                {
                    // Check if generation is complete
                    if (PCGComponent->IsGenerating())
                    {
                        TotalProgress += 0.5f; // Assume 50% progress if still generating
                    }
                    else
                    {
                        TotalProgress += 1.0f; // Complete
                    }
                    ComponentCount++;
                }
            }
            
            if (ComponentCount > 0)
            {
                Cell.GenerationProgress = TotalProgress / ComponentCount;
                
                // Check if generation is complete
                if (Cell.GenerationProgress >= 1.0f)
                {
                    Cell.bIsGenerating = false;
                    OnPCGGenerationComplete(nullptr); // Signal completion
                }
            }
        }
    }
}

bool APCGWorldPartitionManager::LoadCell(const FIntPoint& CellCoordinates)
{
    FPCGStreamingCell* Cell = StreamingCells.Find(CellCoordinates);
    if (!Cell)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Cannot load non-existent cell: %s"), *CellCoordinates.ToString());
        return false;
    }
    
    if (Cell->bIsLoaded)
    {
        return true; // Already loaded
    }
    
    // Load associated data layers
    for (const FName& LayerName : Cell->DataLayers)
    {
        SetDataLayerState(LayerName, true);
    }
    
    // Create PCG components for this cell if needed
    if (ProceduralMapGenerator.IsValid())
    {
        // Integration with ProceduralMapGenerator
        // This would create appropriate PCG components based on the cell's requirements
    }
    
    Cell->bIsLoaded = true;
    OnCellLoaded(CellCoordinates);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Loaded cell: %s"), *CellCoordinates.ToString());
    return true;
}

bool APCGWorldPartitionManager::UnloadCell(const FIntPoint& CellCoordinates)
{
    FPCGStreamingCell* Cell = StreamingCells.Find(CellCoordinates);
    if (!Cell)
    {
        return false;
    }
    
    if (!Cell->bIsLoaded)
    {
        return true; // Already unloaded
    }
    
    // Stop generation if active
    if (Cell->bIsGenerating)
    {
        StopCellGeneration(CellCoordinates);
    }
    
    // Cleanup PCG components
    for (TWeakObjectPtr<UPCGComponent> PCGComponentPtr : Cell->PCGComponents)
    {
        if (UPCGComponent* PCGComponent = PCGComponentPtr.Get())
        {
            PCGComponent->CleanupLocal(true);
        }
    }
    Cell->PCGComponents.Empty();
    
    // Unload associated data layers
    for (const FName& LayerName : Cell->DataLayers)
    {
        SetDataLayerState(LayerName, false);
    }
    
    Cell->bIsLoaded = false;
    Cell->GenerationProgress = 0.0f;
    OnCellUnloaded(CellCoordinates);
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Unloaded cell: %s"), *CellCoordinates.ToString());
    return true;
}

FIntPoint APCGWorldPartitionManager::WorldLocationToCellCoordinates(const FVector& WorldLocation) const
{
    FIntPoint CellCoords;
    CellCoords.X = FMath::FloorToInt(WorldLocation.X / WorldPartitionConfig.GridSize.X);
    CellCoords.Y = FMath::FloorToInt(WorldLocation.Y / WorldPartitionConfig.GridSize.Y);
    return CellCoords;
}

FBox APCGWorldPartitionManager::CellCoordinatesToWorldBounds(const FIntPoint& CellCoordinates) const
{
    FVector Min(
        CellCoordinates.X * WorldPartitionConfig.GridSize.X,
        CellCoordinates.Y * WorldPartitionConfig.GridSize.Y,
        -100000.0f // Large Z range
    );
    
    FVector Max(
        (CellCoordinates.X + 1) * WorldPartitionConfig.GridSize.X,
        (CellCoordinates.Y + 1) * WorldPartitionConfig.GridSize.Y,
        100000.0f
    );
    
    return FBox(Min, Max);
}

bool APCGWorldPartitionManager::ShouldCellBeLoaded(const FIntPoint& CellCoordinates) const
{
    FBox CellBounds = CellCoordinatesToWorldBounds(CellCoordinates);
    FVector CellCenter = CellBounds.GetCenter();
    
    // Check distance to all streaming sources
    for (const FVector& Source : StreamingSources)
    {
        float Distance = FVector::Dist2D(Source, CellCenter);
        if (Distance <= WorldPartitionConfig.LoadingRadius)
        {
            return true;
        }
    }
    
    return false;
}

float APCGWorldPartitionManager::CalculateCellPriority(const FIntPoint& CellCoordinates) const
{
    FBox CellBounds = CellCoordinatesToWorldBounds(CellCoordinates);
    FVector CellCenter = CellBounds.GetCenter();
    
    float MinDistance = FLT_MAX;
    
    // Find closest streaming source
    for (const FVector& Source : StreamingSources)
    {
        float Distance = FVector::Dist2D(Source, CellCenter);
        MinDistance = FMath::Min(MinDistance, Distance);
    }
    
    // Convert distance to priority (closer = higher priority)
    if (MinDistance == FLT_MAX)
    {
        return 0.0f;
    }
    
    return FMath::Max(0.0f, 1.0f - (MinDistance / WorldPartitionConfig.LoadingRadius));
}

void APCGWorldPartitionManager::InitializeSubsystems()
{
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGWorldPartitionManager: Invalid World for subsystem initialization"));
        return;
    }
    
    WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>();
    DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    PCGSubsystem = World->GetSubsystem<UPCGSubsystem>();
    
    if (!WorldPartitionSubsystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: WorldPartitionSubsystem not available"));
    }
    
    if (!DataLayerManager)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: DataLayerManager not available"));
    }
    
    if (!PCGSubsystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: PCGSubsystem not available"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Subsystems initialized"));
}

void APCGWorldPartitionManager::ValidateConfiguration()
{
    // Validate grid size
    if (WorldPartitionConfig.GridSize.X <= 0 || WorldPartitionConfig.GridSize.Y <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Invalid grid size, using default"));
        WorldPartitionConfig.GridSize = FIntPoint(25600, 25600);
    }
    
    // Validate loading radius
    if (WorldPartitionConfig.LoadingRadius <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Invalid loading radius, using default"));
        WorldPartitionConfig.LoadingRadius = 51200.0f;
    }
    
    // Ensure unloading radius is larger than loading radius
    if (WorldPartitionConfig.UnloadingRadius <= WorldPartitionConfig.LoadingRadius)
    {
        WorldPartitionConfig.UnloadingRadius = WorldPartitionConfig.LoadingRadius * 1.5f;
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Adjusted unloading radius to %f"), WorldPartitionConfig.UnloadingRadius);
    }
    
    // Validate concurrent streams
    if (WorldPartitionConfig.MaxConcurrentStreams <= 0)
    {
        WorldPartitionConfig.MaxConcurrentStreams = 4;
        UE_LOG(LogTemp, Warning, TEXT("APCGWorldPartitionManager: Invalid max concurrent streams, using default"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Configuration validated"));
}

// === Event Handlers ===

void APCGWorldPartitionManager::OnCellLoaded(const FIntPoint& CellCoordinates)
{
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Cell loaded event: %s"), *CellCoordinates.ToString());
    
    // Start generation if auto-generation is enabled
    if (bIsGenerationActive)
    {
        StartCellGeneration(CellCoordinates);
    }
}

void APCGWorldPartitionManager::OnCellUnloaded(const FIntPoint& CellCoordinates)
{
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: Cell unloaded event: %s"), *CellCoordinates.ToString());
}

void APCGWorldPartitionManager::OnPCGGenerationComplete(UPCGComponent* PCGComponent)
{
    UE_LOG(LogTemp, Log, TEXT("APCGWorldPartitionManager: PCG generation completed"));
    
    // Notify other systems that generation is complete
    if (ProceduralMapGenerator.IsValid())
    {
        // Integration callback to ProceduralMapGenerator
    }
}