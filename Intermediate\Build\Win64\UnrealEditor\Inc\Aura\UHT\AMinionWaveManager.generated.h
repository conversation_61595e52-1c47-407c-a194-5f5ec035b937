// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AMinionWaveManager.h"

#ifdef AURA_AMinionWaveManager_generated_h
#error "AMinionWaveManager.generated.h already included, missing '#pragma once' in AMinionWaveManager.h"
#endif
#define AURA_AMinionWaveManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EFormationType : uint8;
enum class ELaneType : uint8;
enum class EMinionType : uint8;
enum class EPathfindingAlgorithm : uint8;
enum class EWaveType : uint8;
struct FMinionData;
struct FMinionFormation;
struct FMinionSpawnPoint;
struct FPathfindingNode;
struct FWaveData;

// ********** Begin ScriptStruct FMinionData *******************************************************
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_100_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMinionData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FMinionData;
// ********** End ScriptStruct FMinionData *********************************************************

// ********** Begin ScriptStruct FWaveData *********************************************************
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_185_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWaveData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FWaveData;
// ********** End ScriptStruct FWaveData ***********************************************************

// ********** Begin ScriptStruct FPathfindingNode **************************************************
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_253_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPathfindingNode_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FPathfindingNode;
// ********** End ScriptStruct FPathfindingNode ****************************************************

// ********** Begin ScriptStruct FPathfindingGrid **************************************************
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_309_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPathfindingGrid_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FPathfindingGrid;
// ********** End ScriptStruct FPathfindingGrid ****************************************************

// ********** Begin ScriptStruct FMinionFormation **************************************************
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_342_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMinionFormation_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FMinionFormation;
// ********** End ScriptStruct FMinionFormation ****************************************************

// ********** Begin ScriptStruct FMinionSpawnPoint *************************************************
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_379_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FMinionSpawnPoint;
// ********** End ScriptStruct FMinionSpawnPoint ***************************************************

// ********** Begin Class AMinionWaveManager *******************************************************
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_425_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetWaveScalingFactor); \
	DECLARE_FUNCTION(execSetGridCellSize); \
	DECLARE_FUNCTION(execSetPathfindingAlgorithm); \
	DECLARE_FUNCTION(execSetWaveInterval); \
	DECLARE_FUNCTION(execGetMinionsInRadius); \
	DECLARE_FUNCTION(execGetMinionsInLane); \
	DECLARE_FUNCTION(execGetWaveProgress); \
	DECLARE_FUNCTION(execGetCurrentWaveNumber); \
	DECLARE_FUNCTION(execGetActiveMinionCount); \
	DECLARE_FUNCTION(execLogMinionStatistics); \
	DECLARE_FUNCTION(execLogWaveStatistics); \
	DECLARE_FUNCTION(execDrawDebugSpawnPoints); \
	DECLARE_FUNCTION(execDrawDebugFormations); \
	DECLARE_FUNCTION(execDrawDebugMinionPaths); \
	DECLARE_FUNCTION(execDrawDebugPathfindingGrid); \
	DECLARE_FUNCTION(execUpdateMinionLOD); \
	DECLARE_FUNCTION(execCullDistantMinions); \
	DECLARE_FUNCTION(execOptimizePathfinding); \
	DECLARE_FUNCTION(execValidateMinionData); \
	DECLARE_FUNCTION(execValidatePathfindingGrid); \
	DECLARE_FUNCTION(execValidateWaveSystem); \
	DECLARE_FUNCTION(execUpdateSpawnPointCooldowns); \
	DECLARE_FUNCTION(execGetAvailableSpawnPoint); \
	DECLARE_FUNCTION(execRemoveSpawnPoint); \
	DECLARE_FUNCTION(execAddSpawnPoint); \
	DECLARE_FUNCTION(execInitializeSpawnPoints); \
	DECLARE_FUNCTION(execAssignMinionsToFormation); \
	DECLARE_FUNCTION(execUpdateFormationPositions); \
	DECLARE_FUNCTION(execCalculateBoxFormation); \
	DECLARE_FUNCTION(execCalculateWedgeFormation); \
	DECLARE_FUNCTION(execCalculateColumnFormation); \
	DECLARE_FUNCTION(execCalculateLineFormation); \
	DECLARE_FUNCTION(execCreateFormation); \
	DECLARE_FUNCTION(execExecuteMinionAttack); \
	DECLARE_FUNCTION(execCanMinionAttack); \
	DECLARE_FUNCTION(execFindNearestTarget); \
	DECLARE_FUNCTION(execUpdateMinionCombat); \
	DECLARE_FUNCTION(execUpdateMinionBehavior); \
	DECLARE_FUNCTION(execUpdateMinionMovement); \
	DECLARE_FUNCTION(execCalculateHeuristic); \
	DECLARE_FUNCTION(execGetNeighborNodeIndices); \
	DECLARE_FUNCTION(execGetNodeAtGridPosition); \
	DECLARE_FUNCTION(execGridToWorldPosition); \
	DECLARE_FUNCTION(execWorldToGridCoordinate); \
	DECLARE_FUNCTION(execIsPositionWalkable); \
	DECLARE_FUNCTION(execUpdateNavigationGrid); \
	DECLARE_FUNCTION(execFindPathDijkstra); \
	DECLARE_FUNCTION(execFindPathAStar); \
	DECLARE_FUNCTION(execFindPath); \
	DECLARE_FUNCTION(execInitializePathfindingGrid); \
	DECLARE_FUNCTION(execDestroyAllMinions); \
	DECLARE_FUNCTION(execDestroyMinion); \
	DECLARE_FUNCTION(execSpawnWaveMinions); \
	DECLARE_FUNCTION(execSpawnMinion); \
	DECLARE_FUNCTION(execSetupWaveFormation); \
	DECLARE_FUNCTION(execCalculateWaveScaling); \
	DECLARE_FUNCTION(execGenerateMinionComposition); \
	DECLARE_FUNCTION(execGenerateWaveData); \
	DECLARE_FUNCTION(execResetWaveSystem); \
	DECLARE_FUNCTION(execResumeWaveSystem); \
	DECLARE_FUNCTION(execPauseWaveSystem); \
	DECLARE_FUNCTION(execStopCurrentWave); \
	DECLARE_FUNCTION(execStartNextWave); \
	DECLARE_FUNCTION(execInitializeWaveSystem);


AURA_API UClass* Z_Construct_UClass_AMinionWaveManager_NoRegister();

#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_425_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAMinionWaveManager(); \
	friend struct Z_Construct_UClass_AMinionWaveManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_AMinionWaveManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AMinionWaveManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_AMinionWaveManager_NoRegister) \
	DECLARE_SERIALIZER(AMinionWaveManager)


#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_425_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AMinionWaveManager(AMinionWaveManager&&) = delete; \
	AMinionWaveManager(const AMinionWaveManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AMinionWaveManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AMinionWaveManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AMinionWaveManager) \
	NO_API virtual ~AMinionWaveManager();


#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_422_PROLOG
#define FID_Aura_Source_Aura_Public_AMinionWaveManager_h_425_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_AMinionWaveManager_h_425_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AMinionWaveManager_h_425_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AMinionWaveManager_h_425_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AMinionWaveManager;

// ********** End Class AMinionWaveManager *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_AMinionWaveManager_h

// ********** Begin Enum EMinionType ***************************************************************
#define FOREACH_ENUM_EMINIONTYPE(op) \
	op(EMinionType::Melee) \
	op(EMinionType::Ranged) \
	op(EMinionType::Caster) \
	op(EMinionType::Tank) \
	op(EMinionType::Support) \
	op(EMinionType::Siege) \
	op(EMinionType::Super) 

enum class EMinionType : uint8;
template<> struct TIsUEnumClass<EMinionType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EMinionType>();
// ********** End Enum EMinionType *****************************************************************

// ********** Begin Enum EWaveType *****************************************************************
#define FOREACH_ENUM_EWAVETYPE(op) \
	op(EWaveType::Normal) \
	op(EWaveType::Elite) \
	op(EWaveType::Boss) \
	op(EWaveType::Siege) \
	op(EWaveType::Cannon) \
	op(EWaveType::Super) 

enum class EWaveType : uint8;
template<> struct TIsUEnumClass<EWaveType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EWaveType>();
// ********** End Enum EWaveType *******************************************************************

// ********** Begin Enum EMinionState **************************************************************
#define FOREACH_ENUM_EMINIONSTATE(op) \
	op(EMinionState::Spawning) \
	op(EMinionState::Moving) \
	op(EMinionState::Fighting) \
	op(EMinionState::Retreating) \
	op(EMinionState::Dead) \
	op(EMinionState::Waiting) 

enum class EMinionState : uint8;
template<> struct TIsUEnumClass<EMinionState> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EMinionState>();
// ********** End Enum EMinionState ****************************************************************

// ********** Begin Enum ELaneType *****************************************************************
#define FOREACH_ENUM_ELANETYPE(op) \
	op(ELaneType::Top) \
	op(ELaneType::Middle) \
	op(ELaneType::Bottom) 

enum class ELaneType : uint8;
template<> struct TIsUEnumClass<ELaneType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ELaneType>();
// ********** End Enum ELaneType *******************************************************************

// ********** Begin Enum EFormationType ************************************************************
#define FOREACH_ENUM_EFORMATIONTYPE(op) \
	op(EFormationType::Line) \
	op(EFormationType::Column) \
	op(EFormationType::Wedge) \
	op(EFormationType::Box) \
	op(EFormationType::Scattered) 

enum class EFormationType : uint8;
template<> struct TIsUEnumClass<EFormationType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EFormationType>();
// ********** End Enum EFormationType **************************************************************

// ********** Begin Enum EPathfindingAlgorithm *****************************************************
#define FOREACH_ENUM_EPATHFINDINGALGORITHM(op) \
	op(EPathfindingAlgorithm::AStar) \
	op(EPathfindingAlgorithm::Dijkstra) \
	op(EPathfindingAlgorithm::BFS) \
	op(EPathfindingAlgorithm::DFS) \
	op(EPathfindingAlgorithm::JPS) 

enum class EPathfindingAlgorithm : uint8;
template<> struct TIsUEnumClass<EPathfindingAlgorithm> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPathfindingAlgorithm>();
// ********** End Enum EPathfindingAlgorithm *******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
