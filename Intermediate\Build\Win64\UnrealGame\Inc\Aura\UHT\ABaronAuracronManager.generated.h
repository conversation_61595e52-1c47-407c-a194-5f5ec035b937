// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "ABaronAuracronManager.h"

#ifdef AURA_ABaronAuracronManager_generated_h
#error "ABaronAuracronManager.generated.h already included, missing '#pragma once' in ABaronAuracronManager.h"
#endif
#define AURA_ABaronAuracronManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class AController;
enum class EBaronState : uint8;
enum class ESentinelType : uint8;
struct FDamageEvent;

// ********** Begin ScriptStruct FBaronData ********************************************************
#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_38_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBaronData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FBaronData;
// ********** End ScriptStruct FBaronData **********************************************************

// ********** Begin ScriptStruct FHexagonalArea ****************************************************
#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_80_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHexagonalArea_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FHexagonalArea;
// ********** End ScriptStruct FHexagonalArea ******************************************************

// ********** Begin ScriptStruct FSentinelData *****************************************************
#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_112_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSentinelData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FSentinelData;
// ********** End ScriptStruct FSentinelData *******************************************************

// ********** Begin ScriptStruct FBuffData *********************************************************
#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_154_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBuffData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FBuffData;
// ********** End ScriptStruct FBuffData ***********************************************************

// ********** Begin Class ABaronAuracronManager ****************************************************
#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_184_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetBaronState); \
	DECLARE_FUNCTION(execSetBaronState); \
	DECLARE_FUNCTION(execGetTimeUntilRespawn); \
	DECLARE_FUNCTION(execGetTimeUntilSpawn); \
	DECLARE_FUNCTION(execShouldSpawnBaron); \
	DECLARE_FUNCTION(execDrawDebugSentinels); \
	DECLARE_FUNCTION(execDrawDebugHexagon); \
	DECLARE_FUNCTION(execValidateHexagonalGeometry); \
	DECLARE_FUNCTION(execDistributeRewards); \
	DECLARE_FUNCTION(execGetPlayersInBuffRange); \
	DECLARE_FUNCTION(execRemoveTeamBuff); \
	DECLARE_FUNCTION(execApplyTeamBuff); \
	DECLARE_FUNCTION(execRespawnSentinel); \
	DECLARE_FUNCTION(execUpdateSentinelRotations); \
	DECLARE_FUNCTION(execSpawnGuardioesPortais); \
	DECLARE_FUNCTION(execSpawnSentinelasCristalinas); \
	DECLARE_FUNCTION(execInitializeSentinels); \
	DECLARE_FUNCTION(execOnBaronDeath); \
	DECLARE_FUNCTION(execTakeDamage); \
	DECLARE_FUNCTION(execEndCombat); \
	DECLARE_FUNCTION(execStartCombat); \
	DECLARE_FUNCTION(execPerformBaronRotation); \
	DECLARE_FUNCTION(execStartBaronRotation); \
	DECLARE_FUNCTION(execUpdateBaronHealth); \
	DECLARE_FUNCTION(execDespawnBaron); \
	DECLARE_FUNCTION(execSpawnBaron); \
	DECLARE_FUNCTION(execGetClosestEntrancePosition); \
	DECLARE_FUNCTION(execIsPositionInHexagon); \
	DECLARE_FUNCTION(execGetHexagonalEntrances); \
	DECLARE_FUNCTION(execCalculateHexagonalArea); \
	DECLARE_FUNCTION(execCalculateHexagonalVertices);


AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister();

#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_184_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesABaronAuracronManager(); \
	friend struct Z_Construct_UClass_ABaronAuracronManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister(); \
public: \
	DECLARE_CLASS2(ABaronAuracronManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_ABaronAuracronManager_NoRegister) \
	DECLARE_SERIALIZER(ABaronAuracronManager)


#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_184_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ABaronAuracronManager(ABaronAuracronManager&&) = delete; \
	ABaronAuracronManager(const ABaronAuracronManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ABaronAuracronManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ABaronAuracronManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ABaronAuracronManager) \
	NO_API virtual ~ABaronAuracronManager();


#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_181_PROLOG
#define FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_184_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_184_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_184_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ABaronAuracronManager_h_184_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ABaronAuracronManager;

// ********** End Class ABaronAuracronManager ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_ABaronAuracronManager_h

// ********** Begin Enum EBaronState ***************************************************************
#define FOREACH_ENUM_EBARONSTATE(op) \
	op(EBaronState::Dormant) \
	op(EBaronState::Spawning) \
	op(EBaronState::Active) \
	op(EBaronState::Combat) \
	op(EBaronState::Rotating) \
	op(EBaronState::Dead) 

enum class EBaronState : uint8;
template<> struct TIsUEnumClass<EBaronState> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EBaronState>();
// ********** End Enum EBaronState *****************************************************************

// ********** Begin Enum ESentinelType *************************************************************
#define FOREACH_ENUM_ESENTINELTYPE(op) \
	op(ESentinelType::Cristalina) \
	op(ESentinelType::Guardian) 

enum class ESentinelType : uint8;
template<> struct TIsUEnumClass<ESentinelType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ESentinelType>();
// ********** End Enum ESentinelType ***************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
