// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "APCGLumenIntegrator.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAPCGLumenIntegrator() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator();
AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGLumenLightType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGLumenQuality();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGLumenReflectionMode();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGLumenUpdateMode();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenLightData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenPerformanceStats();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenSurfaceData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_ULightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGLumenQuality **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGLumenQuality;
static UEnum* EPCGLumenQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGLumenQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGLumenQuality, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGLumenQuality"));
	}
	return Z_Registration_Info_UEnum_EPCGLumenQuality.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGLumenQuality>()
{
	return EPCGLumenQuality_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGLumenQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic Quality" },
		{ "Cinematic.Name", "EPCGLumenQuality::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums\n" },
#endif
		{ "Epic.DisplayName", "Epic Quality" },
		{ "Epic.Name", "EPCGLumenQuality::Epic" },
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EPCGLumenQuality::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EPCGLumenQuality::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EPCGLumenQuality::Medium" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGLumenQuality::Low", (int64)EPCGLumenQuality::Low },
		{ "EPCGLumenQuality::Medium", (int64)EPCGLumenQuality::Medium },
		{ "EPCGLumenQuality::High", (int64)EPCGLumenQuality::High },
		{ "EPCGLumenQuality::Epic", (int64)EPCGLumenQuality::Epic },
		{ "EPCGLumenQuality::Cinematic", (int64)EPCGLumenQuality::Cinematic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGLumenQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGLumenQuality",
	"EPCGLumenQuality",
	Z_Construct_UEnum_Aura_EPCGLumenQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGLumenQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGLumenQuality()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGLumenQuality.InnerSingleton, Z_Construct_UEnum_Aura_EPCGLumenQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGLumenQuality.InnerSingleton;
}
// ********** End Enum EPCGLumenQuality ************************************************************

// ********** Begin Enum EPCGLumenLightType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGLumenLightType;
static UEnum* EPCGLumenLightType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenLightType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGLumenLightType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGLumenLightType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGLumenLightType"));
	}
	return Z_Registration_Info_UEnum_EPCGLumenLightType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGLumenLightType>()
{
	return EPCGLumenLightType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGLumenLightType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Area.DisplayName", "Area Light" },
		{ "Area.Name", "EPCGLumenLightType::Area" },
		{ "BlueprintType", "true" },
		{ "Directional.DisplayName", "Directional Light" },
		{ "Directional.Name", "EPCGLumenLightType::Directional" },
		{ "Emissive.DisplayName", "Emissive Surface" },
		{ "Emissive.Name", "EPCGLumenLightType::Emissive" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
		{ "Point.DisplayName", "Point Light" },
		{ "Point.Name", "EPCGLumenLightType::Point" },
		{ "Sky.DisplayName", "Sky Light" },
		{ "Sky.Name", "EPCGLumenLightType::Sky" },
		{ "Spot.DisplayName", "Spot Light" },
		{ "Spot.Name", "EPCGLumenLightType::Spot" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGLumenLightType::Directional", (int64)EPCGLumenLightType::Directional },
		{ "EPCGLumenLightType::Point", (int64)EPCGLumenLightType::Point },
		{ "EPCGLumenLightType::Spot", (int64)EPCGLumenLightType::Spot },
		{ "EPCGLumenLightType::Sky", (int64)EPCGLumenLightType::Sky },
		{ "EPCGLumenLightType::Area", (int64)EPCGLumenLightType::Area },
		{ "EPCGLumenLightType::Emissive", (int64)EPCGLumenLightType::Emissive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGLumenLightType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGLumenLightType",
	"EPCGLumenLightType",
	Z_Construct_UEnum_Aura_EPCGLumenLightType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenLightType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenLightType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGLumenLightType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGLumenLightType()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenLightType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGLumenLightType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGLumenLightType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGLumenLightType.InnerSingleton;
}
// ********** End Enum EPCGLumenLightType **********************************************************

// ********** Begin Enum EPCGLumenUpdateMode *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGLumenUpdateMode;
static UEnum* EPCGLumenUpdateMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenUpdateMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGLumenUpdateMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGLumenUpdateMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGLumenUpdateMode"));
	}
	return Z_Registration_Info_UEnum_EPCGLumenUpdateMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGLumenUpdateMode>()
{
	return EPCGLumenUpdateMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGLumenUpdateMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive - Performance Based" },
		{ "Adaptive.Name", "EPCGLumenUpdateMode::Adaptive" },
		{ "BlueprintType", "true" },
		{ "Dynamic.DisplayName", "Dynamic - Real-time Updates" },
		{ "Dynamic.Name", "EPCGLumenUpdateMode::Dynamic" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
		{ "OnDemand.DisplayName", "On Demand - Manual Updates" },
		{ "OnDemand.Name", "EPCGLumenUpdateMode::OnDemand" },
		{ "Static.DisplayName", "Static - No Updates" },
		{ "Static.Name", "EPCGLumenUpdateMode::Static" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGLumenUpdateMode::Static", (int64)EPCGLumenUpdateMode::Static },
		{ "EPCGLumenUpdateMode::Dynamic", (int64)EPCGLumenUpdateMode::Dynamic },
		{ "EPCGLumenUpdateMode::Adaptive", (int64)EPCGLumenUpdateMode::Adaptive },
		{ "EPCGLumenUpdateMode::OnDemand", (int64)EPCGLumenUpdateMode::OnDemand },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGLumenUpdateMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGLumenUpdateMode",
	"EPCGLumenUpdateMode",
	Z_Construct_UEnum_Aura_EPCGLumenUpdateMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenUpdateMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenUpdateMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGLumenUpdateMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGLumenUpdateMode()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenUpdateMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGLumenUpdateMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGLumenUpdateMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGLumenUpdateMode.InnerSingleton;
}
// ********** End Enum EPCGLumenUpdateMode *********************************************************

// ********** Begin Enum EPCGLumenReflectionMode ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGLumenReflectionMode;
static UEnum* EPCGLumenReflectionMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenReflectionMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGLumenReflectionMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGLumenReflectionMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGLumenReflectionMode"));
	}
	return Z_Registration_Info_UEnum_EPCGLumenReflectionMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGLumenReflectionMode>()
{
	return EPCGLumenReflectionMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGLumenReflectionMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EPCGLumenReflectionMode::Disabled" },
		{ "Hybrid.DisplayName", "Hybrid Reflections" },
		{ "Hybrid.Name", "EPCGLumenReflectionMode::Hybrid" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
		{ "RayTraced.DisplayName", "Ray Traced Reflections" },
		{ "RayTraced.Name", "EPCGLumenReflectionMode::RayTraced" },
		{ "ScreenSpace.DisplayName", "Screen Space Reflections" },
		{ "ScreenSpace.Name", "EPCGLumenReflectionMode::ScreenSpace" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGLumenReflectionMode::ScreenSpace", (int64)EPCGLumenReflectionMode::ScreenSpace },
		{ "EPCGLumenReflectionMode::RayTraced", (int64)EPCGLumenReflectionMode::RayTraced },
		{ "EPCGLumenReflectionMode::Hybrid", (int64)EPCGLumenReflectionMode::Hybrid },
		{ "EPCGLumenReflectionMode::Disabled", (int64)EPCGLumenReflectionMode::Disabled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGLumenReflectionMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGLumenReflectionMode",
	"EPCGLumenReflectionMode",
	Z_Construct_UEnum_Aura_EPCGLumenReflectionMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenReflectionMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGLumenReflectionMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGLumenReflectionMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGLumenReflectionMode()
{
	if (!Z_Registration_Info_UEnum_EPCGLumenReflectionMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGLumenReflectionMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGLumenReflectionMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGLumenReflectionMode.InnerSingleton;
}
// ********** End Enum EPCGLumenReflectionMode *****************************************************

// ********** Begin ScriptStruct FPCGLumenConfig ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGLumenConfig;
class UScriptStruct* FPCGLumenConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGLumenConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGLumenConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGLumenConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGLumenConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateMode_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReflectionMode_MetaData[] = {
		{ "Category", "Reflections" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalIlluminationIntensity_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReflectionIntensity_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBounces_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "8" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateFrequency_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTemporalUpsampling_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHardwareRayTracing_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncCompute_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_UpdateMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UpdateMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReflectionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReflectionMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalIlluminationIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReflectionIntensity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxBounces;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateFrequency;
	static void NewProp_bEnableTemporalUpsampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTemporalUpsampling;
	static void NewProp_bEnableHardwareRayTracing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHardwareRayTracing;
	static void NewProp_bEnableAsyncCompute_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncCompute;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGLumenConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenConfig, Quality), Z_Construct_UEnum_Aura_EPCGLumenQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 2106416851
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_UpdateMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_UpdateMode = { "UpdateMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenConfig, UpdateMode), Z_Construct_UEnum_Aura_EPCGLumenUpdateMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateMode_MetaData), NewProp_UpdateMode_MetaData) }; // 3873132859
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_ReflectionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_ReflectionMode = { "ReflectionMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenConfig, ReflectionMode), Z_Construct_UEnum_Aura_EPCGLumenReflectionMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReflectionMode_MetaData), NewProp_ReflectionMode_MetaData) }; // 1021864741
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_GlobalIlluminationIntensity = { "GlobalIlluminationIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenConfig, GlobalIlluminationIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalIlluminationIntensity_MetaData), NewProp_GlobalIlluminationIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_ReflectionIntensity = { "ReflectionIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenConfig, ReflectionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReflectionIntensity_MetaData), NewProp_ReflectionIntensity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_MaxBounces = { "MaxBounces", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenConfig, MaxBounces), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBounces_MetaData), NewProp_MaxBounces_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_UpdateFrequency = { "UpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenConfig, UpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateFrequency_MetaData), NewProp_UpdateFrequency_MetaData) };
void Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableTemporalUpsampling_SetBit(void* Obj)
{
	((FPCGLumenConfig*)Obj)->bEnableTemporalUpsampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableTemporalUpsampling = { "bEnableTemporalUpsampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGLumenConfig), &Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableTemporalUpsampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTemporalUpsampling_MetaData), NewProp_bEnableTemporalUpsampling_MetaData) };
void Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableHardwareRayTracing_SetBit(void* Obj)
{
	((FPCGLumenConfig*)Obj)->bEnableHardwareRayTracing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableHardwareRayTracing = { "bEnableHardwareRayTracing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGLumenConfig), &Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableHardwareRayTracing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHardwareRayTracing_MetaData), NewProp_bEnableHardwareRayTracing_MetaData) };
void Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableAsyncCompute_SetBit(void* Obj)
{
	((FPCGLumenConfig*)Obj)->bEnableAsyncCompute = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableAsyncCompute = { "bEnableAsyncCompute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGLumenConfig), &Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableAsyncCompute_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncCompute_MetaData), NewProp_bEnableAsyncCompute_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_UpdateMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_UpdateMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_ReflectionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_ReflectionMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_GlobalIlluminationIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_ReflectionIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_MaxBounces,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_UpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableTemporalUpsampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableHardwareRayTracing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewProp_bEnableAsyncCompute,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGLumenConfig",
	Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::PropPointers),
	sizeof(FPCGLumenConfig),
	alignof(FPCGLumenConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGLumenConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGLumenConfig *****************************************************

// ********** Begin ScriptStruct FPCGLumenLightData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGLumenLightData;
class UScriptStruct* FPCGLumenLightData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenLightData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGLumenLightData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGLumenLightData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGLumenLightData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenLightData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGLumenLightData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightType_MetaData[] = {
		{ "Category", "Light" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Light" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Light" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "Category", "Light" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Intensity_MetaData[] = {
		{ "Category", "Light" },
		{ "ClampMax", "100000.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttenuationRadius_MetaData[] = {
		{ "Category", "Light" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastShadows_MetaData[] = {
		{ "Category", "Light" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectGlobalIllumination_MetaData[] = {
		{ "Category", "Light" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightComponent_MetaData[] = {
		{ "Category", "Light" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LightType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LightType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttenuationRadius;
	static void NewProp_bCastShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastShadows;
	static void NewProp_bAffectGlobalIllumination_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectGlobalIllumination;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_LightComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGLumenLightData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_LightType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_LightType = { "LightType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenLightData, LightType), Z_Construct_UEnum_Aura_EPCGLumenLightType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightType_MetaData), NewProp_LightType_MetaData) }; // 1441988139
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenLightData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenLightData, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenLightData, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenLightData, Intensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Intensity_MetaData), NewProp_Intensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_AttenuationRadius = { "AttenuationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenLightData, AttenuationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttenuationRadius_MetaData), NewProp_AttenuationRadius_MetaData) };
void Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bCastShadows_SetBit(void* Obj)
{
	((FPCGLumenLightData*)Obj)->bCastShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bCastShadows = { "bCastShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGLumenLightData), &Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bCastShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastShadows_MetaData), NewProp_bCastShadows_MetaData) };
void Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bAffectGlobalIllumination_SetBit(void* Obj)
{
	((FPCGLumenLightData*)Obj)->bAffectGlobalIllumination = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bAffectGlobalIllumination = { "bAffectGlobalIllumination", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGLumenLightData), &Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bAffectGlobalIllumination_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectGlobalIllumination_MetaData), NewProp_bAffectGlobalIllumination_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_LightComponent = { "LightComponent", nullptr, (EPropertyFlags)0x001400000008000d, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenLightData, LightComponent), Z_Construct_UClass_ULightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightComponent_MetaData), NewProp_LightComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_LightType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_LightType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_AttenuationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bCastShadows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_bAffectGlobalIllumination,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewProp_LightComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGLumenLightData",
	Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::PropPointers),
	sizeof(FPCGLumenLightData),
	alignof(FPCGLumenLightData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenLightData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenLightData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGLumenLightData.InnerSingleton, Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenLightData.InnerSingleton;
}
// ********** End ScriptStruct FPCGLumenLightData **************************************************

// ********** Begin ScriptStruct FPCGLumenSurfaceData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData;
class UScriptStruct* FPCGLumenSurfaceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGLumenSurfaceData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGLumenSurfaceData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "Category", "Surface" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Material_MetaData[] = {
		{ "Category", "Surface" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmissiveColor_MetaData[] = {
		{ "Category", "Surface" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmissiveIntensity_MetaData[] = {
		{ "Category", "Surface" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Roughness_MetaData[] = {
		{ "Category", "Surface" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metallic_MetaData[] = {
		{ "Category", "Surface" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTwoSided_MetaData[] = {
		{ "Category", "Surface" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectGlobalIllumination_MetaData[] = {
		{ "Category", "Surface" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Material;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EmissiveColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EmissiveIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Roughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metallic;
	static void NewProp_bTwoSided_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTwoSided;
	static void NewProp_bAffectGlobalIllumination_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectGlobalIllumination;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGLumenSurfaceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x001400000008000d, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenSurfaceData, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_Material = { "Material", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenSurfaceData, Material), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Material_MetaData), NewProp_Material_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_EmissiveColor = { "EmissiveColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenSurfaceData, EmissiveColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmissiveColor_MetaData), NewProp_EmissiveColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_EmissiveIntensity = { "EmissiveIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenSurfaceData, EmissiveIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmissiveIntensity_MetaData), NewProp_EmissiveIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_Roughness = { "Roughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenSurfaceData, Roughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Roughness_MetaData), NewProp_Roughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_Metallic = { "Metallic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenSurfaceData, Metallic), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metallic_MetaData), NewProp_Metallic_MetaData) };
void Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bTwoSided_SetBit(void* Obj)
{
	((FPCGLumenSurfaceData*)Obj)->bTwoSided = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bTwoSided = { "bTwoSided", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGLumenSurfaceData), &Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bTwoSided_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTwoSided_MetaData), NewProp_bTwoSided_MetaData) };
void Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bAffectGlobalIllumination_SetBit(void* Obj)
{
	((FPCGLumenSurfaceData*)Obj)->bAffectGlobalIllumination = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bAffectGlobalIllumination = { "bAffectGlobalIllumination", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGLumenSurfaceData), &Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bAffectGlobalIllumination_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectGlobalIllumination_MetaData), NewProp_bAffectGlobalIllumination_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_Material,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_EmissiveColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_EmissiveIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_Roughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_Metallic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bTwoSided,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewProp_bAffectGlobalIllumination,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGLumenSurfaceData",
	Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::PropPointers),
	sizeof(FPCGLumenSurfaceData),
	alignof(FPCGLumenSurfaceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenSurfaceData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData.InnerSingleton, Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData.InnerSingleton;
}
// ********** End ScriptStruct FPCGLumenSurfaceData ************************************************

// ********** Begin ScriptStruct FPCGLumenPerformanceStats *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats;
class UScriptStruct* FPCGLumenPerformanceStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGLumenPerformanceStats, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGLumenPerformanceStats"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalIlluminationTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReflectionTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceCacheTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RadianceCacheTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveLights_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveSurfaces_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalIlluminationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReflectionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SurfaceCacheTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RadianceCacheTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveLights;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveSurfaces;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGLumenPerformanceStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_GlobalIlluminationTime = { "GlobalIlluminationTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, GlobalIlluminationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalIlluminationTime_MetaData), NewProp_GlobalIlluminationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_ReflectionTime = { "ReflectionTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, ReflectionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReflectionTime_MetaData), NewProp_ReflectionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_SurfaceCacheTime = { "SurfaceCacheTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, SurfaceCacheTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceCacheTime_MetaData), NewProp_SurfaceCacheTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_RadianceCacheTime = { "RadianceCacheTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, RadianceCacheTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RadianceCacheTime_MetaData), NewProp_RadianceCacheTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_ActiveLights = { "ActiveLights", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, ActiveLights), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveLights_MetaData), NewProp_ActiveLights_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_ActiveSurfaces = { "ActiveSurfaces", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, ActiveSurfaces), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveSurfaces_MetaData), NewProp_ActiveSurfaces_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGLumenPerformanceStats, FrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTime_MetaData), NewProp_FrameTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_GlobalIlluminationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_ReflectionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_SurfaceCacheTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_RadianceCacheTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_ActiveLights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_ActiveSurfaces,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewProp_FrameTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGLumenPerformanceStats",
	Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::PropPointers),
	sizeof(FPCGLumenPerformanceStats),
	alignof(FPCGLumenPerformanceStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGLumenPerformanceStats()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats.InnerSingleton, Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats.InnerSingleton;
}
// ********** End ScriptStruct FPCGLumenPerformanceStats *******************************************

// ********** Begin Class APCGLumenIntegrator Function ForceGlobalIlluminationUpdate ***************
struct Z_Construct_UFunction_APCGLumenIntegrator_ForceGlobalIlluminationUpdate_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen GI" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_ForceGlobalIlluminationUpdate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "ForceGlobalIlluminationUpdate", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_ForceGlobalIlluminationUpdate_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_ForceGlobalIlluminationUpdate_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_ForceGlobalIlluminationUpdate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_ForceGlobalIlluminationUpdate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execForceGlobalIlluminationUpdate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceGlobalIlluminationUpdate();
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function ForceGlobalIlluminationUpdate *****************

// ********** Begin Class APCGLumenIntegrator Function GetActiveLights *****************************
struct Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics
{
	struct PCGLumenIntegrator_eventGetActiveLights_Parms
	{
		TArray<FPCGLumenLightData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Lights" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGLumenLightData, METADATA_PARAMS(0, nullptr) }; // 186323533
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventGetActiveLights_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 186323533
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "GetActiveLights", Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::PCGLumenIntegrator_eventGetActiveLights_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::PCGLumenIntegrator_eventGetActiveLights_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execGetActiveLights)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGLumenLightData>*)Z_Param__Result=P_THIS->GetActiveLights();
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function GetActiveLights *******************************

// ********** Begin Class APCGLumenIntegrator Function GetActiveSurfaces ***************************
struct Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics
{
	struct PCGLumenIntegrator_eventGetActiveSurfaces_Parms
	{
		TArray<FPCGLumenSurfaceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Surfaces" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGLumenSurfaceData, METADATA_PARAMS(0, nullptr) }; // 130860752
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventGetActiveSurfaces_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 130860752
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "GetActiveSurfaces", Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::PCGLumenIntegrator_eventGetActiveSurfaces_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::PCGLumenIntegrator_eventGetActiveSurfaces_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execGetActiveSurfaces)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGLumenSurfaceData>*)Z_Param__Result=P_THIS->GetActiveSurfaces();
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function GetActiveSurfaces *****************************

// ********** Begin Class APCGLumenIntegrator Function GetPerformanceStats *************************
struct Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics
{
	struct PCGLumenIntegrator_eventGetPerformanceStats_Parms
	{
		FPCGLumenPerformanceStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventGetPerformanceStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGLumenPerformanceStats, METADATA_PARAMS(0, nullptr) }; // 1012148288
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "GetPerformanceStats", Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::PCGLumenIntegrator_eventGetPerformanceStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::PCGLumenIntegrator_eventGetPerformanceStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execGetPerformanceStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGLumenPerformanceStats*)Z_Param__Result=P_THIS->GetPerformanceStats();
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function GetPerformanceStats ***************************

// ********** Begin Class APCGLumenIntegrator Function IntegrateWithNanite *************************
struct Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics
{
	struct PCGLumenIntegrator_eventIntegrateWithNanite_Parms
	{
		APCGNaniteOptimizer* NaniteOptimizer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NaniteOptimizer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer = { "NaniteOptimizer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventIntegrateWithNanite_Parms, NaniteOptimizer), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "IntegrateWithNanite", Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::PCGLumenIntegrator_eventIntegrateWithNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::PCGLumenIntegrator_eventIntegrateWithNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execIntegrateWithNanite)
{
	P_GET_OBJECT(APCGNaniteOptimizer,Z_Param_NaniteOptimizer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithNanite(Z_Param_NaniteOptimizer);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function IntegrateWithNanite ***************************

// ********** Begin Class APCGLumenIntegrator Function IntegrateWithWorldPartition *****************
struct Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics
{
	struct PCGLumenIntegrator_eventIntegrateWithWorldPartition_Parms
	{
		APCGWorldPartitionManager* WorldPartitionManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Integration" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventIntegrateWithWorldPartition_Parms, WorldPartitionManager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "IntegrateWithWorldPartition", Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::PCGLumenIntegrator_eventIntegrateWithWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::PCGLumenIntegrator_eventIntegrateWithWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execIntegrateWithWorldPartition)
{
	P_GET_OBJECT(APCGWorldPartitionManager,Z_Param_WorldPartitionManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithWorldPartition(Z_Param_WorldPartitionManager);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function IntegrateWithWorldPartition *******************

// ********** Begin Class APCGLumenIntegrator Function OnLightComponentDestroyed *******************
struct Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics
{
	struct PCGLumenIntegrator_eventOnLightComponentDestroyed_Parms
	{
		UActorComponent* DestroyedComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event handlers\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event handlers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestroyedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DestroyedComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::NewProp_DestroyedComponent = { "DestroyedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventOnLightComponentDestroyed_Parms, DestroyedComponent), Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestroyedComponent_MetaData), NewProp_DestroyedComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::NewProp_DestroyedComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "OnLightComponentDestroyed", Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::PCGLumenIntegrator_eventOnLightComponentDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::PCGLumenIntegrator_eventOnLightComponentDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execOnLightComponentDestroyed)
{
	P_GET_OBJECT(UActorComponent,Z_Param_DestroyedComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnLightComponentDestroyed(Z_Param_DestroyedComponent);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function OnLightComponentDestroyed *********************

// ********** Begin Class APCGLumenIntegrator Function OnMeshComponentDestroyed ********************
struct Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics
{
	struct PCGLumenIntegrator_eventOnMeshComponentDestroyed_Parms
	{
		UActorComponent* DestroyedComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestroyedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DestroyedComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::NewProp_DestroyedComponent = { "DestroyedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventOnMeshComponentDestroyed_Parms, DestroyedComponent), Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestroyedComponent_MetaData), NewProp_DestroyedComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::NewProp_DestroyedComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "OnMeshComponentDestroyed", Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::PCGLumenIntegrator_eventOnMeshComponentDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::PCGLumenIntegrator_eventOnMeshComponentDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execOnMeshComponentDestroyed)
{
	P_GET_OBJECT(UActorComponent,Z_Param_DestroyedComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnMeshComponentDestroyed(Z_Param_DestroyedComponent);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function OnMeshComponentDestroyed **********************

// ********** Begin Class APCGLumenIntegrator Function OptimizeForPerformance **********************
struct Z_Construct_UFunction_APCGLumenIntegrator_OptimizeForPerformance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_OptimizeForPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "OptimizeForPerformance", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_OptimizeForPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_OptimizeForPerformance_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_OptimizeForPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_OptimizeForPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execOptimizeForPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeForPerformance();
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function OptimizeForPerformance ************************

// ********** Begin Class APCGLumenIntegrator Function RegisterLight *******************************
struct Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics
{
	struct PCGLumenIntegrator_eventRegisterLight_Parms
	{
		ULightComponent* LightComponent;
		FPCGLumenLightData LightData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Lights" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Light Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Light Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LightComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::NewProp_LightComponent = { "LightComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventRegisterLight_Parms, LightComponent), Z_Construct_UClass_ULightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightComponent_MetaData), NewProp_LightComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::NewProp_LightData = { "LightData", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventRegisterLight_Parms, LightData), Z_Construct_UScriptStruct_FPCGLumenLightData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightData_MetaData), NewProp_LightData_MetaData) }; // 186323533
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::NewProp_LightComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::NewProp_LightData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "RegisterLight", Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::PCGLumenIntegrator_eventRegisterLight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::PCGLumenIntegrator_eventRegisterLight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execRegisterLight)
{
	P_GET_OBJECT(ULightComponent,Z_Param_LightComponent);
	P_GET_STRUCT_REF(FPCGLumenLightData,Z_Param_Out_LightData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterLight(Z_Param_LightComponent,Z_Param_Out_LightData);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function RegisterLight *********************************

// ********** Begin Class APCGLumenIntegrator Function RegisterSurface *****************************
struct Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics
{
	struct PCGLumenIntegrator_eventRegisterSurface_Parms
	{
		UStaticMeshComponent* MeshComponent;
		FPCGLumenSurfaceData SurfaceData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Surfaces" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Surface Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Surface Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SurfaceData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventRegisterSurface_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::NewProp_SurfaceData = { "SurfaceData", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventRegisterSurface_Parms, SurfaceData), Z_Construct_UScriptStruct_FPCGLumenSurfaceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceData_MetaData), NewProp_SurfaceData_MetaData) }; // 130860752
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::NewProp_SurfaceData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "RegisterSurface", Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::PCGLumenIntegrator_eventRegisterSurface_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::PCGLumenIntegrator_eventRegisterSurface_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execRegisterSurface)
{
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_MeshComponent);
	P_GET_STRUCT_REF(FPCGLumenSurfaceData,Z_Param_Out_SurfaceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterSurface(Z_Param_MeshComponent,Z_Param_Out_SurfaceData);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function RegisterSurface *******************************

// ********** Begin Class APCGLumenIntegrator Function SetAdaptiveQuality **************************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics
{
	struct PCGLumenIntegrator_eventSetAdaptiveQuality_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((PCGLumenIntegrator_eventSetAdaptiveQuality_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGLumenIntegrator_eventSetAdaptiveQuality_Parms), &Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetAdaptiveQuality", Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::PCGLumenIntegrator_eventSetAdaptiveQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::PCGLumenIntegrator_eventSetAdaptiveQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetAdaptiveQuality)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAdaptiveQuality(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetAdaptiveQuality ****************************

// ********** Begin Class APCGLumenIntegrator Function SetEmissiveSurface **************************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics
{
	struct PCGLumenIntegrator_eventSetEmissiveSurface_Parms
	{
		UStaticMeshComponent* MeshComponent;
		FLinearColor EmissiveColor;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Surfaces" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmissiveColor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EmissiveColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetEmissiveSurface_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::NewProp_EmissiveColor = { "EmissiveColor", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetEmissiveSurface_Parms, EmissiveColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmissiveColor_MetaData), NewProp_EmissiveColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetEmissiveSurface_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::NewProp_EmissiveColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetEmissiveSurface", Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::PCGLumenIntegrator_eventSetEmissiveSurface_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::PCGLumenIntegrator_eventSetEmissiveSurface_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetEmissiveSurface)
{
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_MeshComponent);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_EmissiveColor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEmissiveSurface(Z_Param_MeshComponent,Z_Param_Out_EmissiveColor,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetEmissiveSurface ****************************

// ********** Begin Class APCGLumenIntegrator Function SetGlobalIlluminationIntensity **************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics
{
	struct PCGLumenIntegrator_eventSetGlobalIlluminationIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen GI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Global Illumination Control\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global Illumination Control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetGlobalIlluminationIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetGlobalIlluminationIntensity", Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::PCGLumenIntegrator_eventSetGlobalIlluminationIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::PCGLumenIntegrator_eventSetGlobalIlluminationIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetGlobalIlluminationIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalIlluminationIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetGlobalIlluminationIntensity ****************

// ********** Begin Class APCGLumenIntegrator Function SetGlobalLightIntensity *********************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics
{
	struct PCGLumenIntegrator_eventSetGlobalLightIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Lights" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetGlobalLightIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetGlobalLightIntensity", Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::PCGLumenIntegrator_eventSetGlobalLightIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::PCGLumenIntegrator_eventSetGlobalLightIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetGlobalLightIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalLightIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetGlobalLightIntensity ***********************

// ********** Begin Class APCGLumenIntegrator Function SetLumenQuality *****************************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics
{
	struct PCGLumenIntegrator_eventSetLumenQuality_Parms
	{
		EPCGLumenQuality Quality;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen GI" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetLumenQuality_Parms, Quality), Z_Construct_UEnum_Aura_EPCGLumenQuality, METADATA_PARAMS(0, nullptr) }; // 2106416851
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::NewProp_Quality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetLumenQuality", Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::PCGLumenIntegrator_eventSetLumenQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::PCGLumenIntegrator_eventSetLumenQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetLumenQuality)
{
	P_GET_ENUM(EPCGLumenQuality,Z_Param_Quality);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLumenQuality(EPCGLumenQuality(Z_Param_Quality));
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetLumenQuality *******************************

// ********** Begin Class APCGLumenIntegrator Function SetMaxReflectionBounces *********************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics
{
	struct PCGLumenIntegrator_eventSetMaxReflectionBounces_Parms
	{
		int32 MaxBounces;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Reflections" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxBounces;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::NewProp_MaxBounces = { "MaxBounces", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetMaxReflectionBounces_Parms, MaxBounces), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::NewProp_MaxBounces,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetMaxReflectionBounces", Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::PCGLumenIntegrator_eventSetMaxReflectionBounces_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::PCGLumenIntegrator_eventSetMaxReflectionBounces_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetMaxReflectionBounces)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxBounces);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaxReflectionBounces(Z_Param_MaxBounces);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetMaxReflectionBounces ***********************

// ********** Begin Class APCGLumenIntegrator Function SetReflectionIntensity **********************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics
{
	struct PCGLumenIntegrator_eventSetReflectionIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen GI" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetReflectionIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetReflectionIntensity", Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::PCGLumenIntegrator_eventSetReflectionIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::PCGLumenIntegrator_eventSetReflectionIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetReflectionIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetReflectionIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetReflectionIntensity ************************

// ********** Begin Class APCGLumenIntegrator Function SetReflectionMode ***************************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics
{
	struct PCGLumenIntegrator_eventSetReflectionMode_Parms
	{
		EPCGLumenReflectionMode ReflectionMode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Reflections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reflection Control\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reflection Control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReflectionMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReflectionMode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::NewProp_ReflectionMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::NewProp_ReflectionMode = { "ReflectionMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetReflectionMode_Parms, ReflectionMode), Z_Construct_UEnum_Aura_EPCGLumenReflectionMode, METADATA_PARAMS(0, nullptr) }; // 1021864741
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::NewProp_ReflectionMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::NewProp_ReflectionMode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetReflectionMode", Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::PCGLumenIntegrator_eventSetReflectionMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::PCGLumenIntegrator_eventSetReflectionMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetReflectionMode)
{
	P_GET_ENUM(EPCGLumenReflectionMode,Z_Param_ReflectionMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetReflectionMode(EPCGLumenReflectionMode(Z_Param_ReflectionMode));
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetReflectionMode *****************************

// ********** Begin Class APCGLumenIntegrator Function SetUpdateMode *******************************
struct Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics
{
	struct PCGLumenIntegrator_eventSetUpdateMode_Parms
	{
		EPCGLumenUpdateMode UpdateMode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen GI" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_UpdateMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_UpdateMode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::NewProp_UpdateMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::NewProp_UpdateMode = { "UpdateMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventSetUpdateMode_Parms, UpdateMode), Z_Construct_UEnum_Aura_EPCGLumenUpdateMode, METADATA_PARAMS(0, nullptr) }; // 3873132859
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::NewProp_UpdateMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::NewProp_UpdateMode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SetUpdateMode", Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::PCGLumenIntegrator_eventSetUpdateMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::PCGLumenIntegrator_eventSetUpdateMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSetUpdateMode)
{
	P_GET_ENUM(EPCGLumenUpdateMode,Z_Param_UpdateMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetUpdateMode(EPCGLumenUpdateMode(Z_Param_UpdateMode));
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SetUpdateMode *********************************

// ********** Begin Class APCGLumenIntegrator Function SynchronizeWithPCGSystem ********************
struct Z_Construct_UFunction_APCGLumenIntegrator_SynchronizeWithPCGSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Integration" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_SynchronizeWithPCGSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "SynchronizeWithPCGSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_SynchronizeWithPCGSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_SynchronizeWithPCGSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_SynchronizeWithPCGSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_SynchronizeWithPCGSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execSynchronizeWithPCGSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithPCGSystem();
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function SynchronizeWithPCGSystem **********************

// ********** Begin Class APCGLumenIntegrator Function UnregisterLight *****************************
struct Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics
{
	struct PCGLumenIntegrator_eventUnregisterLight_Parms
	{
		ULightComponent* LightComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Lights" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LightComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::NewProp_LightComponent = { "LightComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventUnregisterLight_Parms, LightComponent), Z_Construct_UClass_ULightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightComponent_MetaData), NewProp_LightComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::NewProp_LightComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "UnregisterLight", Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::PCGLumenIntegrator_eventUnregisterLight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::PCGLumenIntegrator_eventUnregisterLight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execUnregisterLight)
{
	P_GET_OBJECT(ULightComponent,Z_Param_LightComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterLight(Z_Param_LightComponent);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function UnregisterLight *******************************

// ********** Begin Class APCGLumenIntegrator Function UnregisterSurface ***************************
struct Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics
{
	struct PCGLumenIntegrator_eventUnregisterSurface_Parms
	{
		UStaticMeshComponent* MeshComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Surfaces" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventUnregisterSurface_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::NewProp_MeshComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "UnregisterSurface", Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::PCGLumenIntegrator_eventUnregisterSurface_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::PCGLumenIntegrator_eventUnregisterSurface_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execUnregisterSurface)
{
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_MeshComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterSurface(Z_Param_MeshComponent);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function UnregisterSurface *****************************

// ********** Begin Class APCGLumenIntegrator Function UpdateLightData *****************************
struct Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics
{
	struct PCGLumenIntegrator_eventUpdateLightData_Parms
	{
		ULightComponent* LightComponent;
		FPCGLumenLightData NewLightData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Lights" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewLightData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LightComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewLightData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::NewProp_LightComponent = { "LightComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventUpdateLightData_Parms, LightComponent), Z_Construct_UClass_ULightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightComponent_MetaData), NewProp_LightComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::NewProp_NewLightData = { "NewLightData", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventUpdateLightData_Parms, NewLightData), Z_Construct_UScriptStruct_FPCGLumenLightData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewLightData_MetaData), NewProp_NewLightData_MetaData) }; // 186323533
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::NewProp_LightComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::NewProp_NewLightData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "UpdateLightData", Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::PCGLumenIntegrator_eventUpdateLightData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::PCGLumenIntegrator_eventUpdateLightData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execUpdateLightData)
{
	P_GET_OBJECT(ULightComponent,Z_Param_LightComponent);
	P_GET_STRUCT_REF(FPCGLumenLightData,Z_Param_Out_NewLightData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLightData(Z_Param_LightComponent,Z_Param_Out_NewLightData);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function UpdateLightData *******************************

// ********** Begin Class APCGLumenIntegrator Function UpdateReflectionCaptures ********************
struct Z_Construct_UFunction_APCGLumenIntegrator_UpdateReflectionCaptures_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Reflections" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_UpdateReflectionCaptures_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "UpdateReflectionCaptures", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UpdateReflectionCaptures_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_UpdateReflectionCaptures_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_UpdateReflectionCaptures()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_UpdateReflectionCaptures_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execUpdateReflectionCaptures)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateReflectionCaptures();
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function UpdateReflectionCaptures **********************

// ********** Begin Class APCGLumenIntegrator Function UpdateSurfaceData ***************************
struct Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics
{
	struct PCGLumenIntegrator_eventUpdateSurfaceData_Parms
	{
		UStaticMeshComponent* MeshComponent;
		FPCGLumenSurfaceData NewSurfaceData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lumen Surfaces" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSurfaceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewSurfaceData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventUpdateSurfaceData_Parms, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::NewProp_NewSurfaceData = { "NewSurfaceData", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGLumenIntegrator_eventUpdateSurfaceData_Parms, NewSurfaceData), Z_Construct_UScriptStruct_FPCGLumenSurfaceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSurfaceData_MetaData), NewProp_NewSurfaceData_MetaData) }; // 130860752
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::NewProp_NewSurfaceData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGLumenIntegrator, nullptr, "UpdateSurfaceData", Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::PCGLumenIntegrator_eventUpdateSurfaceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::PCGLumenIntegrator_eventUpdateSurfaceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGLumenIntegrator::execUpdateSurfaceData)
{
	P_GET_OBJECT(UStaticMeshComponent,Z_Param_MeshComponent);
	P_GET_STRUCT_REF(FPCGLumenSurfaceData,Z_Param_Out_NewSurfaceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSurfaceData(Z_Param_MeshComponent,Z_Param_Out_NewSurfaceData);
	P_NATIVE_END;
}
// ********** End Class APCGLumenIntegrator Function UpdateSurfaceData *****************************

// ********** Begin Class APCGLumenIntegrator ******************************************************
void APCGLumenIntegrator::StaticRegisterNativesAPCGLumenIntegrator()
{
	UClass* Class = APCGLumenIntegrator::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ForceGlobalIlluminationUpdate", &APCGLumenIntegrator::execForceGlobalIlluminationUpdate },
		{ "GetActiveLights", &APCGLumenIntegrator::execGetActiveLights },
		{ "GetActiveSurfaces", &APCGLumenIntegrator::execGetActiveSurfaces },
		{ "GetPerformanceStats", &APCGLumenIntegrator::execGetPerformanceStats },
		{ "IntegrateWithNanite", &APCGLumenIntegrator::execIntegrateWithNanite },
		{ "IntegrateWithWorldPartition", &APCGLumenIntegrator::execIntegrateWithWorldPartition },
		{ "OnLightComponentDestroyed", &APCGLumenIntegrator::execOnLightComponentDestroyed },
		{ "OnMeshComponentDestroyed", &APCGLumenIntegrator::execOnMeshComponentDestroyed },
		{ "OptimizeForPerformance", &APCGLumenIntegrator::execOptimizeForPerformance },
		{ "RegisterLight", &APCGLumenIntegrator::execRegisterLight },
		{ "RegisterSurface", &APCGLumenIntegrator::execRegisterSurface },
		{ "SetAdaptiveQuality", &APCGLumenIntegrator::execSetAdaptiveQuality },
		{ "SetEmissiveSurface", &APCGLumenIntegrator::execSetEmissiveSurface },
		{ "SetGlobalIlluminationIntensity", &APCGLumenIntegrator::execSetGlobalIlluminationIntensity },
		{ "SetGlobalLightIntensity", &APCGLumenIntegrator::execSetGlobalLightIntensity },
		{ "SetLumenQuality", &APCGLumenIntegrator::execSetLumenQuality },
		{ "SetMaxReflectionBounces", &APCGLumenIntegrator::execSetMaxReflectionBounces },
		{ "SetReflectionIntensity", &APCGLumenIntegrator::execSetReflectionIntensity },
		{ "SetReflectionMode", &APCGLumenIntegrator::execSetReflectionMode },
		{ "SetUpdateMode", &APCGLumenIntegrator::execSetUpdateMode },
		{ "SynchronizeWithPCGSystem", &APCGLumenIntegrator::execSynchronizeWithPCGSystem },
		{ "UnregisterLight", &APCGLumenIntegrator::execUnregisterLight },
		{ "UnregisterSurface", &APCGLumenIntegrator::execUnregisterSurface },
		{ "UpdateLightData", &APCGLumenIntegrator::execUpdateLightData },
		{ "UpdateReflectionCaptures", &APCGLumenIntegrator::execUpdateReflectionCaptures },
		{ "UpdateSurfaceData", &APCGLumenIntegrator::execUpdateSurfaceData },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_APCGLumenIntegrator;
UClass* APCGLumenIntegrator::GetPrivateStaticClass()
{
	using TClass = APCGLumenIntegrator;
	if (!Z_Registration_Info_UClass_APCGLumenIntegrator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGLumenIntegrator"),
			Z_Registration_Info_UClass_APCGLumenIntegrator.InnerSingleton,
			StaticRegisterNativesAPCGLumenIntegrator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_APCGLumenIntegrator.InnerSingleton;
}
UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister()
{
	return APCGLumenIntegrator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_APCGLumenIntegrator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * APCGLumenIntegrator - Advanced Lumen integration for procedural content generation\n * Provides dynamic global illumination and reflection management for PCG systems\n * Optimized for UE5.6 with modern rendering pipeline integration\n */" },
#endif
		{ "IncludePath", "APCGLumenIntegrator.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "APCGLumenIntegrator - Advanced Lumen integration for procedural content generation\nProvides dynamic global illumination and reflection management for PCG systems\nOptimized for UE5.6 with modern rendering pipeline integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenConfig_MetaData[] = {
		{ "Category", "Lumen Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceMonitoring_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoOptimization_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugInfo_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredLights_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredSurfaces_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentStats_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteOptimizerRef_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration references\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionManagerRef_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGLumenIntegrator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LumenConfig;
	static void NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceMonitoring;
	static void NewProp_bAutoOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoOptimization;
	static void NewProp_bShowDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugInfo;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredLights_ValueProp;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_RegisteredLights_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredLights;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredSurfaces_ValueProp;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_RegisteredSurfaces_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredSurfaces;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentStats;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_NaniteOptimizerRef;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WorldPartitionManagerRef;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APCGLumenIntegrator_ForceGlobalIlluminationUpdate, "ForceGlobalIlluminationUpdate" }, // 85420190
		{ &Z_Construct_UFunction_APCGLumenIntegrator_GetActiveLights, "GetActiveLights" }, // 1761389387
		{ &Z_Construct_UFunction_APCGLumenIntegrator_GetActiveSurfaces, "GetActiveSurfaces" }, // 2496622832
		{ &Z_Construct_UFunction_APCGLumenIntegrator_GetPerformanceStats, "GetPerformanceStats" }, // 3383997375
		{ &Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithNanite, "IntegrateWithNanite" }, // 2035874966
		{ &Z_Construct_UFunction_APCGLumenIntegrator_IntegrateWithWorldPartition, "IntegrateWithWorldPartition" }, // 1311013419
		{ &Z_Construct_UFunction_APCGLumenIntegrator_OnLightComponentDestroyed, "OnLightComponentDestroyed" }, // 3003437228
		{ &Z_Construct_UFunction_APCGLumenIntegrator_OnMeshComponentDestroyed, "OnMeshComponentDestroyed" }, // 2969389294
		{ &Z_Construct_UFunction_APCGLumenIntegrator_OptimizeForPerformance, "OptimizeForPerformance" }, // 1189205457
		{ &Z_Construct_UFunction_APCGLumenIntegrator_RegisterLight, "RegisterLight" }, // 4160412916
		{ &Z_Construct_UFunction_APCGLumenIntegrator_RegisterSurface, "RegisterSurface" }, // 152598536
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetAdaptiveQuality, "SetAdaptiveQuality" }, // 407177989
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetEmissiveSurface, "SetEmissiveSurface" }, // 3214151300
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalIlluminationIntensity, "SetGlobalIlluminationIntensity" }, // 568710301
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetGlobalLightIntensity, "SetGlobalLightIntensity" }, // 3375809375
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetLumenQuality, "SetLumenQuality" }, // 2220928630
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetMaxReflectionBounces, "SetMaxReflectionBounces" }, // 3287355670
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionIntensity, "SetReflectionIntensity" }, // 1837728369
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetReflectionMode, "SetReflectionMode" }, // 2858771235
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SetUpdateMode, "SetUpdateMode" }, // 2297454894
		{ &Z_Construct_UFunction_APCGLumenIntegrator_SynchronizeWithPCGSystem, "SynchronizeWithPCGSystem" }, // 3014255649
		{ &Z_Construct_UFunction_APCGLumenIntegrator_UnregisterLight, "UnregisterLight" }, // 1137516541
		{ &Z_Construct_UFunction_APCGLumenIntegrator_UnregisterSurface, "UnregisterSurface" }, // 2526031175
		{ &Z_Construct_UFunction_APCGLumenIntegrator_UpdateLightData, "UpdateLightData" }, // 3317161161
		{ &Z_Construct_UFunction_APCGLumenIntegrator_UpdateReflectionCaptures, "UpdateReflectionCaptures" }, // 2666219902
		{ &Z_Construct_UFunction_APCGLumenIntegrator_UpdateSurfaceData, "UpdateSurfaceData" }, // 3733950519
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APCGLumenIntegrator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_LumenConfig = { "LumenConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGLumenIntegrator, LumenConfig), Z_Construct_UScriptStruct_FPCGLumenConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenConfig_MetaData), NewProp_LumenConfig_MetaData) }; // 221160948
void Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj)
{
	((APCGLumenIntegrator*)Obj)->bEnablePerformanceMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bEnablePerformanceMonitoring = { "bEnablePerformanceMonitoring", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGLumenIntegrator), &Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bEnablePerformanceMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceMonitoring_MetaData), NewProp_bEnablePerformanceMonitoring_MetaData) };
void Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bAutoOptimization_SetBit(void* Obj)
{
	((APCGLumenIntegrator*)Obj)->bAutoOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bAutoOptimization = { "bAutoOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGLumenIntegrator), &Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bAutoOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoOptimization_MetaData), NewProp_bAutoOptimization_MetaData) };
void Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bShowDebugInfo_SetBit(void* Obj)
{
	((APCGLumenIntegrator*)Obj)->bShowDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bShowDebugInfo = { "bShowDebugInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGLumenIntegrator), &Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bShowDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugInfo_MetaData), NewProp_bShowDebugInfo_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredLights_ValueProp = { "RegisteredLights", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGLumenLightData, METADATA_PARAMS(0, nullptr) }; // 186323533
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredLights_Key_KeyProp = { "RegisteredLights_Key", nullptr, (EPropertyFlags)0x0004008000080000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ULightComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredLights = { "RegisteredLights", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGLumenIntegrator, RegisteredLights), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredLights_MetaData), NewProp_RegisteredLights_MetaData) }; // 186323533
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredSurfaces_ValueProp = { "RegisteredSurfaces", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGLumenSurfaceData, METADATA_PARAMS(0, nullptr) }; // 130860752
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredSurfaces_Key_KeyProp = { "RegisteredSurfaces_Key", nullptr, (EPropertyFlags)0x0004008000080000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredSurfaces = { "RegisteredSurfaces", nullptr, (EPropertyFlags)0x0040008000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGLumenIntegrator, RegisteredSurfaces), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredSurfaces_MetaData), NewProp_RegisteredSurfaces_MetaData) }; // 130860752
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_CurrentStats = { "CurrentStats", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGLumenIntegrator, CurrentStats), Z_Construct_UScriptStruct_FPCGLumenPerformanceStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentStats_MetaData), NewProp_CurrentStats_MetaData) }; // 1012148288
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_NaniteOptimizerRef = { "NaniteOptimizerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGLumenIntegrator, NaniteOptimizerRef), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteOptimizerRef_MetaData), NewProp_NaniteOptimizerRef_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_WorldPartitionManagerRef = { "WorldPartitionManagerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGLumenIntegrator, WorldPartitionManagerRef), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionManagerRef_MetaData), NewProp_WorldPartitionManagerRef_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APCGLumenIntegrator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_LumenConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bEnablePerformanceMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bAutoOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_bShowDebugInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredLights_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredLights_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredLights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredSurfaces_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredSurfaces_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_RegisteredSurfaces,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_CurrentStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_NaniteOptimizerRef,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGLumenIntegrator_Statics::NewProp_WorldPartitionManagerRef,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGLumenIntegrator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APCGLumenIntegrator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGLumenIntegrator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_APCGLumenIntegrator_Statics::ClassParams = {
	&APCGLumenIntegrator::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APCGLumenIntegrator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APCGLumenIntegrator_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APCGLumenIntegrator_Statics::Class_MetaDataParams), Z_Construct_UClass_APCGLumenIntegrator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APCGLumenIntegrator()
{
	if (!Z_Registration_Info_UClass_APCGLumenIntegrator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APCGLumenIntegrator.OuterSingleton, Z_Construct_UClass_APCGLumenIntegrator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APCGLumenIntegrator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APCGLumenIntegrator);
APCGLumenIntegrator::~APCGLumenIntegrator() {}
// ********** End Class APCGLumenIntegrator ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGLumenQuality_StaticEnum, TEXT("EPCGLumenQuality"), &Z_Registration_Info_UEnum_EPCGLumenQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2106416851U) },
		{ EPCGLumenLightType_StaticEnum, TEXT("EPCGLumenLightType"), &Z_Registration_Info_UEnum_EPCGLumenLightType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1441988139U) },
		{ EPCGLumenUpdateMode_StaticEnum, TEXT("EPCGLumenUpdateMode"), &Z_Registration_Info_UEnum_EPCGLumenUpdateMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3873132859U) },
		{ EPCGLumenReflectionMode_StaticEnum, TEXT("EPCGLumenReflectionMode"), &Z_Registration_Info_UEnum_EPCGLumenReflectionMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1021864741U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGLumenConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGLumenConfig_Statics::NewStructOps, TEXT("PCGLumenConfig"), &Z_Registration_Info_UScriptStruct_FPCGLumenConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGLumenConfig), 221160948U) },
		{ FPCGLumenLightData::StaticStruct, Z_Construct_UScriptStruct_FPCGLumenLightData_Statics::NewStructOps, TEXT("PCGLumenLightData"), &Z_Registration_Info_UScriptStruct_FPCGLumenLightData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGLumenLightData), 186323533U) },
		{ FPCGLumenSurfaceData::StaticStruct, Z_Construct_UScriptStruct_FPCGLumenSurfaceData_Statics::NewStructOps, TEXT("PCGLumenSurfaceData"), &Z_Registration_Info_UScriptStruct_FPCGLumenSurfaceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGLumenSurfaceData), 130860752U) },
		{ FPCGLumenPerformanceStats::StaticStruct, Z_Construct_UScriptStruct_FPCGLumenPerformanceStats_Statics::NewStructOps, TEXT("PCGLumenPerformanceStats"), &Z_Registration_Info_UScriptStruct_FPCGLumenPerformanceStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGLumenPerformanceStats), 1012148288U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APCGLumenIntegrator, APCGLumenIntegrator::StaticClass, TEXT("APCGLumenIntegrator"), &Z_Registration_Info_UClass_APCGLumenIntegrator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APCGLumenIntegrator), 1785093063U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_1747750874(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGLumenIntegrator_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
