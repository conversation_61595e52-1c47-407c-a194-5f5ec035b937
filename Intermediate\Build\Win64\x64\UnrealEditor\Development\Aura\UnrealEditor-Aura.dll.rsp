/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/PCG.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/GeometryCore.natvis"
/NATVIS:"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/Niagara.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
"C:/Aura/Intermediate/Build/Win64/x64/AuraEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.RTTI.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.1.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.2.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.3.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.4.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.5.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.6.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.7.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.8.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.9.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.10.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.11.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Aura.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ABaronAuracronManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ADragonPrismalManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AGeometricValidator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ALaneManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AMapManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AMinionWaveManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGCacheManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGChaosIntegrator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGLumenIntegrator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGNaniteOptimizer.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGStreamingManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGWorldPartitionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AProceduralMapGenerator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ARiverPrismalManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AWallCollisionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/implementacao_automatizada.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/testes_precisao_geometrica.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/UPCGPerformanceProfiler.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/UPCGQualityValidator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/UPCGVersionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/PerModuleInline.gen.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Default.rc2.res"
"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/UnrealEditor-PCG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/StaticMeshEditor/UnrealEditor-StaticMeshEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorSubsystem/UnrealEditor-EditorSubsystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealEd/UnrealEditor-UnrealEd.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ToolMenus/UnrealEditor-ToolMenus.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Json/UnrealEditor-Json.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/JsonUtilities/UnrealEditor-JsonUtilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Landscape/UnrealEditor-Landscape.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshDescription/UnrealEditor-MeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/StaticMeshDescription/UnrealEditor-StaticMeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/UnrealEditor-GeometryCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryFramework/UnrealEditor-GeometryFramework.lib"
"../Plugins/Runtime/GeometryScripting/Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryScriptingCore/UnrealEditor-GeometryScriptingCore.lib"
"../Plugins/Runtime/GeometryProcessing/Intermediate/Build/Win64/x64/UnrealEditor/Development/DynamicMesh/UnrealEditor-DynamicMesh.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PhysicsCore/UnrealEditor-PhysicsCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Chaos/UnrealEditor-Chaos.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosSolverEngine/UnrealEditor-ChaosSolverEngine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMG/UnrealEditor-UMG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NavigationSystem/UnrealEditor-NavigationSystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AIModule/UnrealEditor-AIModule.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTasks/UnrealEditor-GameplayTasks.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTags/UnrealEditor-GameplayTags.lib"
"../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayAbilities/UnrealEditor-GameplayAbilities.lib"
"../Plugins/Runtime/ProceduralMeshComponent/Intermediate/Build/Win64/x64/UnrealEditor/Development/ProceduralMeshComponent/UnrealEditor-ProceduralMeshComponent.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AutomationController/UnrealEditor-AutomationController.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/FunctionalTesting/UnrealEditor-FunctionalTesting.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/Niagara/UnrealEditor-Niagara.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraCore/UnrealEditor-NiagaraCore.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraShader/UnrealEditor-NiagaraShader.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InputCore/UnrealEditor-InputCore.lib"
"../Plugins/EnhancedInput/Intermediate/Build/Win64/x64/UnrealEditor/Development/EnhancedInput/UnrealEditor-EnhancedInput.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Renderer/UnrealEditor-Renderer.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"C:/Aura/Binaries/Win64/UnrealEditor-Aura.dll"
/PDB:"C:/Aura/Binaries/Win64/UnrealEditor-Aura.pdb"
/ignore:4078