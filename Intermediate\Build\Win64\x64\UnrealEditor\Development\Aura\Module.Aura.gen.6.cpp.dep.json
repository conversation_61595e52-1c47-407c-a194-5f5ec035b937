{"Version": "1.2", "Data": {"Source": "c:\\aura\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\aura\\module.aura.gen.6.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\auraeditor\\development\\unrealed\\sharedpch.unrealed.project.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\aura\\definitions.aura.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\aura.init.gen.cpp", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\awallcollisionmanager.gen.cpp", "c:\\aura\\source\\aura\\public\\awallcollisionmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\boxcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\boxcomponent.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\awallcollisionmanager.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\implementacao_automatizada.gen.cpp", "c:\\aura\\source\\aura\\public\\implementacao_automatizada.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\implementacao_automatizada.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}