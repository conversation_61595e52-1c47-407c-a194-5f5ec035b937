C:\Aura\Source\Aura\Public\ALaneManager.h
C:\Aura\Source\Aura\Public\AMinionWaveManager.h
C:\Aura\Source\Aura\Public\ADragonPrismalManager.h
C:\Aura\Source\Aura\Public\AProceduralMapGenerator.h
C:\Aura\Source\Aura\Public\AMapManager.h
C:\Aura\Source\Aura\Public\ARiverPrismalManager.h
C:\Aura\Source\Aura\Public\APCGWorldPartitionManager.h
C:\Aura\Source\Aura\Public\ABaronAuracronManager.h
C:\Aura\Source\Aura\Public\testes_precisao_geometrica.h
C:\Aura\Source\Aura\Public\APCGStreamingManager.h
C:\Aura\Source\Aura\Public\UPCGPerformanceProfiler.h
C:\Aura\Source\Aura\Public\Interfaces\RiverSystemInterface.h
C:\Aura\Source\Aura\Public\UPCGQualityValidator.h
C:\Aura\Source\Aura\Public\UPCGVersionManager.h
C:\Aura\Source\Aura\Public\Interfaces\LaneSystemInterface.h
C:\Aura\Source\Aura\Public\APCGLumenIntegrator.h
C:\Aura\Source\Aura\Public\APCGNaniteOptimizer.h
C:\Aura\Source\Aura\Public\APCGChaosIntegrator.h
C:\Aura\Source\Aura\Public\AGeometricValidator.h
C:\Aura\Source\Aura\Public\AWallCollisionManager.h
C:\Aura\Source\Aura\Public\APCGCacheManager.h
C:\Aura\Source\Aura\Public\implementacao_automatizada.h
