// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for Aura
#pragma once
#include "SharedDefinitions.Engine.Project.RTTI.Exceptions.ValApi.ValExpApi.Cpp20.h"
#undef AURA_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Aura
#define UE_TARGET_NAME Aura
#define UE_MODULE_NAME "Aura"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define PCG_API 
#define COMPUTEFRAMEWORK_API 
#define LANDSCAPE_API 
#define FOLIAGE_API 
#define READ_TARGET_ENABLED_PLUGINS_FROM_RECEIPT 0
#define LOAD_PLUGINS_FOR_TARGET_PLATFORMS 0
#define PROJECTS_API 
#define GEOMETRYFRAMEWORK_API 
#define INTERACTIVETOOLSFRAMEWORK_API 
#define MESHCONVERSION_API 
#define GEOMETRYSCRIPTINGCORE_API 
#define DYNAMICMESH_API 
#define GEOMETRYALGORITHMS_API 
#define CHAOSSOLVERENGINE_API 
#define DATAFLOWCORE_API 
#define DATAFLOWENGINE_API 
#define DATAFLOWSIMULATION_API 
#define UMG_API 
#define HTTP_PACKAGE 1
#define CURL_ENABLE_DEBUG_CALLBACK 1
#define CURL_ENABLE_NO_TIMEOUTS_OPTION 1
#define WITH_WINHTTP 1
#define UE_HTTP_CONNECTION_TIMEOUT_MAX_DEVIATION 0.5
#define UE_HTTP_ACTIVITY_TIMER_START_AFTER_RECEIVED_DATA 0
#define UE_HTTP_SUPPORT_LOCAL_SERVER 1
#define UE_HTTP_SUPPORT_UNIX_SOCKET 1
#define UE_HTTP_SUPPORT_VERB_CONNECT 1
#define HTTP_API 
#define MOVIESCENE_API 
#define TIMEMANAGEMENT_API 
#define UNIVERSALOBJECTLOCATOR_API 
#define MOVIESCENETRACKS_API 
#define CONSTRAINTS_API 
#define PROPERTYPATH_API 
#define WITH_RECAST 1
#define WITH_NAVMESH_SEGMENT_LINKS 1
#define WITH_NAVMESH_CLUSTER_LINKS 1
#define NAVIGATIONSYSTEM_API 
#define GEOMETRYCOLLECTIONENGINE_API 
#define FIELDSYSTEMENGINE_API 
#define ISMPOOL_API 
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define AIMODULE_API 
#define GAMEPLAYTASKS_API 
#define GAMEPLAYABILITIES_API 
#define DATAREGISTRY_API 
#define PROCEDURALMESHCOMPONENT_API 
#define AURA_API 
#define ENHANCEDINPUT_API 
