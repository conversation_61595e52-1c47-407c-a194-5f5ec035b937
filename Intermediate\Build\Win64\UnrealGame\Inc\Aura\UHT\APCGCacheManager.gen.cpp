// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "APCGCacheManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAPCGCacheManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGCacheManager();
AURA_API UClass* Z_Construct_UClass_APCGCacheManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGCachePriority();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGCacheState();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGCacheStorageType();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheEntry();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheOperationResult();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheStatistics();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_AActor();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGCachePriority *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGCachePriority;
static UEnum* EPCGCachePriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGCachePriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGCachePriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGCachePriority, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGCachePriority"));
	}
	return Z_Registration_Info_UEnum_EPCGCachePriority.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGCachePriority>()
{
	return EPCGCachePriority_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGCachePriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache priority levels\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EPCGCachePriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EPCGCachePriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EPCGCachePriority::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EPCGCachePriority::Medium" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache priority levels" },
#endif
		{ "VeryHigh.DisplayName", "Very High" },
		{ "VeryHigh.Name", "EPCGCachePriority::VeryHigh" },
		{ "VeryLow.DisplayName", "Very Low" },
		{ "VeryLow.Name", "EPCGCachePriority::VeryLow" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGCachePriority::VeryLow", (int64)EPCGCachePriority::VeryLow },
		{ "EPCGCachePriority::Low", (int64)EPCGCachePriority::Low },
		{ "EPCGCachePriority::Medium", (int64)EPCGCachePriority::Medium },
		{ "EPCGCachePriority::High", (int64)EPCGCachePriority::High },
		{ "EPCGCachePriority::VeryHigh", (int64)EPCGCachePriority::VeryHigh },
		{ "EPCGCachePriority::Critical", (int64)EPCGCachePriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGCachePriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGCachePriority",
	"EPCGCachePriority",
	Z_Construct_UEnum_Aura_EPCGCachePriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCachePriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCachePriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGCachePriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGCachePriority()
{
	if (!Z_Registration_Info_UEnum_EPCGCachePriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGCachePriority.InnerSingleton, Z_Construct_UEnum_Aura_EPCGCachePriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGCachePriority.InnerSingleton;
}
// ********** End Enum EPCGCachePriority ***********************************************************

// ********** Begin Enum EPCGCacheState ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGCacheState;
static UEnum* EPCGCacheState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGCacheState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGCacheState, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGCacheState"));
	}
	return Z_Registration_Info_UEnum_EPCGCacheState.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGCacheState>()
{
	return EPCGCacheState_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGCacheState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache entry states\n" },
#endif
		{ "Dirty.DisplayName", "Dirty" },
		{ "Dirty.Name", "EPCGCacheState::Dirty" },
		{ "Evicted.DisplayName", "Evicted" },
		{ "Evicted.Name", "EPCGCacheState::Evicted" },
		{ "Expired.DisplayName", "Expired" },
		{ "Expired.Name", "EPCGCacheState::Expired" },
		{ "Invalid.DisplayName", "Invalid" },
		{ "Invalid.Name", "EPCGCacheState::Invalid" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EPCGCacheState::Loading" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache entry states" },
#endif
		{ "Valid.DisplayName", "Valid" },
		{ "Valid.Name", "EPCGCacheState::Valid" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGCacheState::Invalid", (int64)EPCGCacheState::Invalid },
		{ "EPCGCacheState::Loading", (int64)EPCGCacheState::Loading },
		{ "EPCGCacheState::Valid", (int64)EPCGCacheState::Valid },
		{ "EPCGCacheState::Dirty", (int64)EPCGCacheState::Dirty },
		{ "EPCGCacheState::Expired", (int64)EPCGCacheState::Expired },
		{ "EPCGCacheState::Evicted", (int64)EPCGCacheState::Evicted },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGCacheState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGCacheState",
	"EPCGCacheState",
	Z_Construct_UEnum_Aura_EPCGCacheState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGCacheState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGCacheState()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGCacheState.InnerSingleton, Z_Construct_UEnum_Aura_EPCGCacheState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGCacheState.InnerSingleton;
}
// ********** End Enum EPCGCacheState **************************************************************

// ********** Begin Enum EPCGCacheStorageType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGCacheStorageType;
static UEnum* EPCGCacheStorageType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheStorageType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGCacheStorageType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGCacheStorageType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGCacheStorageType"));
	}
	return Z_Registration_Info_UEnum_EPCGCacheStorageType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGCacheStorageType>()
{
	return EPCGCacheStorageType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGCacheStorageType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache storage types\n" },
#endif
		{ "Compressed.DisplayName", "Compressed" },
		{ "Compressed.Name", "EPCGCacheStorageType::Compressed" },
		{ "Disk.DisplayName", "Disk" },
		{ "Disk.Name", "EPCGCacheStorageType::Disk" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EPCGCacheStorageType::Hybrid" },
		{ "Memory.DisplayName", "Memory" },
		{ "Memory.Name", "EPCGCacheStorageType::Memory" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
		{ "Network.DisplayName", "Network" },
		{ "Network.Name", "EPCGCacheStorageType::Network" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache storage types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGCacheStorageType::Memory", (int64)EPCGCacheStorageType::Memory },
		{ "EPCGCacheStorageType::Disk", (int64)EPCGCacheStorageType::Disk },
		{ "EPCGCacheStorageType::Hybrid", (int64)EPCGCacheStorageType::Hybrid },
		{ "EPCGCacheStorageType::Network", (int64)EPCGCacheStorageType::Network },
		{ "EPCGCacheStorageType::Compressed", (int64)EPCGCacheStorageType::Compressed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGCacheStorageType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGCacheStorageType",
	"EPCGCacheStorageType",
	Z_Construct_UEnum_Aura_EPCGCacheStorageType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheStorageType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheStorageType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGCacheStorageType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGCacheStorageType()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheStorageType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGCacheStorageType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGCacheStorageType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGCacheStorageType.InnerSingleton;
}
// ********** End Enum EPCGCacheStorageType ********************************************************

// ********** Begin Enum EPCGCacheEvictionPolicy ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy;
static UEnum* EPCGCacheEvictionPolicy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGCacheEvictionPolicy"));
	}
	return Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGCacheEvictionPolicy>()
{
	return EPCGCacheEvictionPolicy_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EPCGCacheEvictionPolicy::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache eviction policies\n" },
#endif
		{ "FIFO.DisplayName", "First In First Out" },
		{ "FIFO.Name", "EPCGCacheEvictionPolicy::FIFO" },
		{ "LFU.DisplayName", "Least Frequently Used" },
		{ "LFU.Name", "EPCGCacheEvictionPolicy::LFU" },
		{ "LRU.DisplayName", "Least Recently Used" },
		{ "LRU.Name", "EPCGCacheEvictionPolicy::LRU" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
		{ "Priority.DisplayName", "Priority Based" },
		{ "Priority.Name", "EPCGCacheEvictionPolicy::Priority" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EPCGCacheEvictionPolicy::Random" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache eviction policies" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGCacheEvictionPolicy::LRU", (int64)EPCGCacheEvictionPolicy::LRU },
		{ "EPCGCacheEvictionPolicy::LFU", (int64)EPCGCacheEvictionPolicy::LFU },
		{ "EPCGCacheEvictionPolicy::FIFO", (int64)EPCGCacheEvictionPolicy::FIFO },
		{ "EPCGCacheEvictionPolicy::Random", (int64)EPCGCacheEvictionPolicy::Random },
		{ "EPCGCacheEvictionPolicy::Priority", (int64)EPCGCacheEvictionPolicy::Priority },
		{ "EPCGCacheEvictionPolicy::Adaptive", (int64)EPCGCacheEvictionPolicy::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGCacheEvictionPolicy",
	"EPCGCacheEvictionPolicy",
	Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy.InnerSingleton, Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy.InnerSingleton;
}
// ********** End Enum EPCGCacheEvictionPolicy *****************************************************

// ********** Begin Enum EPCGCacheCompressionLevel *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGCacheCompressionLevel;
static UEnum* EPCGCacheCompressionLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheCompressionLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGCacheCompressionLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGCacheCompressionLevel"));
	}
	return Z_Registration_Info_UEnum_EPCGCacheCompressionLevel.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGCacheCompressionLevel>()
{
	return EPCGCacheCompressionLevel_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EPCGCacheCompressionLevel::Adaptive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EPCGCacheCompressionLevel::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache compression levels\n" },
#endif
		{ "Fast.DisplayName", "Fast" },
		{ "Fast.Name", "EPCGCacheCompressionLevel::Fast" },
		{ "Maximum.DisplayName", "Maximum" },
		{ "Maximum.Name", "EPCGCacheCompressionLevel::Maximum" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EPCGCacheCompressionLevel::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache compression levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGCacheCompressionLevel::None", (int64)EPCGCacheCompressionLevel::None },
		{ "EPCGCacheCompressionLevel::Fast", (int64)EPCGCacheCompressionLevel::Fast },
		{ "EPCGCacheCompressionLevel::Balanced", (int64)EPCGCacheCompressionLevel::Balanced },
		{ "EPCGCacheCompressionLevel::Maximum", (int64)EPCGCacheCompressionLevel::Maximum },
		{ "EPCGCacheCompressionLevel::Adaptive", (int64)EPCGCacheCompressionLevel::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGCacheCompressionLevel",
	"EPCGCacheCompressionLevel",
	Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel()
{
	if (!Z_Registration_Info_UEnum_EPCGCacheCompressionLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGCacheCompressionLevel.InnerSingleton, Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGCacheCompressionLevel.InnerSingleton;
}
// ********** End Enum EPCGCacheCompressionLevel ***************************************************

// ********** Begin ScriptStruct FPCGCacheConfig ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGCacheConfig;
class UScriptStruct* FPCGCacheConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGCacheConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGCacheConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGCacheConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGCacheConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache configuration structure\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache configuration structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryCacheSizeMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryEntries_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryEvictionThreshold_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiskCachePath_MetaData[] = {
		{ "Category", "Disk" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Disk settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Disk settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDiskCacheSizeMB_MetaData[] = {
		{ "Category", "Disk" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDiskCache_MetaData[] = {
		{ "Category", "Disk" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionLevel_MetaData[] = {
		{ "Category", "Compression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Compression settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compression settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCompression_MetaData[] = {
		{ "Category", "Compression" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionRatio_MetaData[] = {
		{ "Category", "Compression" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvictionPolicy_MetaData[] = {
		{ "Category", "Eviction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eviction settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eviction settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvictionCheckInterval_MetaData[] = {
		{ "Category", "Eviction" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntryExpirationTime_MetaData[] = {
		{ "Category", "Eviction" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncLoading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePrefetching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentOperations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNetworkCache_MetaData[] = {
		{ "Category", "Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Network settings\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkCacheEndpoint_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkTimeout_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMemoryCacheSizeMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMemoryEntries;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryEvictionThreshold;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiskCachePath;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxDiskCacheSizeMB;
	static void NewProp_bEnableDiskCache_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDiskCache;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompressionLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CompressionLevel;
	static void NewProp_bEnableCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCompression;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompressionRatio;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EvictionPolicy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EvictionPolicy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvictionCheckInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EntryExpirationTime;
	static void NewProp_bEnableAsyncLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncLoading;
	static void NewProp_bEnablePrefetching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePrefetching;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentOperations;
	static void NewProp_bEnableNetworkCache_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNetworkCache;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NetworkCacheEndpoint;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkTimeout;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGCacheConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxMemoryCacheSizeMB = { "MaxMemoryCacheSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, MaxMemoryCacheSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryCacheSizeMB_MetaData), NewProp_MaxMemoryCacheSizeMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxMemoryEntries = { "MaxMemoryEntries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, MaxMemoryEntries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryEntries_MetaData), NewProp_MaxMemoryEntries_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MemoryEvictionThreshold = { "MemoryEvictionThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, MemoryEvictionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryEvictionThreshold_MetaData), NewProp_MemoryEvictionThreshold_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_DiskCachePath = { "DiskCachePath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, DiskCachePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiskCachePath_MetaData), NewProp_DiskCachePath_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxDiskCacheSizeMB = { "MaxDiskCacheSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, MaxDiskCacheSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDiskCacheSizeMB_MetaData), NewProp_MaxDiskCacheSizeMB_MetaData) };
void Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableDiskCache_SetBit(void* Obj)
{
	((FPCGCacheConfig*)Obj)->bEnableDiskCache = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableDiskCache = { "bEnableDiskCache", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGCacheConfig), &Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableDiskCache_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDiskCache_MetaData), NewProp_bEnableDiskCache_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_CompressionLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_CompressionLevel = { "CompressionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, CompressionLevel), Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionLevel_MetaData), NewProp_CompressionLevel_MetaData) }; // 2324820021
void Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableCompression_SetBit(void* Obj)
{
	((FPCGCacheConfig*)Obj)->bEnableCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableCompression = { "bEnableCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGCacheConfig), &Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCompression_MetaData), NewProp_bEnableCompression_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_CompressionRatio = { "CompressionRatio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, CompressionRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionRatio_MetaData), NewProp_CompressionRatio_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EvictionPolicy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EvictionPolicy = { "EvictionPolicy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, EvictionPolicy), Z_Construct_UEnum_Aura_EPCGCacheEvictionPolicy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvictionPolicy_MetaData), NewProp_EvictionPolicy_MetaData) }; // 1583555971
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EvictionCheckInterval = { "EvictionCheckInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, EvictionCheckInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvictionCheckInterval_MetaData), NewProp_EvictionCheckInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EntryExpirationTime = { "EntryExpirationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, EntryExpirationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntryExpirationTime_MetaData), NewProp_EntryExpirationTime_MetaData) };
void Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableAsyncLoading_SetBit(void* Obj)
{
	((FPCGCacheConfig*)Obj)->bEnableAsyncLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableAsyncLoading = { "bEnableAsyncLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGCacheConfig), &Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableAsyncLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncLoading_MetaData), NewProp_bEnableAsyncLoading_MetaData) };
void Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnablePrefetching_SetBit(void* Obj)
{
	((FPCGCacheConfig*)Obj)->bEnablePrefetching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnablePrefetching = { "bEnablePrefetching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGCacheConfig), &Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnablePrefetching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePrefetching_MetaData), NewProp_bEnablePrefetching_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxConcurrentOperations = { "MaxConcurrentOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, MaxConcurrentOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentOperations_MetaData), NewProp_MaxConcurrentOperations_MetaData) };
void Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableNetworkCache_SetBit(void* Obj)
{
	((FPCGCacheConfig*)Obj)->bEnableNetworkCache = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableNetworkCache = { "bEnableNetworkCache", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGCacheConfig), &Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableNetworkCache_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNetworkCache_MetaData), NewProp_bEnableNetworkCache_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_NetworkCacheEndpoint = { "NetworkCacheEndpoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, NetworkCacheEndpoint), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkCacheEndpoint_MetaData), NewProp_NetworkCacheEndpoint_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_NetworkTimeout = { "NetworkTimeout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheConfig, NetworkTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkTimeout_MetaData), NewProp_NetworkTimeout_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxMemoryCacheSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxMemoryEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MemoryEvictionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_DiskCachePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxDiskCacheSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableDiskCache,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_CompressionLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_CompressionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_CompressionRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EvictionPolicy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EvictionPolicy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EvictionCheckInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_EntryExpirationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableAsyncLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnablePrefetching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_MaxConcurrentOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_bEnableNetworkCache,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_NetworkCacheEndpoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewProp_NetworkTimeout,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGCacheConfig",
	Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::PropPointers),
	sizeof(FPCGCacheConfig),
	alignof(FPCGCacheConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGCacheConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGCacheConfig *****************************************************

// ********** Begin ScriptStruct FPCGCacheEntry ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGCacheEntry;
class UScriptStruct* FPCGCacheEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGCacheEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGCacheEntry, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGCacheEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGCacheEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache entry metadata\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache entry metadata" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntryKey_MetaData[] = {
		{ "Category", "Entry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Entry identification\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entry identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntryType_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority and access\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority and access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Access" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Access" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastModificationTime_MetaData[] = {
		{ "Category", "Access" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccessCount_MetaData[] = {
		{ "Category", "Access" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SizeInBytes_MetaData[] = {
		{ "Category", "Storage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Size and storage\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Size and storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressedSizeInBytes_MetaData[] = {
		{ "Category", "Storage" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StorageType_MetaData[] = {
		{ "Category", "Storage" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiskPath_MetaData[] = {
		{ "Category", "Storage" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChecksumHash_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation and dependencies\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation and dependencies" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dependencies_MetaData[] = {
		{ "Category", "Dependencies" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dependents_MetaData[] = {
		{ "Category", "Dependencies" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadTimeMs_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressionTimeMs_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DecompressionTimeMs_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EntryKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EntryType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastModificationTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AccessCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SizeInBytes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompressedSizeInBytes;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StorageType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StorageType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DiskPath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChecksumHash;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Dependencies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Dependencies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Dependents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Dependents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompressionTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DecompressionTimeMs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGCacheEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_EntryKey = { "EntryKey", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, EntryKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntryKey_MetaData), NewProp_EntryKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_EntryType = { "EntryType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, EntryType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntryType_MetaData), NewProp_EntryType_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, State), Z_Construct_UEnum_Aura_EPCGCacheState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 544656260
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, Priority), Z_Construct_UEnum_Aura_EPCGCachePriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 1356705782
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_LastModificationTime = { "LastModificationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, LastModificationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastModificationTime_MetaData), NewProp_LastModificationTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_AccessCount = { "AccessCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, AccessCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccessCount_MetaData), NewProp_AccessCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_SizeInBytes = { "SizeInBytes", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, SizeInBytes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SizeInBytes_MetaData), NewProp_SizeInBytes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_CompressedSizeInBytes = { "CompressedSizeInBytes", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, CompressedSizeInBytes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressedSizeInBytes_MetaData), NewProp_CompressedSizeInBytes_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_StorageType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_StorageType = { "StorageType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, StorageType), Z_Construct_UEnum_Aura_EPCGCacheStorageType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StorageType_MetaData), NewProp_StorageType_MetaData) }; // 630467440
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_DiskPath = { "DiskPath", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, DiskPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiskPath_MetaData), NewProp_DiskPath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_ChecksumHash = { "ChecksumHash", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, ChecksumHash), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChecksumHash_MetaData), NewProp_ChecksumHash_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependencies_Inner = { "Dependencies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependencies = { "Dependencies", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, Dependencies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dependencies_MetaData), NewProp_Dependencies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependents_Inner = { "Dependents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependents = { "Dependents", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, Dependents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dependents_MetaData), NewProp_Dependents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_LoadTimeMs = { "LoadTimeMs", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, LoadTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadTimeMs_MetaData), NewProp_LoadTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_CompressionTimeMs = { "CompressionTimeMs", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, CompressionTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressionTimeMs_MetaData), NewProp_CompressionTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_DecompressionTimeMs = { "DecompressionTimeMs", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheEntry, DecompressionTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DecompressionTimeMs_MetaData), NewProp_DecompressionTimeMs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_EntryKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_EntryType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_LastAccessTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_LastModificationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_AccessCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_SizeInBytes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_CompressedSizeInBytes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_StorageType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_StorageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_DiskPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_ChecksumHash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependencies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependencies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_Dependents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_LoadTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_CompressionTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewProp_DecompressionTimeMs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGCacheEntry",
	Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::PropPointers),
	sizeof(FPCGCacheEntry),
	alignof(FPCGCacheEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGCacheEntry.InnerSingleton, Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheEntry.InnerSingleton;
}
// ********** End ScriptStruct FPCGCacheEntry ******************************************************

// ********** Begin ScriptStruct FPCGCacheStatistics ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGCacheStatistics;
class UScriptStruct* FPCGCacheStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGCacheStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGCacheStatistics, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGCacheStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalRequests_MetaData[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Hit/Miss statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Hit/Miss statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheHits_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheMisses_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitRatio_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsedMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryAvailableMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryEntryCount_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiskUsedMB_MetaData[] = {
		{ "Category", "Disk" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Disk statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Disk statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiskAvailableMB_MetaData[] = {
		{ "Category", "Disk" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiskEntryCount_MetaData[] = {
		{ "Category", "Disk" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadTimeMs_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageCompressionRatio_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvictedEntries_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpiredEntries_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalRequests;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheHits;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheMisses;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HitRatio;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryUsedMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryAvailableMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryEntryCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DiskUsedMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DiskAvailableMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DiskEntryCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadTimeMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageCompressionRatio;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EvictedEntries;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExpiredEntries;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGCacheStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_TotalRequests = { "TotalRequests", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, TotalRequests), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalRequests_MetaData), NewProp_TotalRequests_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_CacheHits = { "CacheHits", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, CacheHits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheHits_MetaData), NewProp_CacheHits_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_CacheMisses = { "CacheMisses", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, CacheMisses), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheMisses_MetaData), NewProp_CacheMisses_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_HitRatio = { "HitRatio", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, HitRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitRatio_MetaData), NewProp_HitRatio_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_MemoryUsedMB = { "MemoryUsedMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, MemoryUsedMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsedMB_MetaData), NewProp_MemoryUsedMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_MemoryAvailableMB = { "MemoryAvailableMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, MemoryAvailableMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryAvailableMB_MetaData), NewProp_MemoryAvailableMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_MemoryEntryCount = { "MemoryEntryCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, MemoryEntryCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryEntryCount_MetaData), NewProp_MemoryEntryCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_DiskUsedMB = { "DiskUsedMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, DiskUsedMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiskUsedMB_MetaData), NewProp_DiskUsedMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_DiskAvailableMB = { "DiskAvailableMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, DiskAvailableMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiskAvailableMB_MetaData), NewProp_DiskAvailableMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_DiskEntryCount = { "DiskEntryCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, DiskEntryCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiskEntryCount_MetaData), NewProp_DiskEntryCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_AverageLoadTimeMs = { "AverageLoadTimeMs", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, AverageLoadTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadTimeMs_MetaData), NewProp_AverageLoadTimeMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_AverageCompressionRatio = { "AverageCompressionRatio", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, AverageCompressionRatio), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageCompressionRatio_MetaData), NewProp_AverageCompressionRatio_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_EvictedEntries = { "EvictedEntries", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, EvictedEntries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvictedEntries_MetaData), NewProp_EvictedEntries_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_ExpiredEntries = { "ExpiredEntries", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheStatistics, ExpiredEntries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpiredEntries_MetaData), NewProp_ExpiredEntries_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_TotalRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_CacheHits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_CacheMisses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_HitRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_MemoryUsedMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_MemoryAvailableMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_MemoryEntryCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_DiskUsedMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_DiskAvailableMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_DiskEntryCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_AverageLoadTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_AverageCompressionRatio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_EvictedEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewProp_ExpiredEntries,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGCacheStatistics",
	Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::PropPointers),
	sizeof(FPCGCacheStatistics),
	alignof(FPCGCacheStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGCacheStatistics.InnerSingleton, Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheStatistics.InnerSingleton;
}
// ********** End ScriptStruct FPCGCacheStatistics *************************************************

// ********** Begin ScriptStruct FPCGCacheOperationResult ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult;
class UScriptStruct* FPCGCacheOperationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGCacheOperationResult, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGCacheOperationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache operation result\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache operation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationTimeMs_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataSizeBytes_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFromCache_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OperationTimeMs;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataSizeBytes;
	static void NewProp_bFromCache_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromCache;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGCacheOperationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FPCGCacheOperationResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGCacheOperationResult), &Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheOperationResult, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_OperationTimeMs = { "OperationTimeMs", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheOperationResult, OperationTimeMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationTimeMs_MetaData), NewProp_OperationTimeMs_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_DataSizeBytes = { "DataSizeBytes", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGCacheOperationResult, DataSizeBytes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataSizeBytes_MetaData), NewProp_DataSizeBytes_MetaData) };
void Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bFromCache_SetBit(void* Obj)
{
	((FPCGCacheOperationResult*)Obj)->bFromCache = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bFromCache = { "bFromCache", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGCacheOperationResult), &Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bFromCache_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFromCache_MetaData), NewProp_bFromCache_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_OperationTimeMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_DataSizeBytes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewProp_bFromCache,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGCacheOperationResult",
	Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::PropPointers),
	sizeof(FPCGCacheOperationResult),
	alignof(FPCGCacheOperationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGCacheOperationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult.InnerSingleton, Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult.InnerSingleton;
}
// ********** End ScriptStruct FPCGCacheOperationResult ********************************************

// ********** Begin Delegate FOnCacheEntryLoaded ***************************************************
struct Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnCacheEntryLoaded_Parms
	{
		FString EntryKey;
		FPCGCacheOperationResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate declarations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate declarations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntryKey_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EntryKey;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::NewProp_EntryKey = { "EntryKey", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnCacheEntryLoaded_Parms, EntryKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntryKey_MetaData), NewProp_EntryKey_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnCacheEntryLoaded_Parms, Result), Z_Construct_UScriptStruct_FPCGCacheOperationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 526902159
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::NewProp_EntryKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnCacheEntryLoaded__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::_Script_Aura_eventOnCacheEntryLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::_Script_Aura_eventOnCacheEntryLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCacheEntryLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnCacheEntryLoaded, const FString& EntryKey, FPCGCacheOperationResult const& Result)
{
	struct _Script_Aura_eventOnCacheEntryLoaded_Parms
	{
		FString EntryKey;
		FPCGCacheOperationResult Result;
	};
	_Script_Aura_eventOnCacheEntryLoaded_Parms Parms;
	Parms.EntryKey=EntryKey;
	Parms.Result=Result;
	OnCacheEntryLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCacheEntryLoaded *****************************************************

// ********** Begin Delegate FOnCacheEntryEvicted **************************************************
struct Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnCacheEntryEvicted_Parms
	{
		FString EntryKey;
		EPCGCachePriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntryKey_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EntryKey;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::NewProp_EntryKey = { "EntryKey", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnCacheEntryEvicted_Parms, EntryKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntryKey_MetaData), NewProp_EntryKey_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnCacheEntryEvicted_Parms, Priority), Z_Construct_UEnum_Aura_EPCGCachePriority, METADATA_PARAMS(0, nullptr) }; // 1356705782
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::NewProp_EntryKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnCacheEntryEvicted__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::_Script_Aura_eventOnCacheEntryEvicted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::_Script_Aura_eventOnCacheEntryEvicted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCacheEntryEvicted_DelegateWrapper(const FMulticastScriptDelegate& OnCacheEntryEvicted, const FString& EntryKey, EPCGCachePriority Priority)
{
	struct _Script_Aura_eventOnCacheEntryEvicted_Parms
	{
		FString EntryKey;
		EPCGCachePriority Priority;
	};
	_Script_Aura_eventOnCacheEntryEvicted_Parms Parms;
	Parms.EntryKey=EntryKey;
	Parms.Priority=Priority;
	OnCacheEntryEvicted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCacheEntryEvicted ****************************************************

// ********** Begin Delegate FOnCacheStatisticsUpdated *********************************************
struct Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnCacheStatisticsUpdated_Parms
	{
		FPCGCacheStatistics Statistics;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Statistics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Statistics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::NewProp_Statistics = { "Statistics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnCacheStatisticsUpdated_Parms, Statistics), Z_Construct_UScriptStruct_FPCGCacheStatistics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Statistics_MetaData), NewProp_Statistics_MetaData) }; // 1277078403
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::NewProp_Statistics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnCacheStatisticsUpdated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::_Script_Aura_eventOnCacheStatisticsUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::_Script_Aura_eventOnCacheStatisticsUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCacheStatisticsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnCacheStatisticsUpdated, FPCGCacheStatistics const& Statistics)
{
	struct _Script_Aura_eventOnCacheStatisticsUpdated_Parms
	{
		FPCGCacheStatistics Statistics;
	};
	_Script_Aura_eventOnCacheStatisticsUpdated_Parms Parms;
	Parms.Statistics=Statistics;
	OnCacheStatisticsUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCacheStatisticsUpdated ***********************************************

// ********** Begin Delegate FOnCacheError *********************************************************
struct Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnCacheError_Parms
	{
		FString ErrorMessage;
		FString EntryKey;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntryKey_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EntryKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnCacheError_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::NewProp_EntryKey = { "EntryKey", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnCacheError_Parms, EntryKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntryKey_MetaData), NewProp_EntryKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::NewProp_EntryKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnCacheError__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::_Script_Aura_eventOnCacheError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::_Script_Aura_eventOnCacheError_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCacheError_DelegateWrapper(const FMulticastScriptDelegate& OnCacheError, const FString& ErrorMessage, const FString& EntryKey)
{
	struct _Script_Aura_eventOnCacheError_Parms
	{
		FString ErrorMessage;
		FString EntryKey;
	};
	_Script_Aura_eventOnCacheError_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	Parms.EntryKey=EntryKey;
	OnCacheError.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCacheError ***********************************************************

// ********** Begin Class APCGCacheManager Function AddEntryDependency *****************************
struct Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics
{
	struct PCGCacheManager_eventAddEntryDependency_Parms
	{
		FString Key;
		FString DependencyKey;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Entry Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DependencyKey_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DependencyKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventAddEntryDependency_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::NewProp_DependencyKey = { "DependencyKey", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventAddEntryDependency_Parms, DependencyKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DependencyKey_MetaData), NewProp_DependencyKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::NewProp_DependencyKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "AddEntryDependency", Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::PCGCacheManager_eventAddEntryDependency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::PCGCacheManager_eventAddEntryDependency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_AddEntryDependency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_AddEntryDependency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execAddEntryDependency)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FStrProperty,Z_Param_DependencyKey);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddEntryDependency(Z_Param_Key,Z_Param_DependencyKey);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function AddEntryDependency *******************************

// ********** Begin Class APCGCacheManager Function CleanupDiskCache *******************************
struct Z_Construct_UFunction_APCGCacheManager_CleanupDiskCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Disk Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_CleanupDiskCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "CleanupDiskCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_CleanupDiskCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_CleanupDiskCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGCacheManager_CleanupDiskCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_CleanupDiskCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execCleanupDiskCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupDiskCache();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function CleanupDiskCache *********************************

// ********** Begin Class APCGCacheManager Function ClearCache *************************************
struct Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics
{
	struct PCGCacheManager_eventClearCache_Parms
	{
		EPCGCacheStorageType StorageType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Management" },
		{ "CPP_Default_StorageType", "Memory" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_StorageType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StorageType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::NewProp_StorageType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::NewProp_StorageType = { "StorageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventClearCache_Parms, StorageType), Z_Construct_UEnum_Aura_EPCGCacheStorageType, METADATA_PARAMS(0, nullptr) }; // 630467440
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::NewProp_StorageType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::NewProp_StorageType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "ClearCache", Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::PCGCacheManager_eventClearCache_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::PCGCacheManager_eventClearCache_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_ClearCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_ClearCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execClearCache)
{
	P_GET_ENUM(EPCGCacheStorageType,Z_Param_StorageType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCache(EPCGCacheStorageType(Z_Param_StorageType));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function ClearCache ***************************************

// ********** Begin Class APCGCacheManager Function CompressData ***********************************
struct Z_Construct_UFunction_APCGCacheManager_CompressData_Statics
{
	struct PCGCacheManager_eventCompressData_Parms
	{
		TArray<uint8> Data;
		EPCGCacheCompressionLevel Level;
		TArray<uint8> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Compression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Compression and Serialization\n" },
#endif
		{ "CPP_Default_Level", "Balanced" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compression and Serialization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Data_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Data;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Level_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Level;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Data_Inner = { "Data", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventCompressData_Parms, Data), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Level_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventCompressData_Parms, Level), Z_Construct_UEnum_Aura_EPCGCacheCompressionLevel, METADATA_PARAMS(0, nullptr) }; // 2324820021
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventCompressData_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Data_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Data,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Level_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "CompressData", Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::PCGCacheManager_eventCompressData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::PCGCacheManager_eventCompressData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_CompressData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_CompressData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execCompressData)
{
	P_GET_TARRAY_REF(uint8,Z_Param_Out_Data);
	P_GET_ENUM(EPCGCacheCompressionLevel,Z_Param_Level);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<uint8>*)Z_Param__Result=P_THIS->CompressData(Z_Param_Out_Data,EPCGCacheCompressionLevel(Z_Param_Level));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function CompressData *************************************

// ********** Begin Class APCGCacheManager Function DecompressData *********************************
struct Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics
{
	struct PCGCacheManager_eventDecompressData_Parms
	{
		TArray<uint8> CompressedData;
		TArray<uint8> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Compression" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressedData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompressedData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompressedData;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_CompressedData_Inner = { "CompressedData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_CompressedData = { "CompressedData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventDecompressData_Parms, CompressedData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressedData_MetaData), NewProp_CompressedData_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventDecompressData_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_CompressedData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_CompressedData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "DecompressData", Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::PCGCacheManager_eventDecompressData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::PCGCacheManager_eventDecompressData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_DecompressData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_DecompressData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execDecompressData)
{
	P_GET_TARRAY_REF(uint8,Z_Param_Out_CompressedData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<uint8>*)Z_Param__Result=P_THIS->DecompressData(Z_Param_Out_CompressedData);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function DecompressData ***********************************

// ********** Begin Class APCGCacheManager Function DownloadFromNetwork ****************************
struct Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics
{
	struct PCGCacheManager_eventDownloadFromNetwork_Parms
	{
		FString Key;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventDownloadFromNetwork_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::NewProp_Key,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "DownloadFromNetwork", Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::PCGCacheManager_eventDownloadFromNetwork_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::PCGCacheManager_eventDownloadFromNetwork_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execDownloadFromNetwork)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DownloadFromNetwork(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function DownloadFromNetwork ******************************

// ********** Begin Class APCGCacheManager Function EnableCache ************************************
struct Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics
{
	struct PCGCacheManager_eventEnableCache_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((PCGCacheManager_eventEnableCache_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventEnableCache_Parms), &Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "EnableCache", Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::PCGCacheManager_eventEnableCache_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::PCGCacheManager_eventEnableCache_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_EnableCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_EnableCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execEnableCache)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableCache(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function EnableCache **************************************

// ********** Begin Class APCGCacheManager Function EvictByPriority ********************************
struct Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics
{
	struct PCGCacheManager_eventEvictByPriority_Parms
	{
		EPCGCachePriority MaxPriority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Eviction" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MaxPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MaxPriority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::NewProp_MaxPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::NewProp_MaxPriority = { "MaxPriority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventEvictByPriority_Parms, MaxPriority), Z_Construct_UEnum_Aura_EPCGCachePriority, METADATA_PARAMS(0, nullptr) }; // 1356705782
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::NewProp_MaxPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::NewProp_MaxPriority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "EvictByPriority", Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::PCGCacheManager_eventEvictByPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::PCGCacheManager_eventEvictByPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_EvictByPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_EvictByPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execEvictByPriority)
{
	P_GET_ENUM(EPCGCachePriority,Z_Param_MaxPriority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EvictByPriority(EPCGCachePriority(Z_Param_MaxPriority));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function EvictByPriority **********************************

// ********** Begin Class APCGCacheManager Function EvictExpiredEntries ****************************
struct Z_Construct_UFunction_APCGCacheManager_EvictExpiredEntries_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Eviction" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_EvictExpiredEntries_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "EvictExpiredEntries", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictExpiredEntries_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_EvictExpiredEntries_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGCacheManager_EvictExpiredEntries()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_EvictExpiredEntries_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execEvictExpiredEntries)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EvictExpiredEntries();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function EvictExpiredEntries ******************************

// ********** Begin Class APCGCacheManager Function EvictLeastFrequentlyUsed ***********************
struct Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics
{
	struct PCGCacheManager_eventEvictLeastFrequentlyUsed_Parms
	{
		int32 Count;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Eviction" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventEvictLeastFrequentlyUsed_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::NewProp_Count,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "EvictLeastFrequentlyUsed", Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::PCGCacheManager_eventEvictLeastFrequentlyUsed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::PCGCacheManager_eventEvictLeastFrequentlyUsed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execEvictLeastFrequentlyUsed)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EvictLeastFrequentlyUsed(Z_Param_Count);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function EvictLeastFrequentlyUsed *************************

// ********** Begin Class APCGCacheManager Function EvictLeastRecentlyUsed *************************
struct Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics
{
	struct PCGCacheManager_eventEvictLeastRecentlyUsed_Parms
	{
		int32 Count;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Eviction" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventEvictLeastRecentlyUsed_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::NewProp_Count,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "EvictLeastRecentlyUsed", Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::PCGCacheManager_eventEvictLeastRecentlyUsed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::PCGCacheManager_eventEvictLeastRecentlyUsed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execEvictLeastRecentlyUsed)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EvictLeastRecentlyUsed(Z_Param_Count);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function EvictLeastRecentlyUsed ***************************

// ********** Begin Class APCGCacheManager Function ExportStatistics *******************************
struct Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics
{
	struct PCGCacheManager_eventExportStatistics_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventExportStatistics_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "ExportStatistics", Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::PCGCacheManager_eventExportStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::PCGCacheManager_eventExportStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_ExportStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_ExportStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execExportStatistics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportStatistics(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function ExportStatistics *********************************

// ********** Begin Class APCGCacheManager Function ForceEviction **********************************
struct Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics
{
	struct PCGCacheManager_eventForceEviction_Parms
	{
		int32 TargetMemoryMB;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Eviction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eviction and Cleanup\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eviction and Cleanup" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetMemoryMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::NewProp_TargetMemoryMB = { "TargetMemoryMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventForceEviction_Parms, TargetMemoryMB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::NewProp_TargetMemoryMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "ForceEviction", Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::PCGCacheManager_eventForceEviction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::PCGCacheManager_eventForceEviction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_ForceEviction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_ForceEviction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execForceEviction)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetMemoryMB);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceEviction(Z_Param_TargetMemoryMB);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function ForceEviction ************************************

// ********** Begin Class APCGCacheManager Function GetAllEntries **********************************
struct Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics
{
	struct PCGCacheManager_eventGetAllEntries_Parms
	{
		TArray<FPCGCacheEntry> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Entry Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGCacheEntry, METADATA_PARAMS(0, nullptr) }; // 2179562633
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetAllEntries_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2179562633
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetAllEntries", Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::PCGCacheManager_eventGetAllEntries_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::PCGCacheManager_eventGetAllEntries_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetAllEntries()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetAllEntries_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetAllEntries)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGCacheEntry>*)Z_Param__Result=P_THIS->GetAllEntries();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetAllEntries ************************************

// ********** Begin Class APCGCacheManager Function GetCacheHitRate ********************************
struct Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics
{
	struct PCGCacheManager_eventGetCacheHitRate_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetCacheHitRate_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetCacheHitRate", Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::PCGCacheManager_eventGetCacheHitRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::PCGCacheManager_eventGetCacheHitRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetCacheHitRate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCacheHitRate();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetCacheHitRate **********************************

// ********** Begin Class APCGCacheManager Function GetCacheStatistics *****************************
struct Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics
{
	struct PCGCacheManager_eventGetCacheStatistics_Parms
	{
		FPCGCacheStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and Monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and Monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetCacheStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGCacheStatistics, METADATA_PARAMS(0, nullptr) }; // 1277078403
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetCacheStatistics", Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::PCGCacheManager_eventGetCacheStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::PCGCacheManager_eventGetCacheStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetCacheStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGCacheStatistics*)Z_Param__Result=P_THIS->GetCacheStatistics();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetCacheStatistics *******************************

// ********** Begin Class APCGCacheManager Function GetCompressionRatio ****************************
struct Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics
{
	struct PCGCacheManager_eventGetCompressionRatio_Parms
	{
		TArray<uint8> OriginalData;
		TArray<uint8> CompressedData;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Compression" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalData_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressedData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OriginalData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OriginalData;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompressedData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompressedData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_OriginalData_Inner = { "OriginalData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_OriginalData = { "OriginalData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetCompressionRatio_Parms, OriginalData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalData_MetaData), NewProp_OriginalData_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_CompressedData_Inner = { "CompressedData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_CompressedData = { "CompressedData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetCompressionRatio_Parms, CompressedData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressedData_MetaData), NewProp_CompressedData_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetCompressionRatio_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_OriginalData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_OriginalData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_CompressedData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_CompressedData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetCompressionRatio", Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::PCGCacheManager_eventGetCompressionRatio_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::PCGCacheManager_eventGetCompressionRatio_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetCompressionRatio)
{
	P_GET_TARRAY_REF(uint8,Z_Param_Out_OriginalData);
	P_GET_TARRAY_REF(uint8,Z_Param_Out_CompressedData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCompressionRatio(Z_Param_Out_OriginalData,Z_Param_Out_CompressedData);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetCompressionRatio ******************************

// ********** Begin Class APCGCacheManager Function GetDiskCacheSize *******************************
struct Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics
{
	struct PCGCacheManager_eventGetDiskCacheSize_Parms
	{
		int64 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Disk Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FInt64PropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetDiskCacheSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetDiskCacheSize", Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::PCGCacheManager_eventGetDiskCacheSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::PCGCacheManager_eventGetDiskCacheSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetDiskCacheSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int64*)Z_Param__Result=P_THIS->GetDiskCacheSize();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetDiskCacheSize *********************************

// ********** Begin Class APCGCacheManager Function GetDiskUsage ***********************************
struct Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics
{
	struct PCGCacheManager_eventGetDiskUsage_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetDiskUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetDiskUsage", Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::PCGCacheManager_eventGetDiskUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::PCGCacheManager_eventGetDiskUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetDiskUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetDiskUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetDiskUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetDiskUsage();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetDiskUsage *************************************

// ********** Begin Class APCGCacheManager Function GetEntriesByPriority ***************************
struct Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics
{
	struct PCGCacheManager_eventGetEntriesByPriority_Parms
	{
		EPCGCachePriority Priority;
		TArray<FPCGCacheEntry> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Entry Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetEntriesByPriority_Parms, Priority), Z_Construct_UEnum_Aura_EPCGCachePriority, METADATA_PARAMS(0, nullptr) }; // 1356705782
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGCacheEntry, METADATA_PARAMS(0, nullptr) }; // 2179562633
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetEntriesByPriority_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2179562633
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetEntriesByPriority", Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::PCGCacheManager_eventGetEntriesByPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::PCGCacheManager_eventGetEntriesByPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetEntriesByPriority)
{
	P_GET_ENUM(EPCGCachePriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGCacheEntry>*)Z_Param__Result=P_THIS->GetEntriesByPriority(EPCGCachePriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetEntriesByPriority *****************************

// ********** Begin Class APCGCacheManager Function GetEntriesByType *******************************
struct Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics
{
	struct PCGCacheManager_eventGetEntriesByType_Parms
	{
		FString Type;
		TArray<FPCGCacheEntry> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Entry Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetEntriesByType_Parms, Type), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGCacheEntry, METADATA_PARAMS(0, nullptr) }; // 2179562633
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetEntriesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2179562633
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetEntriesByType", Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::PCGCacheManager_eventGetEntriesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::PCGCacheManager_eventGetEntriesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetEntriesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetEntriesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetEntriesByType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Type);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGCacheEntry>*)Z_Param__Result=P_THIS->GetEntriesByType(Z_Param_Type);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetEntriesByType *********************************

// ********** Begin Class APCGCacheManager Function GetEntryInfo ***********************************
struct Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics
{
	struct PCGCacheManager_eventGetEntryInfo_Parms
	{
		FString Key;
		FPCGCacheEntry ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Entry Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Entry Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entry Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetEntryInfo_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetEntryInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGCacheEntry, METADATA_PARAMS(0, nullptr) }; // 2179562633
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetEntryInfo", Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::PCGCacheManager_eventGetEntryInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::PCGCacheManager_eventGetEntryInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetEntryInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetEntryInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetEntryInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGCacheEntry*)Z_Param__Result=P_THIS->GetEntryInfo(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetEntryInfo *************************************

// ********** Begin Class APCGCacheManager Function GetHitRatio ************************************
struct Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics
{
	struct PCGCacheManager_eventGetHitRatio_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetHitRatio_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetHitRatio", Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::PCGCacheManager_eventGetHitRatio_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::PCGCacheManager_eventGetHitRatio_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetHitRatio()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetHitRatio_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetHitRatio)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHitRatio();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetHitRatio **************************************

// ********** Begin Class APCGCacheManager Function GetMemoryUsage *********************************
struct Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics
{
	struct PCGCacheManager_eventGetMemoryUsage_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventGetMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "GetMemoryUsage", Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::PCGCacheManager_eventGetMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::PCGCacheManager_eventGetMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execGetMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function GetMemoryUsage ***********************************

// ********** Begin Class APCGCacheManager Function HasEntry ***************************************
struct Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics
{
	struct PCGCacheManager_eventHasEntry_Parms
	{
		FString Key;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventHasEntry_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGCacheManager_eventHasEntry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventHasEntry_Parms), &Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "HasEntry", Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::PCGCacheManager_eventHasEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::PCGCacheManager_eventHasEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_HasEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_HasEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execHasEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasEntry(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function HasEntry *****************************************

// ********** Begin Class APCGCacheManager Function InitializeCache ********************************
struct Z_Construct_UFunction_APCGCacheManager_InitializeCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Initialization and Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialization and Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_InitializeCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "InitializeCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_InitializeCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_InitializeCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGCacheManager_InitializeCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_InitializeCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execInitializeCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeCache();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function InitializeCache **********************************

// ********** Begin Class APCGCacheManager Function IntegrateWithLumen *****************************
struct Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics
{
	struct PCGCacheManager_eventIntegrateWithLumen_Parms
	{
		APCGLumenIntegrator* LumenIntegrator;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LumenIntegrator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::NewProp_LumenIntegrator = { "LumenIntegrator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventIntegrateWithLumen_Parms, LumenIntegrator), Z_Construct_UClass_APCGLumenIntegrator_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::NewProp_LumenIntegrator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "IntegrateWithLumen", Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::PCGCacheManager_eventIntegrateWithLumen_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::PCGCacheManager_eventIntegrateWithLumen_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execIntegrateWithLumen)
{
	P_GET_OBJECT(APCGLumenIntegrator,Z_Param_LumenIntegrator);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithLumen(Z_Param_LumenIntegrator);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function IntegrateWithLumen *******************************

// ********** Begin Class APCGCacheManager Function IntegrateWithNanite ****************************
struct Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics
{
	struct PCGCacheManager_eventIntegrateWithNanite_Parms
	{
		APCGNaniteOptimizer* NaniteOptimizer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NaniteOptimizer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer = { "NaniteOptimizer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventIntegrateWithNanite_Parms, NaniteOptimizer), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "IntegrateWithNanite", Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::PCGCacheManager_eventIntegrateWithNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::PCGCacheManager_eventIntegrateWithNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execIntegrateWithNanite)
{
	P_GET_OBJECT(APCGNaniteOptimizer,Z_Param_NaniteOptimizer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithNanite(Z_Param_NaniteOptimizer);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function IntegrateWithNanite ******************************

// ********** Begin Class APCGCacheManager Function IntegrateWithProfiler **************************
struct Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics
{
	struct PCGCacheManager_eventIntegrateWithProfiler_Parms
	{
		UPCGPerformanceProfiler* PerformanceProfiler;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PerformanceProfiler;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::NewProp_PerformanceProfiler = { "PerformanceProfiler", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventIntegrateWithProfiler_Parms, PerformanceProfiler), Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::NewProp_PerformanceProfiler,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "IntegrateWithProfiler", Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::PCGCacheManager_eventIntegrateWithProfiler_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::PCGCacheManager_eventIntegrateWithProfiler_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execIntegrateWithProfiler)
{
	P_GET_OBJECT(UPCGPerformanceProfiler,Z_Param_PerformanceProfiler);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithProfiler(Z_Param_PerformanceProfiler);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function IntegrateWithProfiler ****************************

// ********** Begin Class APCGCacheManager Function IntegrateWithStreaming *************************
struct Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics
{
	struct PCGCacheManager_eventIntegrateWithStreaming_Parms
	{
		APCGStreamingManager* StreamingManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StreamingManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::NewProp_StreamingManager = { "StreamingManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventIntegrateWithStreaming_Parms, StreamingManager), Z_Construct_UClass_APCGStreamingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::NewProp_StreamingManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "IntegrateWithStreaming", Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::PCGCacheManager_eventIntegrateWithStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::PCGCacheManager_eventIntegrateWithStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execIntegrateWithStreaming)
{
	P_GET_OBJECT(APCGStreamingManager,Z_Param_StreamingManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithStreaming(Z_Param_StreamingManager);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function IntegrateWithStreaming ***************************

// ********** Begin Class APCGCacheManager Function IntegrateWithWorldPartition ********************
struct Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics
{
	struct PCGCacheManager_eventIntegrateWithWorldPartition_Parms
	{
		APCGWorldPartitionManager* WorldPartitionManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with PCG Systems\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with PCG Systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventIntegrateWithWorldPartition_Parms, WorldPartitionManager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "IntegrateWithWorldPartition", Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::PCGCacheManager_eventIntegrateWithWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::PCGCacheManager_eventIntegrateWithWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execIntegrateWithWorldPartition)
{
	P_GET_OBJECT(APCGWorldPartitionManager,Z_Param_WorldPartitionManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithWorldPartition(Z_Param_WorldPartitionManager);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function IntegrateWithWorldPartition **********************

// ********** Begin Class APCGCacheManager Function InvalidateEntry ********************************
struct Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics
{
	struct PCGCacheManager_eventInvalidateEntry_Parms
	{
		FString Key;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventInvalidateEntry_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::NewProp_Key,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "InvalidateEntry", Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::PCGCacheManager_eventInvalidateEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::PCGCacheManager_eventInvalidateEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_InvalidateEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_InvalidateEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execInvalidateEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InvalidateEntry(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function InvalidateEntry **********************************

// ********** Begin Class APCGCacheManager Function LoadData ***************************************
struct Z_Construct_UFunction_APCGCacheManager_LoadData_Statics
{
	struct PCGCacheManager_eventLoadData_Parms
	{
		FString Key;
		TArray<uint8> OutData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OutData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventLoadData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_OutData_Inner = { "OutData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_OutData = { "OutData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventLoadData_Parms, OutData), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGCacheManager_eventLoadData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventLoadData_Parms), &Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_OutData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_OutData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "LoadData", Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::PCGCacheManager_eventLoadData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::PCGCacheManager_eventLoadData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_LoadData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_LoadData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execLoadData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_TARRAY_REF(uint8,Z_Param_Out_OutData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadData(Z_Param_Key,Z_Param_Out_OutData);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function LoadData *****************************************

// ********** Begin Class APCGCacheManager Function LoadDataAsync **********************************
struct Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics
{
	struct PCGCacheManager_eventLoadDataAsync_Parms
	{
		FString Key;
		FMulticastScriptDelegate Callback;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Async Operations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async Operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Callback_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_Callback;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventLoadDataAsync_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::NewProp_Callback = { "Callback", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventLoadDataAsync_Parms, Callback), Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Callback_MetaData), NewProp_Callback_MetaData) }; // 1022059410
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::NewProp_Callback,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "LoadDataAsync", Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::PCGCacheManager_eventLoadDataAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::PCGCacheManager_eventLoadDataAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_LoadDataAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_LoadDataAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execLoadDataAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY_REF(FMulticastInlineDelegateProperty,Z_Param_Out_Callback);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadDataAsync(Z_Param_Key,FOnCacheEntryLoaded(Z_Param_Out_Callback));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function LoadDataAsync ************************************

// ********** Begin Class APCGCacheManager Function LoadFromDisk ***********************************
struct Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics
{
	struct PCGCacheManager_eventLoadFromDisk_Parms
	{
		FString Key;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Disk Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventLoadFromDisk_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGCacheManager_eventLoadFromDisk_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventLoadFromDisk_Parms), &Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "LoadFromDisk", Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::PCGCacheManager_eventLoadFromDisk_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::PCGCacheManager_eventLoadFromDisk_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_LoadFromDisk()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_LoadFromDisk_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execLoadFromDisk)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadFromDisk(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function LoadFromDisk *************************************

// ********** Begin Class APCGCacheManager Function OptimizeCache **********************************
struct Z_Construct_UFunction_APCGCacheManager_OptimizeCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_OptimizeCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "OptimizeCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_OptimizeCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_OptimizeCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGCacheManager_OptimizeCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_OptimizeCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execOptimizeCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeCache();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function OptimizeCache ************************************

// ********** Begin Class APCGCacheManager Function PrefetchByPattern ******************************
struct Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics
{
	struct PCGCacheManager_eventPrefetchByPattern_Parms
	{
		FString Pattern;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pattern_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventPrefetchByPattern_Parms, Pattern), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pattern_MetaData), NewProp_Pattern_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::NewProp_Pattern,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "PrefetchByPattern", Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::PCGCacheManager_eventPrefetchByPattern_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::PCGCacheManager_eventPrefetchByPattern_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execPrefetchByPattern)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Pattern);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PrefetchByPattern(Z_Param_Pattern);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function PrefetchByPattern ********************************

// ********** Begin Class APCGCacheManager Function PrefetchData ***********************************
struct Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics
{
	struct PCGCacheManager_eventPrefetchData_Parms
	{
		TArray<FString> Keys;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Keys_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Keys_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Keys;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::NewProp_Keys_Inner = { "Keys", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::NewProp_Keys = { "Keys", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventPrefetchData_Parms, Keys), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Keys_MetaData), NewProp_Keys_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::NewProp_Keys_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::NewProp_Keys,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "PrefetchData", Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::PCGCacheManager_eventPrefetchData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::PCGCacheManager_eventPrefetchData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_PrefetchData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_PrefetchData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execPrefetchData)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_Keys);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PrefetchData(Z_Param_Out_Keys);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function PrefetchData *************************************

// ********** Begin Class APCGCacheManager Function RefreshEntry ***********************************
struct Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics
{
	struct PCGCacheManager_eventRefreshEntry_Parms
	{
		FString Key;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventRefreshEntry_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::NewProp_Key,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "RefreshEntry", Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::PCGCacheManager_eventRefreshEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::PCGCacheManager_eventRefreshEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_RefreshEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_RefreshEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execRefreshEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RefreshEntry(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function RefreshEntry *************************************

// ********** Begin Class APCGCacheManager Function RemoveEntry ************************************
struct Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics
{
	struct PCGCacheManager_eventRemoveEntry_Parms
	{
		FString Key;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventRemoveEntry_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGCacheManager_eventRemoveEntry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventRemoveEntry_Parms), &Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "RemoveEntry", Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::PCGCacheManager_eventRemoveEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::PCGCacheManager_eventRemoveEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_RemoveEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_RemoveEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execRemoveEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveEntry(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function RemoveEntry **************************************

// ********** Begin Class APCGCacheManager Function RemoveEntryDependency **************************
struct Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics
{
	struct PCGCacheManager_eventRemoveEntryDependency_Parms
	{
		FString Key;
		FString DependencyKey;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Entry Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DependencyKey_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DependencyKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventRemoveEntryDependency_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::NewProp_DependencyKey = { "DependencyKey", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventRemoveEntryDependency_Parms, DependencyKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DependencyKey_MetaData), NewProp_DependencyKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::NewProp_DependencyKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "RemoveEntryDependency", Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::PCGCacheManager_eventRemoveEntryDependency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::PCGCacheManager_eventRemoveEntryDependency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execRemoveEntryDependency)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FStrProperty,Z_Param_DependencyKey);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveEntryDependency(Z_Param_Key,Z_Param_DependencyKey);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function RemoveEntryDependency ****************************

// ********** Begin Class APCGCacheManager Function ResetStatistics ********************************
struct Z_Construct_UFunction_APCGCacheManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGCacheManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function ResetStatistics **********************************

// ********** Begin Class APCGCacheManager Function SaveToDisk *************************************
struct Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics
{
	struct PCGCacheManager_eventSaveToDisk_Parms
	{
		FString Key;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Disk Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Disk Operations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Disk Operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventSaveToDisk_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGCacheManager_eventSaveToDisk_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventSaveToDisk_Parms), &Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "SaveToDisk", Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::PCGCacheManager_eventSaveToDisk_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::PCGCacheManager_eventSaveToDisk_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_SaveToDisk()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_SaveToDisk_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execSaveToDisk)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveToDisk(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function SaveToDisk ***************************************

// ********** Begin Class APCGCacheManager Function SetEntryPriority *******************************
struct Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics
{
	struct PCGCacheManager_eventSetEntryPriority_Parms
	{
		FString Key;
		EPCGCachePriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Entry Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventSetEntryPriority_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventSetEntryPriority_Parms, Priority), Z_Construct_UEnum_Aura_EPCGCachePriority, METADATA_PARAMS(0, nullptr) }; // 1356705782
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "SetEntryPriority", Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::PCGCacheManager_eventSetEntryPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::PCGCacheManager_eventSetEntryPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_SetEntryPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_SetEntryPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execSetEntryPriority)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_ENUM(EPCGCachePriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEntryPriority(Z_Param_Key,EPCGCachePriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function SetEntryPriority *********************************

// ********** Begin Class APCGCacheManager Function SetMaintenanceMode *****************************
struct Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics
{
	struct PCGCacheManager_eventSetMaintenanceMode_Parms
	{
		bool bMaintenance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bMaintenance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMaintenance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::NewProp_bMaintenance_SetBit(void* Obj)
{
	((PCGCacheManager_eventSetMaintenanceMode_Parms*)Obj)->bMaintenance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::NewProp_bMaintenance = { "bMaintenance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventSetMaintenanceMode_Parms), &Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::NewProp_bMaintenance_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::NewProp_bMaintenance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "SetMaintenanceMode", Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::PCGCacheManager_eventSetMaintenanceMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::PCGCacheManager_eventSetMaintenanceMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execSetMaintenanceMode)
{
	P_GET_UBOOL(Z_Param_bMaintenance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaintenanceMode(Z_Param_bMaintenance);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function SetMaintenanceMode *******************************

// ********** Begin Class APCGCacheManager Function ShutdownCache **********************************
struct Z_Construct_UFunction_APCGCacheManager_ShutdownCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Management" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_ShutdownCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "ShutdownCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_ShutdownCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_ShutdownCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGCacheManager_ShutdownCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_ShutdownCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execShutdownCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownCache();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function ShutdownCache ************************************

// ********** Begin Class APCGCacheManager Function StoreData **************************************
struct Z_Construct_UFunction_APCGCacheManager_StoreData_Statics
{
	struct PCGCacheManager_eventStoreData_Parms
	{
		FString Key;
		TArray<uint8> Data;
		EPCGCachePriority Priority;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache Operations\n" },
#endif
		{ "CPP_Default_Priority", "Medium" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache Operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Data_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Data;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventStoreData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Data_Inner = { "Data", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventStoreData_Parms, Data), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventStoreData_Parms, Priority), Z_Construct_UEnum_Aura_EPCGCachePriority, METADATA_PARAMS(0, nullptr) }; // 1356705782
void Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGCacheManager_eventStoreData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGCacheManager_eventStoreData_Parms), &Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Data_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Data,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "StoreData", Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::PCGCacheManager_eventStoreData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::PCGCacheManager_eventStoreData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_StoreData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_StoreData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execStoreData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_TARRAY_REF(uint8,Z_Param_Out_Data);
	P_GET_ENUM(EPCGCachePriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StoreData(Z_Param_Key,Z_Param_Out_Data,EPCGCachePriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function StoreData ****************************************

// ********** Begin Class APCGCacheManager Function StoreDataAsync *********************************
struct Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics
{
	struct PCGCacheManager_eventStoreDataAsync_Parms
	{
		FString Key;
		TArray<uint8> Data;
		EPCGCachePriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Async Operations" },
		{ "CPP_Default_Priority", "Medium" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Data_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Data;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventStoreDataAsync_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Data_Inner = { "Data", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventStoreDataAsync_Parms, Data), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventStoreDataAsync_Parms, Priority), Z_Construct_UEnum_Aura_EPCGCachePriority, METADATA_PARAMS(0, nullptr) }; // 1356705782
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Data_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Data,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "StoreDataAsync", Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::PCGCacheManager_eventStoreDataAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::PCGCacheManager_eventStoreDataAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_StoreDataAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_StoreDataAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execStoreDataAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_TARRAY_REF(uint8,Z_Param_Out_Data);
	P_GET_ENUM(EPCGCachePriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StoreDataAsync(Z_Param_Key,Z_Param_Out_Data,EPCGCachePriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function StoreDataAsync ***********************************

// ********** Begin Class APCGCacheManager Function SyncWithNetworkCache ***************************
struct Z_Construct_UFunction_APCGCacheManager_SyncWithNetworkCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Operations" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Network Operations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network Operations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_SyncWithNetworkCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "SyncWithNetworkCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_SyncWithNetworkCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_SyncWithNetworkCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGCacheManager_SyncWithNetworkCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_SyncWithNetworkCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execSyncWithNetworkCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SyncWithNetworkCache();
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function SyncWithNetworkCache *****************************

// ********** Begin Class APCGCacheManager Function UploadToNetwork ********************************
struct Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics
{
	struct PCGCacheManager_eventUploadToNetwork_Parms
	{
		FString Key;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Operations" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGCacheManager_eventUploadToNetwork_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::NewProp_Key,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGCacheManager, nullptr, "UploadToNetwork", Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::PCGCacheManager_eventUploadToNetwork_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::PCGCacheManager_eventUploadToNetwork_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGCacheManager_UploadToNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGCacheManager_UploadToNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGCacheManager::execUploadToNetwork)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UploadToNetwork(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class APCGCacheManager Function UploadToNetwork **********************************

// ********** Begin Class APCGCacheManager *********************************************************
void APCGCacheManager::StaticRegisterNativesAPCGCacheManager()
{
	UClass* Class = APCGCacheManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddEntryDependency", &APCGCacheManager::execAddEntryDependency },
		{ "CleanupDiskCache", &APCGCacheManager::execCleanupDiskCache },
		{ "ClearCache", &APCGCacheManager::execClearCache },
		{ "CompressData", &APCGCacheManager::execCompressData },
		{ "DecompressData", &APCGCacheManager::execDecompressData },
		{ "DownloadFromNetwork", &APCGCacheManager::execDownloadFromNetwork },
		{ "EnableCache", &APCGCacheManager::execEnableCache },
		{ "EvictByPriority", &APCGCacheManager::execEvictByPriority },
		{ "EvictExpiredEntries", &APCGCacheManager::execEvictExpiredEntries },
		{ "EvictLeastFrequentlyUsed", &APCGCacheManager::execEvictLeastFrequentlyUsed },
		{ "EvictLeastRecentlyUsed", &APCGCacheManager::execEvictLeastRecentlyUsed },
		{ "ExportStatistics", &APCGCacheManager::execExportStatistics },
		{ "ForceEviction", &APCGCacheManager::execForceEviction },
		{ "GetAllEntries", &APCGCacheManager::execGetAllEntries },
		{ "GetCacheHitRate", &APCGCacheManager::execGetCacheHitRate },
		{ "GetCacheStatistics", &APCGCacheManager::execGetCacheStatistics },
		{ "GetCompressionRatio", &APCGCacheManager::execGetCompressionRatio },
		{ "GetDiskCacheSize", &APCGCacheManager::execGetDiskCacheSize },
		{ "GetDiskUsage", &APCGCacheManager::execGetDiskUsage },
		{ "GetEntriesByPriority", &APCGCacheManager::execGetEntriesByPriority },
		{ "GetEntriesByType", &APCGCacheManager::execGetEntriesByType },
		{ "GetEntryInfo", &APCGCacheManager::execGetEntryInfo },
		{ "GetHitRatio", &APCGCacheManager::execGetHitRatio },
		{ "GetMemoryUsage", &APCGCacheManager::execGetMemoryUsage },
		{ "HasEntry", &APCGCacheManager::execHasEntry },
		{ "InitializeCache", &APCGCacheManager::execInitializeCache },
		{ "IntegrateWithLumen", &APCGCacheManager::execIntegrateWithLumen },
		{ "IntegrateWithNanite", &APCGCacheManager::execIntegrateWithNanite },
		{ "IntegrateWithProfiler", &APCGCacheManager::execIntegrateWithProfiler },
		{ "IntegrateWithStreaming", &APCGCacheManager::execIntegrateWithStreaming },
		{ "IntegrateWithWorldPartition", &APCGCacheManager::execIntegrateWithWorldPartition },
		{ "InvalidateEntry", &APCGCacheManager::execInvalidateEntry },
		{ "LoadData", &APCGCacheManager::execLoadData },
		{ "LoadDataAsync", &APCGCacheManager::execLoadDataAsync },
		{ "LoadFromDisk", &APCGCacheManager::execLoadFromDisk },
		{ "OptimizeCache", &APCGCacheManager::execOptimizeCache },
		{ "PrefetchByPattern", &APCGCacheManager::execPrefetchByPattern },
		{ "PrefetchData", &APCGCacheManager::execPrefetchData },
		{ "RefreshEntry", &APCGCacheManager::execRefreshEntry },
		{ "RemoveEntry", &APCGCacheManager::execRemoveEntry },
		{ "RemoveEntryDependency", &APCGCacheManager::execRemoveEntryDependency },
		{ "ResetStatistics", &APCGCacheManager::execResetStatistics },
		{ "SaveToDisk", &APCGCacheManager::execSaveToDisk },
		{ "SetEntryPriority", &APCGCacheManager::execSetEntryPriority },
		{ "SetMaintenanceMode", &APCGCacheManager::execSetMaintenanceMode },
		{ "ShutdownCache", &APCGCacheManager::execShutdownCache },
		{ "StoreData", &APCGCacheManager::execStoreData },
		{ "StoreDataAsync", &APCGCacheManager::execStoreDataAsync },
		{ "SyncWithNetworkCache", &APCGCacheManager::execSyncWithNetworkCache },
		{ "UploadToNetwork", &APCGCacheManager::execUploadToNetwork },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_APCGCacheManager;
UClass* APCGCacheManager::GetPrivateStaticClass()
{
	using TClass = APCGCacheManager;
	if (!Z_Registration_Info_UClass_APCGCacheManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGCacheManager"),
			Z_Registration_Info_UClass_APCGCacheManager.InnerSingleton,
			StaticRegisterNativesAPCGCacheManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_APCGCacheManager.InnerSingleton;
}
UClass* Z_Construct_UClass_APCGCacheManager_NoRegister()
{
	return APCGCacheManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_APCGCacheManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * APCGCacheManager - Advanced cache management system for PCG content\n * \n * Features:\n * - Multi-tier caching (Memory, Disk, Network)\n * - Intelligent eviction policies\n * - Compression and decompression\n * - Async loading and prefetching\n * - Dependency tracking\n * - Performance monitoring\n * - Integration with other PCG systems\n */" },
#endif
		{ "IncludePath", "APCGCacheManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "APCGCacheManager - Advanced cache management system for PCG content\n\nFeatures:\n- Multi-tier caching (Memory, Disk, Network)\n- Intelligent eviction policies\n- Compression and decompression\n- Async loading and prefetching\n- Dependency tracking\n- Performance monitoring\n- Integration with other PCG systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheConfig_MetaData[] = {
		{ "Category", "Cache Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "Category", "Cache State" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEnabled_MetaData[] = {
		{ "Category", "Cache State" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMaintenanceMode_MetaData[] = {
		{ "Category", "Cache State" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCacheEntryLoaded_MetaData[] = {
		{ "Category", "Cache Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event delegates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCacheEntryEvicted_MetaData[] = {
		{ "Category", "Cache Events" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCacheStatisticsUpdated_MetaData[] = {
		{ "Category", "Cache Events" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCacheError_MetaData[] = {
		{ "Category", "Cache Events" },
		{ "ModuleRelativePath", "Public/APCGCacheManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CacheConfig;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static void NewProp_bMaintenanceMode_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMaintenanceMode;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCacheEntryLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCacheEntryEvicted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCacheStatisticsUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCacheError;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APCGCacheManager_AddEntryDependency, "AddEntryDependency" }, // 2637039650
		{ &Z_Construct_UFunction_APCGCacheManager_CleanupDiskCache, "CleanupDiskCache" }, // 3786444160
		{ &Z_Construct_UFunction_APCGCacheManager_ClearCache, "ClearCache" }, // 1916320629
		{ &Z_Construct_UFunction_APCGCacheManager_CompressData, "CompressData" }, // 2458214621
		{ &Z_Construct_UFunction_APCGCacheManager_DecompressData, "DecompressData" }, // 2919338261
		{ &Z_Construct_UFunction_APCGCacheManager_DownloadFromNetwork, "DownloadFromNetwork" }, // 3868478437
		{ &Z_Construct_UFunction_APCGCacheManager_EnableCache, "EnableCache" }, // 1451882919
		{ &Z_Construct_UFunction_APCGCacheManager_EvictByPriority, "EvictByPriority" }, // 3372580606
		{ &Z_Construct_UFunction_APCGCacheManager_EvictExpiredEntries, "EvictExpiredEntries" }, // 2738432368
		{ &Z_Construct_UFunction_APCGCacheManager_EvictLeastFrequentlyUsed, "EvictLeastFrequentlyUsed" }, // 2696271508
		{ &Z_Construct_UFunction_APCGCacheManager_EvictLeastRecentlyUsed, "EvictLeastRecentlyUsed" }, // 3772527141
		{ &Z_Construct_UFunction_APCGCacheManager_ExportStatistics, "ExportStatistics" }, // 3208393049
		{ &Z_Construct_UFunction_APCGCacheManager_ForceEviction, "ForceEviction" }, // 262935172
		{ &Z_Construct_UFunction_APCGCacheManager_GetAllEntries, "GetAllEntries" }, // 3852224068
		{ &Z_Construct_UFunction_APCGCacheManager_GetCacheHitRate, "GetCacheHitRate" }, // 3044376159
		{ &Z_Construct_UFunction_APCGCacheManager_GetCacheStatistics, "GetCacheStatistics" }, // 578358532
		{ &Z_Construct_UFunction_APCGCacheManager_GetCompressionRatio, "GetCompressionRatio" }, // 3219863385
		{ &Z_Construct_UFunction_APCGCacheManager_GetDiskCacheSize, "GetDiskCacheSize" }, // 2320863134
		{ &Z_Construct_UFunction_APCGCacheManager_GetDiskUsage, "GetDiskUsage" }, // 1458876814
		{ &Z_Construct_UFunction_APCGCacheManager_GetEntriesByPriority, "GetEntriesByPriority" }, // 3678186300
		{ &Z_Construct_UFunction_APCGCacheManager_GetEntriesByType, "GetEntriesByType" }, // 42492823
		{ &Z_Construct_UFunction_APCGCacheManager_GetEntryInfo, "GetEntryInfo" }, // 288483294
		{ &Z_Construct_UFunction_APCGCacheManager_GetHitRatio, "GetHitRatio" }, // 2781977735
		{ &Z_Construct_UFunction_APCGCacheManager_GetMemoryUsage, "GetMemoryUsage" }, // 1537030722
		{ &Z_Construct_UFunction_APCGCacheManager_HasEntry, "HasEntry" }, // 3533090834
		{ &Z_Construct_UFunction_APCGCacheManager_InitializeCache, "InitializeCache" }, // 357783202
		{ &Z_Construct_UFunction_APCGCacheManager_IntegrateWithLumen, "IntegrateWithLumen" }, // 3488799632
		{ &Z_Construct_UFunction_APCGCacheManager_IntegrateWithNanite, "IntegrateWithNanite" }, // 1233018478
		{ &Z_Construct_UFunction_APCGCacheManager_IntegrateWithProfiler, "IntegrateWithProfiler" }, // 1309220726
		{ &Z_Construct_UFunction_APCGCacheManager_IntegrateWithStreaming, "IntegrateWithStreaming" }, // 1151379465
		{ &Z_Construct_UFunction_APCGCacheManager_IntegrateWithWorldPartition, "IntegrateWithWorldPartition" }, // 1332353127
		{ &Z_Construct_UFunction_APCGCacheManager_InvalidateEntry, "InvalidateEntry" }, // 3906666815
		{ &Z_Construct_UFunction_APCGCacheManager_LoadData, "LoadData" }, // 1109044199
		{ &Z_Construct_UFunction_APCGCacheManager_LoadDataAsync, "LoadDataAsync" }, // 137785712
		{ &Z_Construct_UFunction_APCGCacheManager_LoadFromDisk, "LoadFromDisk" }, // 4011086137
		{ &Z_Construct_UFunction_APCGCacheManager_OptimizeCache, "OptimizeCache" }, // 2835237727
		{ &Z_Construct_UFunction_APCGCacheManager_PrefetchByPattern, "PrefetchByPattern" }, // 3937500901
		{ &Z_Construct_UFunction_APCGCacheManager_PrefetchData, "PrefetchData" }, // 1384668985
		{ &Z_Construct_UFunction_APCGCacheManager_RefreshEntry, "RefreshEntry" }, // 2538911635
		{ &Z_Construct_UFunction_APCGCacheManager_RemoveEntry, "RemoveEntry" }, // 1336345037
		{ &Z_Construct_UFunction_APCGCacheManager_RemoveEntryDependency, "RemoveEntryDependency" }, // 775528283
		{ &Z_Construct_UFunction_APCGCacheManager_ResetStatistics, "ResetStatistics" }, // 2170908865
		{ &Z_Construct_UFunction_APCGCacheManager_SaveToDisk, "SaveToDisk" }, // 124336005
		{ &Z_Construct_UFunction_APCGCacheManager_SetEntryPriority, "SetEntryPriority" }, // 2067685401
		{ &Z_Construct_UFunction_APCGCacheManager_SetMaintenanceMode, "SetMaintenanceMode" }, // 2025269735
		{ &Z_Construct_UFunction_APCGCacheManager_ShutdownCache, "ShutdownCache" }, // 818187600
		{ &Z_Construct_UFunction_APCGCacheManager_StoreData, "StoreData" }, // 4216912386
		{ &Z_Construct_UFunction_APCGCacheManager_StoreDataAsync, "StoreDataAsync" }, // 531474330
		{ &Z_Construct_UFunction_APCGCacheManager_SyncWithNetworkCache, "SyncWithNetworkCache" }, // 3123716045
		{ &Z_Construct_UFunction_APCGCacheManager_UploadToNetwork, "UploadToNetwork" }, // 1127843567
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APCGCacheManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_CacheConfig = { "CacheConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGCacheManager, CacheConfig), Z_Construct_UScriptStruct_FPCGCacheConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheConfig_MetaData), NewProp_CacheConfig_MetaData) }; // 3055025912
void Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((APCGCacheManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGCacheManager), &Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((APCGCacheManager*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGCacheManager), &Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEnabled_MetaData), NewProp_bIsEnabled_MetaData) };
void Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bMaintenanceMode_SetBit(void* Obj)
{
	((APCGCacheManager*)Obj)->bMaintenanceMode = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bMaintenanceMode = { "bMaintenanceMode", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGCacheManager), &Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bMaintenanceMode_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMaintenanceMode_MetaData), NewProp_bMaintenanceMode_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheEntryLoaded = { "OnCacheEntryLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGCacheManager, OnCacheEntryLoaded), Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCacheEntryLoaded_MetaData), NewProp_OnCacheEntryLoaded_MetaData) }; // 1022059410
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheEntryEvicted = { "OnCacheEntryEvicted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGCacheManager, OnCacheEntryEvicted), Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCacheEntryEvicted_MetaData), NewProp_OnCacheEntryEvicted_MetaData) }; // 1411704890
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheStatisticsUpdated = { "OnCacheStatisticsUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGCacheManager, OnCacheStatisticsUpdated), Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCacheStatisticsUpdated_MetaData), NewProp_OnCacheStatisticsUpdated_MetaData) }; // 1371362462
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheError = { "OnCacheError", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGCacheManager, OnCacheError), Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCacheError_MetaData), NewProp_OnCacheError_MetaData) }; // 2342183963
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APCGCacheManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_CacheConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_bMaintenanceMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheEntryLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheEntryEvicted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheStatisticsUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGCacheManager_Statics::NewProp_OnCacheError,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGCacheManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APCGCacheManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGCacheManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_APCGCacheManager_Statics::ClassParams = {
	&APCGCacheManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APCGCacheManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APCGCacheManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APCGCacheManager_Statics::Class_MetaDataParams), Z_Construct_UClass_APCGCacheManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APCGCacheManager()
{
	if (!Z_Registration_Info_UClass_APCGCacheManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APCGCacheManager.OuterSingleton, Z_Construct_UClass_APCGCacheManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APCGCacheManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APCGCacheManager);
APCGCacheManager::~APCGCacheManager() {}
// ********** End Class APCGCacheManager ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGCachePriority_StaticEnum, TEXT("EPCGCachePriority"), &Z_Registration_Info_UEnum_EPCGCachePriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1356705782U) },
		{ EPCGCacheState_StaticEnum, TEXT("EPCGCacheState"), &Z_Registration_Info_UEnum_EPCGCacheState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 544656260U) },
		{ EPCGCacheStorageType_StaticEnum, TEXT("EPCGCacheStorageType"), &Z_Registration_Info_UEnum_EPCGCacheStorageType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 630467440U) },
		{ EPCGCacheEvictionPolicy_StaticEnum, TEXT("EPCGCacheEvictionPolicy"), &Z_Registration_Info_UEnum_EPCGCacheEvictionPolicy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1583555971U) },
		{ EPCGCacheCompressionLevel_StaticEnum, TEXT("EPCGCacheCompressionLevel"), &Z_Registration_Info_UEnum_EPCGCacheCompressionLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2324820021U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGCacheConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGCacheConfig_Statics::NewStructOps, TEXT("PCGCacheConfig"), &Z_Registration_Info_UScriptStruct_FPCGCacheConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGCacheConfig), 3055025912U) },
		{ FPCGCacheEntry::StaticStruct, Z_Construct_UScriptStruct_FPCGCacheEntry_Statics::NewStructOps, TEXT("PCGCacheEntry"), &Z_Registration_Info_UScriptStruct_FPCGCacheEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGCacheEntry), 2179562633U) },
		{ FPCGCacheStatistics::StaticStruct, Z_Construct_UScriptStruct_FPCGCacheStatistics_Statics::NewStructOps, TEXT("PCGCacheStatistics"), &Z_Registration_Info_UScriptStruct_FPCGCacheStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGCacheStatistics), 1277078403U) },
		{ FPCGCacheOperationResult::StaticStruct, Z_Construct_UScriptStruct_FPCGCacheOperationResult_Statics::NewStructOps, TEXT("PCGCacheOperationResult"), &Z_Registration_Info_UScriptStruct_FPCGCacheOperationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGCacheOperationResult), 526902159U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APCGCacheManager, APCGCacheManager::StaticClass, TEXT("APCGCacheManager"), &Z_Registration_Info_UClass_APCGCacheManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APCGCacheManager), 1306787670U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_2627753014(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGCacheManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
