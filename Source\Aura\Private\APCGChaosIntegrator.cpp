#include "APCGChaosIntegrator.h"
#include "EngineUtils.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/BoxComponent.h"
#include "Components/CapsuleComponent.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Chaos/ChaosSolverActor.h"
#include "Chaos/ChaosGameplayEventDispatcher.h"
#include "Physics/Experimental/PhysScene_Chaos.h"
#include "PhysicalMaterials/PhysicalMaterial.h"
#include "Materials/MaterialInterface.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Async/Async.h"
#include "APCGWorldPartitionManager.h"
#include "APCGNaniteOptimizer.h"
#include "APCGLumenIntegrator.h"
#include "APCGStreamingManager.h"
#include "APCGCacheManager.h"
#include "UPCGPerformanceProfiler.h"

APCGChaosIntegrator::APCGChaosIntegrator()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.016667f; // 60 FPS
    
    // Set default physics configuration
    PhysicsConfig = FPCGPhysicsConfig();
    
    // Initialize performance tracking
    FrameTimeHistory.SetNum(60); // Track last 60 frames
    for (int32 i = 0; i < FrameTimeHistory.Num(); i++)
    {
        FrameTimeHistory[i] = 0.016667f; // Default to 60 FPS
    }
    
    // Initialize current stats
    CurrentStats = FPCGPhysicsPerformanceStats();
    CurrentStats.TargetFrameRate = 60.0f;
    CurrentStats.ActualFrameRate = 60.0f;
    
    bIsInitialized = false;
    bIsEnabled = true;
    bSimulationRunning = false;
    bPhysicsInitialized = false;
    bSimulationPaused = false;
    LastPerformanceUpdate = 0.0f;
    LastLODUpdate = 0.0f;
    FrameTimeHistoryIndex = 0;
}

void APCGChaosIntegrator::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: BeginPlay started"));
    
    // Initialize physics system
    InitializePhysics();
    
    // Start simulation if enabled
    if (bIsEnabled)
    {
        StartSimulation();
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: BeginPlay completed"));
}

void APCGChaosIntegrator::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: EndPlay started"));
    
    // Stop simulation
    StopSimulation();
    
    // Shutdown physics
    ShutdownPhysics();
    
    // Clear all references
    WorldPartitionManagerRef.Reset();
    NaniteOptimizerRef.Reset();
    LumenIntegratorRef.Reset();
    StreamingManagerRef.Reset();
    CacheManagerRef.Reset();
    PerformanceProfilerRef.Reset();
    ChaosSolver.Reset();
    EventDispatcher.Reset();
    
    Super::EndPlay(EndPlayReason);
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: EndPlay completed"));
}

void APCGChaosIntegrator::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsInitialized || !bIsEnabled || bSimulationPaused)
    {
        return;
    }
    
    // Update frame time history
    FrameTimeHistory[FrameTimeHistoryIndex] = DeltaTime;
    FrameTimeHistoryIndex = (FrameTimeHistoryIndex + 1) % FrameTimeHistory.Num();
    
    // Update performance statistics
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastPerformanceUpdate >= 1.0f) // Update every second
    {
        UpdatePerformanceStatistics();
        LastPerformanceUpdate = CurrentTime;
    }
    
    // Update physics LOD
    if (PhysicsConfig.bEnablePhysicsLOD && CurrentTime - LastLODUpdate >= 0.1f) // Update every 100ms
    {
        UpdatePhysicsLODInternal();
        LastLODUpdate = CurrentTime;
    }
    
    // Process physics events
    ProcessPhysicsEvents();
    
    // Update integrated systems
    UpdateIntegratedSystems();
    
    // Monitor performance
    MonitorPhysicsPerformance();
    
    // Handle memory pressure if needed
    if (CurrentStats.PhysicsMemoryUsageMB > 1000) // 1GB threshold
    {
        HandleMemoryPressure();
    }
}

// Initialization and Management
void APCGChaosIntegrator::InitializePhysics()
{
    if (bPhysicsInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Physics already initialized"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Initializing physics system"));
    
    // Validate configuration
    ValidatePhysicsConfiguration();
    
    // Find or create Chaos solver
    UWorld* World = GetWorld();
    if (World)
    {
        // Look for existing Chaos solver
        for (TActorIterator<AChaosSolverActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            ChaosSolver = *ActorItr;
            break;
        }
        
        // Create new solver if none found
        if (!ChaosSolver.IsValid())
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Name = FName(TEXT("PCGChaosSolver"));
            ChaosSolver = World->SpawnActor<AChaosSolverActor>(SpawnParams);
        }
        
        // Configure solver
        if (ChaosSolver.IsValid())
        {
            // Configure solver settings based on our config
            // Note: Actual Chaos solver configuration would go here
            UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Chaos solver configured"));
        }
        
        // Initialize event dispatcher
        EventDispatcher = NewObject<UChaosGameplayEventDispatcher>(this);
        if (EventDispatcher.IsValid())
        {
            // Bind event handlers
            // Note: Actual event binding would go here
            UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Event dispatcher initialized"));
        }
    }
    
    // Initialize physics data containers
    PhysicsBodies.Empty();
    PhysicsConstraints.Empty();
    PhysicsComponents.Empty();
    RecentEvents.Empty();
    
    // Apply physics optimizations
    ApplyPhysicsOptimizations();
    
    bPhysicsInitialized = true;
    bIsInitialized = true;
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics system initialized successfully"));
}

void APCGChaosIntegrator::ShutdownPhysics()
{
    if (!bPhysicsInitialized)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Shutting down physics system"));
    
    // Stop simulation
    StopSimulation();
    
    // Clean up physics resources
    CleanupPhysicsResources();
    
    // Clear all physics data
    PhysicsBodies.Empty();
    PhysicsConstraints.Empty();
    PhysicsComponents.Empty();
    RecentEvents.Empty();
    
    // Reset solver references
    ChaosSolver.Reset();
    EventDispatcher.Reset();
    
    bPhysicsInitialized = false;
    bIsInitialized = false;
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics system shutdown completed"));
}

void APCGChaosIntegrator::EnablePhysics(bool bEnable)
{
    bIsEnabled = bEnable;
    
    if (bEnable && bPhysicsInitialized)
    {
        StartSimulation();
    }
    else
    {
        StopSimulation();
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void APCGChaosIntegrator::StartSimulation()
{
    if (!bPhysicsInitialized || bSimulationRunning)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Starting physics simulation"));
    
    // Enable physics simulation on all bodies
    for (auto& BodyPair : PhysicsBodies)
    {
        if (auto* Component = PhysicsComponents.Find(BodyPair.Key))
        {
            if (Component->IsValid())
            {
                Component->Get()->SetSimulatePhysics(true);
            }
        }
    }
    
    bSimulationRunning = true;
    bSimulationPaused = false;
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics simulation started"));
}

void APCGChaosIntegrator::StopSimulation()
{
    if (!bSimulationRunning)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Stopping physics simulation"));
    
    // Disable physics simulation on all bodies
    for (auto& BodyPair : PhysicsBodies)
    {
        if (auto* Component = PhysicsComponents.Find(BodyPair.Key))
        {
            if (Component->IsValid())
            {
                Component->Get()->SetSimulatePhysics(false);
            }
        }
    }
    
    bSimulationRunning = false;
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics simulation stopped"));
}

void APCGChaosIntegrator::PauseSimulation(bool bPause)
{
    bSimulationPaused = bPause;
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics simulation %s"), bPause ? TEXT("paused") : TEXT("resumed"));
}

void APCGChaosIntegrator::ResetSimulation()
{
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Resetting physics simulation"));
    
    // Stop simulation
    StopSimulation();
    
    // Reset all physics bodies to initial state
    for (auto& BodyPair : PhysicsBodies)
    {
        FPCGPhysicsBodyData& BodyData = BodyPair.Value;
        if (auto* Component = PhysicsComponents.Find(BodyPair.Key))
        {
            if (Component->IsValid())
            {
                // Reset transform
                FTransform InitialTransform(BodyData.Rotation, BodyData.Position, BodyData.Scale);
                Component->Get()->SetWorldTransform(InitialTransform);
                
                // Reset velocities
                Component->Get()->SetPhysicsLinearVelocity(FVector::ZeroVector);
                Component->Get()->SetPhysicsAngularVelocityInRadians(FVector::ZeroVector);
                
                // Wake up the body
                Component->Get()->WakeRigidBody();
            }
        }
    }
    
    // Clear recent events
    RecentEvents.Empty();
    
    // Restart simulation if it was running
    if (bIsEnabled)
    {
        StartSimulation();
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics simulation reset completed"));
}

// Physics Body Management
FString APCGChaosIntegrator::CreatePhysicsBody(const FPCGPhysicsBodyData& BodyData)
{
    if (!bPhysicsInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGChaosIntegrator: Cannot create physics body - physics not initialized"));
        return FString();
    }
    
    // Generate unique ID
    FString BodyID = GenerateUniqueBodyID();
    
    // Create physics component
    UPrimitiveComponent* Component = CreatePhysicsComponent(BodyData);
    if (!Component)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGChaosIntegrator: Failed to create physics component for body %s"), *BodyID);
        return FString();
    }
    
    // Configure component
    ConfigurePhysicsComponent(Component, BodyData);
    
    // Store body data
    FPCGPhysicsBodyData StoredBodyData = BodyData;
    StoredBodyData.BodyID = BodyID;
    PhysicsBodies.Add(BodyID, StoredBodyData);
    PhysicsComponents.Add(BodyID, Component);
    
    // Broadcast event
    OnPhysicsBodyCreated.Broadcast(StoredBodyData);
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Created physics body %s"), *BodyID);
    
    return BodyID;
}

bool APCGChaosIntegrator::DestroyPhysicsBody(const FString& BodyID)
{
    if (!PhysicsBodies.Contains(BodyID))
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Physics body %s not found"), *BodyID);
        return false;
    }
    
    // Remove constraints connected to this body
    TArray<FString> ConstraintsToRemove;
    for (const auto& ConstraintPair : PhysicsConstraints)
    {
        const FPCGPhysicsConstraintData& ConstraintData = ConstraintPair.Value;
        if (ConstraintData.BodyA_ID == BodyID || ConstraintData.BodyB_ID == BodyID)
        {
            ConstraintsToRemove.Add(ConstraintPair.Key);
        }
    }
    
    for (const FString& ConstraintID : ConstraintsToRemove)
    {
        DestroyPhysicsConstraint(ConstraintID);
    }
    
    // Destroy physics component
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->DestroyComponent();
        }
    }
    
    // Remove from storage
    PhysicsBodies.Remove(BodyID);
    PhysicsComponents.Remove(BodyID);
    
    // Broadcast event
    OnPhysicsBodyDestroyed.Broadcast(BodyID);
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Destroyed physics body %s"), *BodyID);
    
    return true;
}

bool APCGChaosIntegrator::UpdatePhysicsBody(const FString& BodyID, const FPCGPhysicsBodyData& NewBodyData)
{
    if (!PhysicsBodies.Contains(BodyID))
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Physics body %s not found"), *BodyID);
        return false;
    }
    
    // Update stored data
    FPCGPhysicsBodyData UpdatedBodyData = NewBodyData;
    UpdatedBodyData.BodyID = BodyID;
    PhysicsBodies[BodyID] = UpdatedBodyData;
    
    // Update physics component
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            ConfigurePhysicsComponent(Component->Get(), UpdatedBodyData);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Updated physics body %s"), *BodyID);
    
    return true;
}

FPCGPhysicsBodyData APCGChaosIntegrator::GetPhysicsBodyData(const FString& BodyID) const
{
    if (const FPCGPhysicsBodyData* BodyData = PhysicsBodies.Find(BodyID))
    {
        return *BodyData;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Physics body %s not found"), *BodyID);
    return FPCGPhysicsBodyData();
}

TArray<FString> APCGChaosIntegrator::GetAllPhysicsBodies() const
{
    TArray<FString> BodyIDs;
    PhysicsBodies.GetKeys(BodyIDs);
    return BodyIDs;
}

TArray<FString> APCGChaosIntegrator::GetPhysicsBodiesByType(EPCGPhysicsBodyType BodyType) const
{
    TArray<FString> BodyIDs;
    
    for (const auto& BodyPair : PhysicsBodies)
    {
        if (BodyPair.Value.BodyType == BodyType)
        {
            BodyIDs.Add(BodyPair.Key);
        }
    }
    
    return BodyIDs;
}

// Physics Body Properties
void APCGChaosIntegrator::SetBodyMass(const FString& BodyID, float Mass)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->Mass = Mass;
        
        if (auto* Component = PhysicsComponents.Find(BodyID))
        {
            if (Component->IsValid())
            {
                Component->Get()->SetMassOverrideInKg(NAME_None, Mass, true);
            }
        }
    }
}

void APCGChaosIntegrator::SetBodyDensity(const FString& BodyID, float Density)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->Density = Density;
        
        // Recalculate mass based on density and volume
        // Calculate volume based on body type and geometry
        float Volume = CalculateBodyVolume(BodyID);
        if (Volume <= 0.0f)
        {
            Volume = 1000.0f; // Default volume in cm³ (UE5.6 units)
        }
        SetBodyMass(BodyID, Density * Volume);
    }
}

void APCGChaosIntegrator::SetBodyFriction(const FString& BodyID, float Friction)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->Friction = Friction;
        
        // Update physics material if needed
        // Note: Actual material update would go here
    }
}

void APCGChaosIntegrator::SetBodyRestitution(const FString& BodyID, float Restitution)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->Restitution = Restitution;
        
        // Update physics material if needed
        // Note: Actual material update would go here
    }
}

void APCGChaosIntegrator::SetBodyLinearDamping(const FString& BodyID, float LinearDamping)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->LinearDamping = LinearDamping;
        
        if (auto* Component = PhysicsComponents.Find(BodyID))
        {
            if (Component->IsValid())
            {
                Component->Get()->SetLinearDamping(LinearDamping);
            }
        }
    }
}

void APCGChaosIntegrator::SetBodyAngularDamping(const FString& BodyID, float AngularDamping)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->AngularDamping = AngularDamping;
        
        if (auto* Component = PhysicsComponents.Find(BodyID))
        {
            if (Component->IsValid())
            {
                Component->Get()->SetAngularDamping(AngularDamping);
            }
        }
    }
}

void APCGChaosIntegrator::SetBodyKinematic(const FString& BodyID, bool bKinematic)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->bIsKinematic = bKinematic;
        BodyData->SimulationMode = bKinematic ? EPCGPhysicsSimulationMode::Kinematic : EPCGPhysicsSimulationMode::Dynamic;
        
        if (auto* Component = PhysicsComponents.Find(BodyID))
        {
            if (Component->IsValid())
            {
                Component->Get()->SetSimulatePhysics(!bKinematic);
            }
        }
    }
}

// Physics Body Forces
void APCGChaosIntegrator::AddForceToBody(const FString& BodyID, const FVector& Force, bool bAccelChange)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->AddForce(Force, NAME_None, bAccelChange);
        }
    }
}

void APCGChaosIntegrator::AddForceAtLocation(const FString& BodyID, const FVector& Force, const FVector& Location)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->AddForceAtLocation(Force, Location);
        }
    }
}

void APCGChaosIntegrator::AddTorqueToBody(const FString& BodyID, const FVector& Torque, bool bAccelChange)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->AddTorqueInRadians(Torque, NAME_None, bAccelChange);
        }
    }
}

void APCGChaosIntegrator::AddImpulseToBody(const FString& BodyID, const FVector& Impulse, bool bVelChange)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->AddImpulse(Impulse, NAME_None, bVelChange);
        }
    }
}

void APCGChaosIntegrator::AddImpulseAtLocation(const FString& BodyID, const FVector& Impulse, const FVector& Location)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->AddImpulseAtLocation(Impulse, Location);
        }
    }
}

void APCGChaosIntegrator::AddAngularImpulseToBody(const FString& BodyID, const FVector& AngularImpulse, bool bVelChange)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->AddAngularImpulseInRadians(AngularImpulse, NAME_None, bVelChange);
        }
    }
}

// Physics Body State
FVector APCGChaosIntegrator::GetBodyLinearVelocity(const FString& BodyID) const
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            return Component->Get()->GetPhysicsLinearVelocity();
        }
    }
    
    return FVector::ZeroVector;
}

FVector APCGChaosIntegrator::GetBodyAngularVelocity(const FString& BodyID) const
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            return Component->Get()->GetPhysicsAngularVelocityInRadians();
        }
    }
    
    return FVector::ZeroVector;
}

void APCGChaosIntegrator::SetBodyLinearVelocity(const FString& BodyID, const FVector& Velocity)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->SetPhysicsLinearVelocity(Velocity);
        }
    }
    
    // Update stored data
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->LinearVelocity = Velocity;
    }
}

void APCGChaosIntegrator::SetBodyAngularVelocity(const FString& BodyID, const FVector& AngularVelocity)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->SetPhysicsAngularVelocityInRadians(AngularVelocity);
        }
    }
    
    // Update stored data
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->AngularVelocity = AngularVelocity;
    }
}

bool APCGChaosIntegrator::IsBodyAwake(const FString& BodyID) const
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            return Component->Get()->IsAnyRigidBodyAwake();
        }
    }
    
    return false;
}

void APCGChaosIntegrator::WakeBody(const FString& BodyID)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->WakeRigidBody();
        }
    }
    
    // Update stored data
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->bIsAwake = true;
    }
}

void APCGChaosIntegrator::PutBodyToSleep(const FString& BodyID)
{
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            Component->Get()->PutRigidBodyToSleep();
        }
    }
    
    // Update stored data
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->bIsAwake = false;
    }
}

// Constraint Management
FString APCGChaosIntegrator::CreatePhysicsConstraint(const FPCGPhysicsConstraintData& ConstraintData)
{
    if (!bPhysicsInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGChaosIntegrator: Cannot create constraint - physics not initialized"));
        return FString();
    }
    
    // Validate that both bodies exist
    if (!PhysicsBodies.Contains(ConstraintData.BodyA_ID) || !PhysicsBodies.Contains(ConstraintData.BodyB_ID))
    {
        UE_LOG(LogTemp, Error, TEXT("APCGChaosIntegrator: Cannot create constraint - one or both bodies not found"));
        return FString();
    }
    
    // Generate unique ID
    FString ConstraintID = GenerateUniqueConstraintID();
    
    // Create constraint component
    UPhysicsConstraintComponent* ConstraintComponent = NewObject<UPhysicsConstraintComponent>(this);
    if (!ConstraintComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGChaosIntegrator: Failed to create constraint component"));
        return FString();
    }
    
    // Configure constraint
    auto* ComponentA = PhysicsComponents.Find(ConstraintData.BodyA_ID);
    auto* ComponentB = PhysicsComponents.Find(ConstraintData.BodyB_ID);
    
    if (ComponentA && ComponentB && ComponentA->IsValid() && ComponentB->IsValid())
    {
        ConstraintComponent->SetConstrainedComponents(ComponentA->Get(), NAME_None, ComponentB->Get(), NAME_None);
        
        // Configure constraint properties based on type
        switch (ConstraintData.ConstraintType)
        {
            case EPCGPhysicsConstraintType::Fixed:
                ConstraintComponent->SetLinearXLimit(ELinearConstraintMotion::LCM_Locked, 0.0f);
                ConstraintComponent->SetLinearYLimit(ELinearConstraintMotion::LCM_Locked, 0.0f);
                ConstraintComponent->SetLinearZLimit(ELinearConstraintMotion::LCM_Locked, 0.0f);
                ConstraintComponent->SetAngularSwing1Limit(EAngularConstraintMotion::ACM_Locked, 0.0f);
                ConstraintComponent->SetAngularSwing2Limit(EAngularConstraintMotion::ACM_Locked, 0.0f);
                ConstraintComponent->SetAngularTwistLimit(EAngularConstraintMotion::ACM_Locked, 0.0f);
                break;
                
            case EPCGPhysicsConstraintType::Hinge:
                ConstraintComponent->SetLinearXLimit(ELinearConstraintMotion::LCM_Locked, 0.0f);
                ConstraintComponent->SetLinearYLimit(ELinearConstraintMotion::LCM_Locked, 0.0f);
                ConstraintComponent->SetLinearZLimit(ELinearConstraintMotion::LCM_Locked, 0.0f);
                ConstraintComponent->SetAngularSwing1Limit(EAngularConstraintMotion::ACM_Locked, 0.0f);
                ConstraintComponent->SetAngularSwing2Limit(EAngularConstraintMotion::ACM_Locked, 0.0f);
                ConstraintComponent->SetAngularTwistLimit(EAngularConstraintMotion::ACM_Free, 0.0f);
                break;
                
            case EPCGPhysicsConstraintType::Distance:
                ConstraintComponent->SetLinearXLimit(ELinearConstraintMotion::LCM_Limited, ConstraintData.LinearLimit);
                ConstraintComponent->SetLinearYLimit(ELinearConstraintMotion::LCM_Limited, ConstraintData.LinearLimit);
                ConstraintComponent->SetLinearZLimit(ELinearConstraintMotion::LCM_Limited, ConstraintData.LinearLimit);
                break;
                
            default:
                // Default configuration
                break;
        }
        
        // Set break limits if enabled
        if (ConstraintData.bEnableBreaking)
        {
            ConstraintComponent->SetLinearBreakable(true, ConstraintData.BreakForce);
            ConstraintComponent->SetAngularBreakable(true, ConstraintData.BreakTorque);
        }
    }
    
    // Store constraint data
    FPCGPhysicsConstraintData StoredConstraintData = ConstraintData;
    StoredConstraintData.ConstraintID = ConstraintID;
    PhysicsConstraints.Add(ConstraintID, StoredConstraintData);
    
    // Broadcast event
    OnPhysicsConstraintCreated.Broadcast(StoredConstraintData);
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Created physics constraint %s"), *ConstraintID);
    
    return ConstraintID;
}

bool APCGChaosIntegrator::DestroyPhysicsConstraint(const FString& ConstraintID)
{
    if (!PhysicsConstraints.Contains(ConstraintID))
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Physics constraint %s not found"), *ConstraintID);
        return false;
    }
    
    // Remove from storage
    PhysicsConstraints.Remove(ConstraintID);
    
    // Broadcast event
    OnPhysicsConstraintBroken.Broadcast(ConstraintID);
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Destroyed physics constraint %s"), *ConstraintID);
    
    return true;
}

bool APCGChaosIntegrator::UpdatePhysicsConstraint(const FString& ConstraintID, const FPCGPhysicsConstraintData& NewConstraintData)
{
    if (!PhysicsConstraints.Contains(ConstraintID))
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Physics constraint %s not found"), *ConstraintID);
        return false;
    }
    
    // Update stored data
    FPCGPhysicsConstraintData UpdatedConstraintData = NewConstraintData;
    UpdatedConstraintData.ConstraintID = ConstraintID;
    PhysicsConstraints[ConstraintID] = UpdatedConstraintData;
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Updated physics constraint %s"), *ConstraintID);
    
    return true;
}

FPCGPhysicsConstraintData APCGChaosIntegrator::GetPhysicsConstraintData(const FString& ConstraintID) const
{
    if (const FPCGPhysicsConstraintData* ConstraintData = PhysicsConstraints.Find(ConstraintID))
    {
        return *ConstraintData;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Physics constraint %s not found"), *ConstraintID);
    return FPCGPhysicsConstraintData();
}

TArray<FString> APCGChaosIntegrator::GetAllPhysicsConstraints() const
{
    TArray<FString> ConstraintIDs;
    PhysicsConstraints.GetKeys(ConstraintIDs);
    return ConstraintIDs;
}

TArray<FString> APCGChaosIntegrator::GetConstraintsForBody(const FString& BodyID) const
{
    TArray<FString> ConstraintIDs;
    
    for (const auto& ConstraintPair : PhysicsConstraints)
    {
        const FPCGPhysicsConstraintData& ConstraintData = ConstraintPair.Value;
        if (ConstraintData.BodyA_ID == BodyID || ConstraintData.BodyB_ID == BodyID)
        {
            ConstraintIDs.Add(ConstraintPair.Key);
        }
    }
    
    return ConstraintIDs;
}

// Collision Detection
bool APCGChaosIntegrator::LineTrace(const FVector& Start, const FVector& End, FVector& HitLocation, FString& HitBodyID) const
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }
    
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = true;
    
    bool bHit = World->LineTraceSingleByChannel(HitResult, Start, End, ECC_WorldStatic, QueryParams);
    
    if (bHit)
    {
        HitLocation = HitResult.Location;
        
        // Find the body ID for the hit component
        for (const auto& ComponentPair : PhysicsComponents)
        {
            if (ComponentPair.Value.IsValid() && ComponentPair.Value.Get() == HitResult.Component.Get())
            {
                HitBodyID = ComponentPair.Key;
                break;
            }
        }
    }
    
    return bHit;
}

bool APCGChaosIntegrator::SphereTrace(const FVector& Start, const FVector& End, float Radius, TArray<FString>& HitBodyIDs) const
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }
    
    TArray<FHitResult> HitResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    
    FCollisionShape SphereShape = FCollisionShape::MakeSphere(Radius);
    bool bHit = World->SweepMultiByChannel(HitResults, Start, End, FQuat::Identity, ECC_WorldStatic, SphereShape, QueryParams);
    
    if (bHit)
    {
        for (const FHitResult& HitResult : HitResults)
        {
            // Find the body ID for the hit component
            for (const auto& ComponentPair : PhysicsComponents)
            {
                if (ComponentPair.Value.IsValid() && ComponentPair.Value.Get() == HitResult.Component.Get())
                {
                    HitBodyIDs.AddUnique(ComponentPair.Key);
                    break;
                }
            }
        }
    }
    
    return bHit;
}

bool APCGChaosIntegrator::BoxTrace(const FVector& Start, const FVector& End, const FVector& HalfExtents, TArray<FString>& HitBodyIDs) const
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }
    
    TArray<FHitResult> HitResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    
    FCollisionShape BoxShape = FCollisionShape::MakeBox(HalfExtents);
    bool bHit = World->SweepMultiByChannel(HitResults, Start, End, FQuat::Identity, ECC_WorldStatic, BoxShape, QueryParams);
    
    if (bHit)
    {
        for (const FHitResult& HitResult : HitResults)
        {
            // Find the body ID for the hit component
            for (const auto& ComponentPair : PhysicsComponents)
            {
                if (ComponentPair.Value.IsValid() && ComponentPair.Value.Get() == HitResult.Component.Get())
                {
                    HitBodyIDs.AddUnique(ComponentPair.Key);
                    break;
                }
            }
        }
    }
    
    return bHit;
}

TArray<FString> APCGChaosIntegrator::GetOverlappingBodies(const FString& BodyID) const
{
    TArray<FString> OverlappingBodyIDs;
    
    if (auto* Component = PhysicsComponents.Find(BodyID))
    {
        if (Component->IsValid())
        {
            TArray<UPrimitiveComponent*> OverlappingComponents;
            Component->Get()->GetOverlappingComponents(OverlappingComponents);
            
            for (UPrimitiveComponent* OverlappingComponent : OverlappingComponents)
            {
                // Find the body ID for the overlapping component
                for (const auto& ComponentPair : PhysicsComponents)
                {
                    if (ComponentPair.Value.IsValid() && ComponentPair.Value.Get() == OverlappingComponent)
                    {
                        OverlappingBodyIDs.Add(ComponentPair.Key);
                        break;
                    }
                }
            }
        }
    }
    
    return OverlappingBodyIDs;
}

bool APCGChaosIntegrator::AreOverlapping(const FString& BodyA_ID, const FString& BodyB_ID) const
{
    auto* ComponentA = PhysicsComponents.Find(BodyA_ID);
    auto* ComponentB = PhysicsComponents.Find(BodyB_ID);
    
    if (ComponentA && ComponentB && ComponentA->IsValid() && ComponentB->IsValid())
    {
        return ComponentA->Get()->IsOverlappingComponent(ComponentB->Get());
    }
    
    return false;
}

// Performance and Optimization
FPCGPhysicsPerformanceStats APCGChaosIntegrator::GetPerformanceStatistics() const
{
    return CurrentStats;
}

void APCGChaosIntegrator::OptimizePhysicsPerformance()
{
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Optimizing physics performance"));
    
    // Apply various optimization strategies
    ApplyPhysicsOptimizations();
    OptimizePhysicsMemory();
    OptimizeConstraints();
    
    // Update physics quality based on performance
    UpdatePhysicsQuality();
    
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics performance optimization completed"));
}

void APCGChaosIntegrator::SetPhysicsLOD(const FString& BodyID, int32 LODLevel)
{
    if (auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        BodyData->LODLevel = LODLevel;
        
        // Apply LOD-specific optimizations
        if (auto* Component = PhysicsComponents.Find(BodyID))
        {
            if (Component->IsValid())
            {
                // Adjust physics quality based on LOD level
                switch (LODLevel)
                {
                    case 0: // High quality
                        Component->Get()->SetSimulatePhysics(true);
                        break;
                    case 1: // Medium quality
                        Component->Get()->SetSimulatePhysics(true);
                        break;
                    case 2: // Low quality
                        Component->Get()->SetSimulatePhysics(false);
                        break;
                    default:
                        Component->Get()->SetSimulatePhysics(false);
                        break;
                }
            }
        }
    }
}

void APCGChaosIntegrator::EnablePhysicsCulling(bool bEnable)
{
    PhysicsConfig.bEnablePhysicsCulling = bEnable;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Physics culling %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void APCGChaosIntegrator::SetCullingDistance(float Distance)
{
    PhysicsConfig.CullingDistance = Distance;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Culling distance set to %.2f"), (double)Distance);
}

void APCGChaosIntegrator::UpdatePhysicsLOD(const FVector& ViewerLocation)
{
    if (!PhysicsConfig.bEnablePhysicsLOD)
    {
        return;
    }
    
    for (auto& BodyPair : PhysicsBodies)
    {
        FPCGPhysicsBodyData& BodyData = BodyPair.Value;
        float Distance = FVector::Dist(ViewerLocation, BodyData.Position);
        
        int32 NewLODLevel = 0;
        if (Distance > 2000.0f)
        {
            NewLODLevel = 2; // Low quality
        }
        else if (Distance > 1000.0f)
        {
            NewLODLevel = 1; // Medium quality
        }
        else
        {
            NewLODLevel = 0; // High quality
        }
        
        if (NewLODLevel != BodyData.LODLevel)
        {
            SetPhysicsLOD(BodyPair.Key, NewLODLevel);
        }
    }
}

// Internal functions
void APCGChaosIntegrator::UpdatePerformanceStatistics()
{
    // Calculate average frame time
    float TotalFrameTime = 0.0f;
    for (float FrameTime : FrameTimeHistory)
    {
        TotalFrameTime += FrameTime;
    }
    CurrentStats.AverageFrameTime = TotalFrameTime / FrameTimeHistory.Num();
    CurrentStats.ActualFrameRate = 1.0f / CurrentStats.AverageFrameTime;
    
    // Update body counts
    CurrentStats.ActiveRigidBodies = 0;
    CurrentStats.SleepingRigidBodies = 0;
    
    for (const auto& BodyPair : PhysicsBodies)
    {
        if (BodyPair.Value.bIsAwake)
        {
            CurrentStats.ActiveRigidBodies++;
        }
        else
        {
            CurrentStats.SleepingRigidBodies++;
        }
    }
    
    // Update constraint count
    CurrentStats.ActiveConstraints = PhysicsConstraints.Num();
    
    // Calculate physics utilization
    CurrentStats.PhysicsUtilization = FMath::Clamp(CurrentStats.AverageFrameTime / (1.0f / CurrentStats.TargetFrameRate), 0.0f, 1.0f);
    
    // Estimate memory usage (simplified)
    CurrentStats.PhysicsMemoryUsageMB = (PhysicsBodies.Num() * sizeof(FPCGPhysicsBodyData) + 
                                        PhysicsConstraints.Num() * sizeof(FPCGPhysicsConstraintData)) / (1024 * 1024);
    
    // Broadcast performance update
    OnPhysicsPerformanceUpdated.Broadcast(CurrentStats);
}

void APCGChaosIntegrator::ProcessPhysicsEvents()
{
    // Process and handle recent physics events
    for (const FPCGPhysicsEventData& EventData : RecentEvents)
    {
        HandlePhysicsEvent(EventData);
    }
    
    // Clear processed events
    RecentEvents.Empty();
}

void APCGChaosIntegrator::UpdatePhysicsLODInternal()
{
    if (!PhysicsConfig.bEnablePhysicsLOD)
    {
        return;
    }
    
    // Get viewer location (simplified - would use actual camera location)
    FVector ViewerLocation = GetActorLocation();
    UpdatePhysicsLOD(ViewerLocation);
}

void APCGChaosIntegrator::OptimizePhysicsMemory()
{
    // Remove invalid component references
    TArray<FString> InvalidBodies;
    for (const auto& ComponentPair : PhysicsComponents)
    {
        if (!ComponentPair.Value.IsValid())
        {
            InvalidBodies.Add(ComponentPair.Key);
        }
    }
    
    for (const FString& BodyID : InvalidBodies)
    {
        PhysicsComponents.Remove(BodyID);
        PhysicsBodies.Remove(BodyID);
    }
    
    // Compact arrays
    PhysicsComponents.Compact();
    PhysicsBodies.Compact();
    PhysicsConstraints.Compact();
}

void APCGChaosIntegrator::CullDistantPhysicsBodies(const FVector& ViewerLocation)
{
    if (!PhysicsConfig.bEnablePhysicsCulling)
    {
        return;
    }
    
    for (auto& BodyPair : PhysicsBodies)
    {
        FPCGPhysicsBodyData& BodyData = BodyPair.Value;
        float Distance = FVector::Dist(ViewerLocation, BodyData.Position);
        
        if (auto* Component = PhysicsComponents.Find(BodyPair.Key))
        {
            if (Component->IsValid())
            {
                bool bShouldSimulate = Distance <= PhysicsConfig.CullingDistance;
                Component->Get()->SetSimulatePhysics(bShouldSimulate && bSimulationRunning);
            }
        }
    }
}

FString APCGChaosIntegrator::GenerateUniqueBodyID() const
{
    return FString::Printf(TEXT("PCGBody_%d_%s"), PhysicsBodies.Num(), *FDateTime::Now().ToString());
}

FString APCGChaosIntegrator::GenerateUniqueConstraintID() const
{
    return FString::Printf(TEXT("PCGConstraint_%d_%s"), PhysicsConstraints.Num(), *FDateTime::Now().ToString());
}

UPrimitiveComponent* APCGChaosIntegrator::CreatePhysicsComponent(const FPCGPhysicsBodyData& BodyData)
{
    UPrimitiveComponent* Component = nullptr;
    
    switch (BodyData.BodyType)
    {
        case EPCGPhysicsBodyType::Box:
        {
            UBoxComponent* BoxComponent = NewObject<UBoxComponent>(this);
            BoxComponent->SetBoxExtent(BodyData.Dimensions * 0.5f);
            Component = BoxComponent;
            break;
        }
        
        case EPCGPhysicsBodyType::Sphere:
        {
            USphereComponent* SphereComponent = NewObject<USphereComponent>(this);
            SphereComponent->SetSphereRadius(BodyData.Radius);
            Component = SphereComponent;
            break;
        }
        
        case EPCGPhysicsBodyType::Capsule:
        {
            UCapsuleComponent* CapsuleComponent = NewObject<UCapsuleComponent>(this);
            CapsuleComponent->SetCapsuleSize(BodyData.Radius, BodyData.Height * 0.5f);
            Component = CapsuleComponent;
            break;
        }
        
        default:
        {
            // Default to box
            UBoxComponent* BoxComponent = NewObject<UBoxComponent>(this);
            BoxComponent->SetBoxExtent(BodyData.Dimensions * 0.5f);
            Component = BoxComponent;
            break;
        }
    }
    
    if (Component)
    {
        // Attach to root component
        Component->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepWorldTransform);
        
        // Set transform
        FTransform Transform(BodyData.Rotation, BodyData.Position, BodyData.Scale);
        Component->SetWorldTransform(Transform);
    }
    
    return Component;
}

void APCGChaosIntegrator::ConfigurePhysicsComponent(UPrimitiveComponent* Component, const FPCGPhysicsBodyData& BodyData)
{
    if (!Component)
    {
        return;
    }
    
    // Set physics properties
    Component->SetSimulatePhysics(BodyData.SimulationMode == EPCGPhysicsSimulationMode::Dynamic);
    Component->SetMassOverrideInKg(NAME_None, BodyData.Mass, true);
    Component->SetLinearDamping(BodyData.LinearDamping);
    Component->SetAngularDamping(BodyData.AngularDamping);
    
    // Set collision properties
    Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    Component->SetCollisionObjectType(ECC_WorldDynamic);
    Component->SetCollisionResponseToAllChannels(ECR_Block);
    
    // Set physical material if provided
    if (BodyData.PhysicalMaterial)
    {
        Component->SetPhysMaterialOverride(BodyData.PhysicalMaterial);
    }
}

void APCGChaosIntegrator::UpdateIntegratedSystems()
{
    // Update integration with other PCG systems
    if (PerformanceProfilerRef.IsValid())
    {
        // Send performance data to profiler
        // Note: Actual integration would go here
    }
    
    if (CacheManagerRef.IsValid())
    {
        // Cache physics state if needed
        // Note: Actual caching would go here
    }
}

void APCGChaosIntegrator::HandlePhysicsEvent(const FPCGPhysicsEventData& EventData)
{
    // Broadcast the event
    OnPhysicsEvent.Broadcast(EventData);
    
    // Handle specific event types
    switch (EventData.EventType)
    {
        case EPCGPhysicsEventType::Collision:
            // Handle collision event
            break;
            
        case EPCGPhysicsEventType::Break:
            // Handle constraint break event
            if (!EventData.BodyA_ID.IsEmpty())
            {
                // Remove broken constraint
                // Note: Actual constraint removal would go here
            }
            break;
            
        default:
            break;
    }
}

void APCGChaosIntegrator::CleanupPhysicsResources()
{
    // Destroy all physics components
    for (auto& ComponentPair : PhysicsComponents)
    {
        if (ComponentPair.Value.IsValid())
        {
            ComponentPair.Value->DestroyComponent();
        }
    }
    
    PhysicsComponents.Empty();
}

void APCGChaosIntegrator::ValidatePhysicsConfiguration()
{
    // Validate and clamp configuration values
    PhysicsConfig.TimeStep = FMath::Clamp(PhysicsConfig.TimeStep, 0.001f, 0.1f);
    PhysicsConfig.MaxSubSteps = FMath::Clamp(PhysicsConfig.MaxSubSteps, 1, 20);
    PhysicsConfig.SolverIterations = FMath::Clamp(PhysicsConfig.SolverIterations, 1, 50);
    PhysicsConfig.CullingDistance = FMath::Max(PhysicsConfig.CullingDistance, 100.0f);
    PhysicsConfig.MaxActiveRigidBodies = FMath::Max(PhysicsConfig.MaxActiveRigidBodies, 10);
    PhysicsConfig.MaxActiveConstraints = FMath::Max(PhysicsConfig.MaxActiveConstraints, 5);
}

void APCGChaosIntegrator::ApplyPhysicsOptimizations()
{
    // Apply optimization strategies based on configuration
    switch (PhysicsConfig.OptimizationStrategy)
    {
        case EPCGPhysicsOptimization::LOD:
            PhysicsConfig.bEnablePhysicsLOD = true;
            break;
            
        case EPCGPhysicsOptimization::Culling:
            PhysicsConfig.bEnablePhysicsCulling = true;
            break;
            
        case EPCGPhysicsOptimization::Hybrid:
            PhysicsConfig.bEnablePhysicsLOD = true;
            PhysicsConfig.bEnablePhysicsCulling = true;
            break;
            
        default:
            break;
    }
}

void APCGChaosIntegrator::UpdateAsyncPhysics()
{
    if (!PhysicsConfig.bEnableAsyncPhysics)
    {
        return;
    }
    
    // Update async physics processing
    // Note: Actual async physics implementation would go here
}

void APCGChaosIntegrator::ProcessPhysicsIslands()
{
    // Process physics islands for optimization
    // Note: Actual island processing would go here
}

void APCGChaosIntegrator::UpdatePhysicsQuality()
{
    // Adjust physics quality based on performance
    if (CurrentStats.ActualFrameRate < CurrentStats.TargetFrameRate * 0.8f)
    {
        // Reduce physics quality
        PhysicsConfig.SolverIterations = FMath::Max(PhysicsConfig.SolverIterations - 1, 1);
        PhysicsConfig.MaxSubSteps = FMath::Max(PhysicsConfig.MaxSubSteps - 1, 1);
    }
    else if (CurrentStats.ActualFrameRate > CurrentStats.TargetFrameRate * 1.1f)
    {
        // Increase physics quality
        PhysicsConfig.SolverIterations = FMath::Min(PhysicsConfig.SolverIterations + 1, 20);
        PhysicsConfig.MaxSubSteps = FMath::Min(PhysicsConfig.MaxSubSteps + 1, 10);
    }
}

void APCGChaosIntegrator::OptimizeConstraints()
{
    // Remove broken or invalid constraints
    TArray<FString> ConstraintsToRemove;
    
    for (const auto& ConstraintPair : PhysicsConstraints)
    {
        const FPCGPhysicsConstraintData& ConstraintData = ConstraintPair.Value;
        
        // Check if both bodies still exist
        if (!PhysicsBodies.Contains(ConstraintData.BodyA_ID) || !PhysicsBodies.Contains(ConstraintData.BodyB_ID))
        {
            ConstraintsToRemove.Add(ConstraintPair.Key);
        }
    }
    
    for (const FString& ConstraintID : ConstraintsToRemove)
    {
        DestroyPhysicsConstraint(ConstraintID);
    }
}

void APCGChaosIntegrator::MonitorPhysicsPerformance()
{
    // Monitor for performance issues
    if (CurrentStats.ActualFrameRate < 30.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: Low frame rate detected: %.2f FPS"), (double)CurrentStats.ActualFrameRate);
        
        // Trigger automatic optimization
        OptimizePhysicsPerformance();
    }
    
    if (CurrentStats.PhysicsMemoryUsageMB > 2000) // 2GB threshold
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator: High memory usage detected: %.2f MB"), (double)CurrentStats.PhysicsMemoryUsageMB);
        
        // Trigger memory cleanup
        HandleMemoryPressure();
    }
}

void APCGChaosIntegrator::HandleMemoryPressure()
{
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Handling memory pressure"));
    
    // Optimize memory usage
    OptimizePhysicsMemory();
    
    // Put distant bodies to sleep
    FVector ViewerLocation = GetActorLocation();
    for (auto& BodyPair : PhysicsBodies)
    {
        float Distance = FVector::Dist(ViewerLocation, BodyPair.Value.Position);
        if (Distance > PhysicsConfig.CullingDistance * 0.5f)
        {
            PutBodyToSleep(BodyPair.Key);
        }
    }
    
    // Reduce physics quality temporarily
    PhysicsConfig.SolverIterations = FMath::Max(PhysicsConfig.SolverIterations / 2, 1);
    PhysicsConfig.MaxSubSteps = FMath::Max(PhysicsConfig.MaxSubSteps / 2, 1);
}

// Integration with other PCG systems
void APCGChaosIntegrator::IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager)
{
    WorldPartitionManagerRef = WorldPartitionManager;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Integrated with World Partition Manager"));
}

void APCGChaosIntegrator::IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer)
{
    NaniteOptimizerRef = NaniteOptimizer;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Integrated with Nanite Optimizer"));
}

void APCGChaosIntegrator::IntegrateWithLumen(APCGLumenIntegrator* LumenIntegrator)
{
    LumenIntegratorRef = LumenIntegrator;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Integrated with Lumen Integrator"));
}

void APCGChaosIntegrator::IntegrateWithStreaming(APCGStreamingManager* StreamingManager)
{
    StreamingManagerRef = StreamingManager;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Integrated with Streaming Manager"));
}

void APCGChaosIntegrator::IntegrateWithCache(APCGCacheManager* CacheManager)
{
    CacheManagerRef = CacheManager;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Integrated with Cache Manager"));
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Integrated with Cache Manager"));
}

void APCGChaosIntegrator::IntegrateWithProfiler(UPCGPerformanceProfiler* PerformanceProfiler)
{
    PerformanceProfilerRef = PerformanceProfiler;
    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Integrated with Performance Profiler"));
}

// Helper function to calculate body volume based on geometry
float APCGChaosIntegrator::CalculateBodyVolume(const FString& BodyID) const
{
    if (const auto* BodyData = PhysicsBodies.Find(BodyID))
    {
        switch (BodyData->BodyType)
        {
            case EPCGPhysicsBodyType::Box:
            {
                // Volume = length * width * height
                FVector Scale = BodyData->Scale;
                return Scale.X * Scale.Y * Scale.Z;
            }
            
            case EPCGPhysicsBodyType::Sphere:
            {
                // Volume = (4/3) * π * r³
                float Radius = BodyData->Scale.X; // Assuming uniform scale for sphere
                return (4.0f / 3.0f) * PI * FMath::Pow(Radius, 3.0f);
            }
            
            case EPCGPhysicsBodyType::Capsule:
            {
                // Volume = π * r² * (h + (4/3) * r)
                float Radius = BodyData->Scale.X;
                float Height = BodyData->Scale.Z;
                float CylinderVolume = PI * FMath::Pow(Radius, 2.0f) * Height;
                float SphereVolume = (4.0f / 3.0f) * PI * FMath::Pow(Radius, 3.0f);
                return CylinderVolume + SphereVolume;
            }
            
            case EPCGPhysicsBodyType::Custom:
            {
                // Volume = π * r² * h (cylinder approximation for custom shapes)
                float Radius = BodyData->Scale.X;
                float Height = BodyData->Scale.Z;
                return PI * FMath::Pow(Radius, 2.0f) * Height;
            }
            
            case EPCGPhysicsBodyType::Convex:
            case EPCGPhysicsBodyType::TriangleMesh:
            {
                // For complex meshes, use bounding box approximation
                FVector Scale = BodyData->Scale;
                return Scale.X * Scale.Y * Scale.Z * 0.6f; // Approximate 60% fill ratio
            }
            
            default:
                return 1000.0f; // Default volume in cm³
        }
    }
    
    return 0.0f; // Body not found
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES FALTANTES =====

TArray<FString> APCGChaosIntegrator::GeneratePhysicsBodiesFromMesh(UStaticMesh* Mesh, const FTransform& Transform, int32 Count)
{
    TArray<FString> GeneratedBodyIDs;

    if (!Mesh || Count <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::GeneratePhysicsBodiesFromMesh: Parâmetros inválidos"));
        return GeneratedBodyIDs;
    }

    // Verificar se o mesh tem dados de física
    UBodySetup* BodySetup = Mesh->GetBodySetup();
    if (!BodySetup)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::GeneratePhysicsBodiesFromMesh: Mesh não possui BodySetup"));
        return GeneratedBodyIDs;
    }

    // Gerar múltiplos corpos baseados no mesh
    for (int32 i = 0; i < Count; i++)
    {
        FString BodyID = FString::Printf(TEXT("MeshBody_%s_%d_%d"), *Mesh->GetName(), FMath::Rand(), i);

        // Calcular transform com offset aleatório
        FVector RandomOffset = FVector(
            FMath::RandRange(-500.0f, 500.0f),
            FMath::RandRange(-500.0f, 500.0f),
            FMath::RandRange(-100.0f, 100.0f)
        );

        FVector InstancePosition = Transform.GetLocation() + RandomOffset;
        FRotator InstanceRotation = Transform.GetRotation().Rotator();
        FVector InstanceScale = Transform.GetScale3D();

        // Criar dados do corpo físico
        FPCGPhysicsBodyData BodyData;
        BodyData.BodyID = BodyID;
        BodyData.BodyType = EPCGPhysicsBodyType::Custom;
        BodyData.Position = InstancePosition;
        BodyData.Rotation = InstanceRotation;
        BodyData.Scale = InstanceScale;
        BodyData.Mass = 100.0f * FMath::RandRange(0.8f, 1.2f); // Massa base de 100kg
        BodyData.LinearDamping = PhysicsConfig.LinearDamping;
        BodyData.AngularDamping = PhysicsConfig.AngularDamping;

        // Adicionar à lista de corpos
        PhysicsBodies.Add(BodyID, BodyData);
        GeneratedBodyIDs.Add(BodyID);

        // Criar componente físico se necessário
        if (PhysicsConfig.bEnableProceduralPhysics)
        {
            CreatePhysicsComponent(BodyData);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Gerados %d corpos físicos do mesh %s"), Count, *Mesh->GetName());

    return GeneratedBodyIDs;
}

FString APCGChaosIntegrator::GeneratePhysicsBodyFromGeometry(EPCGPhysicsBodyType BodyType, const FVector& Dimensions, const FTransform& Transform)
{
    FString BodyID = FString::Printf(TEXT("GeomBody_%d_%d"), (int32)BodyType, FMath::Rand());

    // Criar dados do corpo físico
    FPCGPhysicsBodyData BodyData;
    BodyData.BodyID = BodyID;
    BodyData.BodyType = BodyType;
    BodyData.Position = Transform.GetLocation();
    BodyData.Rotation = Transform.GetRotation().Rotator();
    BodyData.Scale = Transform.GetScale3D();
    BodyData.Dimensions = Dimensions;
    BodyData.Mass = 50.0f; // Massa base
    BodyData.LinearDamping = PhysicsConfig.LinearDamping;
    BodyData.AngularDamping = PhysicsConfig.AngularDamping;

    // Configurar propriedades específicas por tipo
    switch (BodyType)
    {
        case EPCGPhysicsBodyType::Box:
            BodyData.Mass *= (Dimensions.X * Dimensions.Y * Dimensions.Z) / 1000000.0f; // Densidade aproximada
            break;
        case EPCGPhysicsBodyType::Sphere:
            BodyData.Mass *= (4.0f/3.0f) * PI * FMath::Pow(Dimensions.X, 3) / 1000000.0f;
            BodyData.Radius = Dimensions.X;
            break;
        case EPCGPhysicsBodyType::Capsule:
            BodyData.Mass *= PI * FMath::Pow(Dimensions.X, 2) * Dimensions.Z / 1000000.0f;
            BodyData.Radius = Dimensions.X;
            BodyData.Height = Dimensions.Z;
            break;
        default:
            break;
    }

    // Adicionar à lista de corpos
    PhysicsBodies.Add(BodyID, BodyData);

    // Criar componente físico se necessário
    if (PhysicsConfig.bEnableProceduralPhysics)
    {
        CreatePhysicsComponent(BodyData);
    }

    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Gerado corpo físico %s (Tipo: %d)"), *BodyID, (int32)BodyType);

    return BodyID;
}

TArray<FString> APCGChaosIntegrator::GeneratePhysicsChain(const TArray<FVector>& Points, EPCGPhysicsConstraintType ConstraintType)
{
    TArray<FString> ChainBodyIDs;

    if (Points.Num() < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::GeneratePhysicsChain: Necessário pelo menos 2 pontos"));
        return ChainBodyIDs;
    }

    // Gerar corpos para cada ponto da cadeia
    for (int32 i = 0; i < Points.Num(); i++)
    {
        FString BodyID = FString::Printf(TEXT("ChainLink_%d_%d"), FMath::Rand(), i);

        // Criar corpo da cadeia
        FPCGPhysicsBodyData BodyData;
        BodyData.BodyID = BodyID;
        BodyData.BodyType = EPCGPhysicsBodyType::Box;
        BodyData.Position = Points[i];
        BodyData.Rotation = FRotator::ZeroRotator;
        BodyData.Scale = FVector::OneVector;
        BodyData.Dimensions = FVector(50.0f, 50.0f, 100.0f); // Link padrão
        BodyData.Mass = 25.0f; // Links mais leves
        BodyData.LinearDamping = PhysicsConfig.LinearDamping;
        BodyData.AngularDamping = PhysicsConfig.AngularDamping;

        PhysicsBodies.Add(BodyID, BodyData);
        ChainBodyIDs.Add(BodyID);

        // Criar componente físico
        if (PhysicsConfig.bEnableProceduralPhysics)
        {
            CreatePhysicsComponent(BodyData);
        }

        // Criar constraint com o link anterior
        if (i > 0)
        {
            FString ConstraintID = FString::Printf(TEXT("ChainConstraint_%d_%d"), FMath::Rand(), i);

            FPCGPhysicsConstraintData ConstraintData;
            ConstraintData.ConstraintID = ConstraintID;
            ConstraintData.ConstraintType = ConstraintType;
            ConstraintData.BodyA_ID = ChainBodyIDs[i-1];
            ConstraintData.BodyB_ID = BodyID;

            // Configurar frames dos constraints
            ConstraintData.FrameA.SetLocation(Points[i-1]);
            ConstraintData.FrameB.SetLocation(Points[i]);

            // Configurar limites baseado no tipo
            switch (ConstraintType)
            {
                case EPCGPhysicsConstraintType::Hinge:
                    ConstraintData.bAngularSwing1Locked = true;
                    ConstraintData.bAngularSwing2Locked = true;
                    ConstraintData.AngularLimit = 90.0f;
                    break;
                case EPCGPhysicsConstraintType::Spherical:
                    ConstraintData.AngularLimit = 45.0f;
                    break;
                case EPCGPhysicsConstraintType::Prismatic:
                    ConstraintData.bLinearXLocked = false;
                    ConstraintData.bLinearYLocked = true;
                    ConstraintData.bLinearZLocked = true;
                    ConstraintData.LinearLimit = 100.0f;
                    break;
                default:
                    break;
            }

            PhysicsConstraints.Add(ConstraintID, ConstraintData);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Gerada cadeia física com %d links"), ChainBodyIDs.Num());

    return ChainBodyIDs;
}

TArray<FString> APCGChaosIntegrator::GeneratePhysicsCluster(const FVector& Center, float Radius, int32 Count, EPCGPhysicsBodyType BodyType)
{
    TArray<FString> ClusterBodyIDs;

    if (Count <= 0 || Radius <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::GeneratePhysicsCluster: Parâmetros inválidos"));
        return ClusterBodyIDs;
    }

    // Gerar corpos em distribuição esférica
    for (int32 i = 0; i < Count; i++)
    {
        FString BodyID = FString::Printf(TEXT("ClusterBody_%d_%d"), FMath::Rand(), i);

        // Posição aleatória dentro da esfera
        FVector RandomDirection = FMath::VRand();
        float RandomDistance = FMath::RandRange(0.0f, Radius);
        FVector Position = Center + (RandomDirection * RandomDistance);

        // Criar corpo do cluster
        FPCGPhysicsBodyData BodyData;
        BodyData.BodyID = BodyID;
        BodyData.BodyType = BodyType;
        BodyData.Position = Position;
        BodyData.Rotation = FRotator(FMath::RandRange(0.0f, 360.0f), FMath::RandRange(0.0f, 360.0f), FMath::RandRange(0.0f, 360.0f));
        BodyData.Scale = FVector::OneVector;

        // Dimensões aleatórias baseadas no tipo
        switch (BodyType)
        {
            case EPCGPhysicsBodyType::Box:
                BodyData.Dimensions = FVector(
                    FMath::RandRange(20.0f, 100.0f),
                    FMath::RandRange(20.0f, 100.0f),
                    FMath::RandRange(20.0f, 100.0f)
                );
                break;
            case EPCGPhysicsBodyType::Sphere:
                BodyData.Radius = FMath::RandRange(10.0f, 50.0f);
                break;
            case EPCGPhysicsBodyType::Capsule:
                BodyData.Radius = FMath::RandRange(10.0f, 30.0f);
                BodyData.Height = FMath::RandRange(50.0f, 150.0f);
                break;
            default:
                BodyData.Dimensions = FVector(50.0f);
                break;
        }

        BodyData.Mass = 50.0f * FMath::RandRange(0.5f, 2.0f);
        BodyData.LinearDamping = PhysicsConfig.LinearDamping;
        BodyData.AngularDamping = PhysicsConfig.AngularDamping;

        PhysicsBodies.Add(BodyID, BodyData);
        ClusterBodyIDs.Add(BodyID);

        // Criar componente físico
        if (PhysicsConfig.bEnableProceduralPhysics)
        {
            CreatePhysicsComponent(BodyData);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Gerado cluster físico com %d corpos"), ClusterBodyIDs.Num());

    return ClusterBodyIDs;
}

void APCGChaosIntegrator::GeneratePhysicsFromPCGData(const TArray<FTransform>& Transforms, EPCGPhysicsBodyType BodyType)
{
    if (Transforms.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::GeneratePhysicsFromPCGData: Array de transforms vazio"));
        return;
    }

    // Gerar corpos para cada transform
    for (int32 i = 0; i < Transforms.Num(); i++)
    {
        FString BodyID = FString::Printf(TEXT("PCGBody_%d_%d"), FMath::Rand(), i);

        // Criar corpo físico
        FPCGPhysicsBodyData BodyData;
        BodyData.BodyID = BodyID;
        BodyData.BodyType = BodyType;
        BodyData.Position = Transforms[i].GetLocation();
        BodyData.Rotation = Transforms[i].GetRotation().Rotator();
        BodyData.Scale = Transforms[i].GetScale3D();

        // Configurar dimensões baseadas na escala do transform
        FVector Scale = Transforms[i].GetScale3D();
        switch (BodyType)
        {
            case EPCGPhysicsBodyType::Box:
                BodyData.Dimensions = FVector(100.0f) * Scale;
                break;
            case EPCGPhysicsBodyType::Sphere:
                BodyData.Radius = 50.0f * Scale.GetMax();
                break;
            case EPCGPhysicsBodyType::Capsule:
                BodyData.Radius = 25.0f * Scale.X;
                BodyData.Height = 100.0f * Scale.Z;
                break;
            default:
                BodyData.Dimensions = FVector(50.0f) * Scale;
                break;
        }

        BodyData.Mass = 50.0f * Scale.GetMax();
        BodyData.LinearDamping = PhysicsConfig.LinearDamping;
        BodyData.AngularDamping = PhysicsConfig.AngularDamping;

        PhysicsBodies.Add(BodyID, BodyData);

        // Criar componente físico
        if (PhysicsConfig.bEnableProceduralPhysics)
        {
            CreatePhysicsComponent(BodyData);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Gerados %d corpos físicos de dados PCG"), Transforms.Num());
}

void APCGChaosIntegrator::EnableAsyncPhysics(bool bEnable)
{
    PhysicsConfig.bEnableAsyncPhysics = bEnable;

    if (bEnable)
    {
        // Configurar processamento assíncrono
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimer(PhysicsUpdateTimer, [this]()
            {
                UpdateAsyncPhysics();
            }, 0.016f, true); // 60 FPS
        }

        UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Física assíncrona habilitada"));
    }
    else
    {
        // Desabilitar processamento assíncrono
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().ClearTimer(PhysicsUpdateTimer);
        }

        UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Física assíncrona desabilitada"));
    }
}

void APCGChaosIntegrator::SetPhysicsQuality(EPCGPhysicsQuality Quality)
{
    PhysicsConfig.QualityLevel = Quality;

    // Aplicar configurações de qualidade
    switch (Quality)
    {
        case EPCGPhysicsQuality::Low:
            PhysicsConfig.MaxSubSteps = 1;
            PhysicsConfig.TimeStep = 0.033f; // 30 FPS
            PhysicsConfig.bEnablePhysicsLOD = true;
            break;
        case EPCGPhysicsQuality::Medium:
            PhysicsConfig.MaxSubSteps = 2;
            PhysicsConfig.TimeStep = 0.016f; // 60 FPS
            PhysicsConfig.bEnablePhysicsLOD = true;
            break;
        case EPCGPhysicsQuality::High:
            PhysicsConfig.MaxSubSteps = 4;
            PhysicsConfig.TimeStep = 0.008f; // 120 FPS
            PhysicsConfig.bEnablePhysicsLOD = false;
            break;
        case EPCGPhysicsQuality::Ultra:
            PhysicsConfig.MaxSubSteps = 8;
            PhysicsConfig.TimeStep = 0.004f; // 240 FPS
            PhysicsConfig.bEnablePhysicsLOD = false;
            break;
    }

    // Aplicar configurações a todos os corpos existentes
    for (auto& BodyPair : PhysicsBodies)
    {
        ApplyQualitySettings(BodyPair.Value);
    }

    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Qualidade de física alterada para %d"), (int32)Quality);
}

void APCGChaosIntegrator::CreatePhysicsIsland(const TArray<FString>& BodyIDs)
{
    if (BodyIDs.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::CreatePhysicsIsland: Lista de corpos vazia"));
        return;
    }

    FString IslandID = FString::Printf(TEXT("PhysicsIsland_%d"), FMath::Rand());

    // Verificar se todos os corpos existem e configurá-los como grupo
    TArray<FString> ValidBodyIDs;
    for (const FString& BodyID : BodyIDs)
    {
        if (!PhysicsBodies.Contains(BodyID))
        {
            UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::CreatePhysicsIsland: Corpo %s não encontrado"), *BodyID);
            continue;
        }

        ValidBodyIDs.Add(BodyID);

        // Configurar propriedades do corpo para fazer parte do grupo
        FPCGPhysicsBodyData& BodyData = PhysicsBodies[BodyID];
        BodyData.LODLevel = 0; // Máxima qualidade para corpos em ilhas

        // Aplicar configurações especiais para corpos agrupados
        if (TWeakObjectPtr<UPrimitiveComponent>* ComponentPtr = PhysicsComponents.Find(BodyID))
        {
            if (UPrimitiveComponent* Component = ComponentPtr->Get())
            {
                // Configurar colisão entre corpos da ilha
                Component->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
                Component->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block);
            }
        }
    }

    if (ValidBodyIDs.Num() > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Criada ilha física %s com %d corpos válidos"), *IslandID, ValidBodyIDs.Num());
    }
}

void APCGChaosIntegrator::DestroyPhysicsIsland(const TArray<FString>& BodyIDs)
{
    if (BodyIDs.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::DestroyPhysicsIsland: Lista de corpos vazia"));
        return;
    }

    // Restaurar configurações individuais dos corpos
    int32 ProcessedBodies = 0;
    for (const FString& BodyID : BodyIDs)
    {
        if (!PhysicsBodies.Contains(BodyID))
        {
            UE_LOG(LogTemp, Warning, TEXT("APCGChaosIntegrator::DestroyPhysicsIsland: Corpo %s não encontrado"), *BodyID);
            continue;
        }

        // Restaurar configurações padrão do corpo
        FPCGPhysicsBodyData& BodyData = PhysicsBodies[BodyID];
        BodyData.LODLevel = 1; // Restaurar LOD padrão

        // Restaurar configurações de colisão padrão
        if (TWeakObjectPtr<UPrimitiveComponent>* ComponentPtr = PhysicsComponents.Find(BodyID))
        {
            if (UPrimitiveComponent* Component = ComponentPtr->Get())
            {
                // Restaurar configurações de colisão padrão
                Component->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
                Component->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block);
                Component->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Block);
            }
        }

        ProcessedBodies++;
    }

    if (ProcessedBodies > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Destruída ilha física com %d corpos processados"), ProcessedBodies);
    }
}

void APCGChaosIntegrator::SetGravity(const FVector& Gravity)
{
    // Aplicar nova gravidade ao mundo se possível
    if (UWorld* World = GetWorld())
    {
        if (AWorldSettings* WorldSettings = World->GetWorldSettings())
        {
            WorldSettings->GlobalGravityZ = Gravity.Z;
        }

        // Aplicar gravidade a todos os componentes físicos
        for (const auto& ComponentPair : PhysicsComponents)
        {
            if (UPrimitiveComponent* Component = ComponentPair.Value.Get())
            {
                Component->SetEnableGravity(true);
                // A gravidade é aplicada automaticamente pelo sistema de física do UE
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("APCGChaosIntegrator: Gravidade alterada para (%f, %f, %f)"), Gravity.X, Gravity.Y, Gravity.Z);
}

FVector APCGChaosIntegrator::GetGravity() const
{
    if (UWorld* World = GetWorld())
    {
        if (AWorldSettings* WorldSettings = World->GetWorldSettings())
        {
            return FVector(0.0f, 0.0f, WorldSettings->GlobalGravityZ);
        }
    }

    return FVector(0.0f, 0.0f, -980.0f); // Gravidade padrão
}

// ===== FUNÇÕES AUXILIARES PARA AS IMPLEMENTAÇÕES =====

void APCGChaosIntegrator::ApplyQualitySettings(FPCGPhysicsBodyData& BodyData)
{
    // Aplicar configurações de qualidade específicas ao corpo
    switch (PhysicsConfig.QualityLevel)
    {
        case EPCGPhysicsQuality::Low:
            BodyData.LinearDamping *= 1.2f; // Mais damping para estabilidade
            BodyData.AngularDamping *= 1.2f;
            BodyData.LODLevel = 2; // LOD mais baixo
            break;
        case EPCGPhysicsQuality::Medium:
            // Configurações padrão
            BodyData.LODLevel = 1;
            break;
        case EPCGPhysicsQuality::High:
            BodyData.LinearDamping *= 0.8f; // Menos damping para mais realismo
            BodyData.AngularDamping *= 0.8f;
            BodyData.LODLevel = 0; // LOD máximo
            break;
        case EPCGPhysicsQuality::Ultra:
            BodyData.LinearDamping *= 0.6f; // Mínimo damping
            BodyData.AngularDamping *= 0.6f;
            BodyData.LODLevel = 0; // LOD máximo
            break;
    }
}