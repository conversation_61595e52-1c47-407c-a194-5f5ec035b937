// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AProceduralMapGenerator.h"
#include "ARiverPrismalManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAProceduralMapGenerator() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AGeometricValidator_NoRegister();
AURA_API UClass* Z_Construct_UClass_ALaneManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AMapManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AMinionWaveManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator();
AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister();
AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EObjectiveType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGDistributionType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGGenerationPhase();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGMapAssetType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPropType();
AURA_API UEnum* Z_Construct_UEnum_Aura_ETeam();
AURA_API UEnum* Z_Construct_UEnum_Aura_EWallMaterial();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FBridgeData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FExclusionZone();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FGenerationProgress();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FLaneGenerationParams();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FObjectiveData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGAssetReference();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationProgress();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGSpawnRule();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPropData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FTerrainGenerationConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FVegetationConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FWallData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGGenerationPhase *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGGenerationPhase;
static UEnum* EPCGGenerationPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGGenerationPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGGenerationPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGGenerationPhase, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGGenerationPhase"));
	}
	return Z_Registration_Info_UEnum_EPCGGenerationPhase.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGGenerationPhase>()
{
	return EPCGGenerationPhase_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== ENUMS =====\n" },
#endif
		{ "Completed.DisplayName", "Conclu\xc3\xad""do" },
		{ "Completed.Name", "EPCGGenerationPhase::Completed" },
		{ "Finalization.DisplayName", "Finaliza\xc3\xa7\xc3\xa3o" },
		{ "Finalization.Name", "EPCGGenerationPhase::Finalization" },
		{ "Initialization.DisplayName", "Inicializa\xc3\xa7\xc3\xa3o" },
		{ "Initialization.Name", "EPCGGenerationPhase::Initialization" },
		{ "Lanes.DisplayName", "Lanes" },
		{ "Lanes.Name", "EPCGGenerationPhase::Lanes" },
		{ "Lighting.DisplayName", "Ilumina\xc3\xa7\xc3\xa3o" },
		{ "Lighting.Name", "EPCGGenerationPhase::Lighting" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "None.DisplayName", "Nenhuma" },
		{ "None.Name", "EPCGGenerationPhase::None" },
		{ "Objectives.DisplayName", "Objetivos" },
		{ "Objectives.Name", "EPCGGenerationPhase::Objectives" },
		{ "Props.DisplayName", "Props" },
		{ "Props.Name", "EPCGGenerationPhase::Props" },
		{ "River.DisplayName", "Rio" },
		{ "River.Name", "EPCGGenerationPhase::River" },
		{ "Terrain.DisplayName", "Terreno" },
		{ "Terrain.Name", "EPCGGenerationPhase::Terrain" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== ENUMS =====" },
#endif
		{ "Vegetation.DisplayName", "Vegeta\xc3\xa7\xc3\xa3o" },
		{ "Vegetation.Name", "EPCGGenerationPhase::Vegetation" },
		{ "Walls.DisplayName", "Paredes" },
		{ "Walls.Name", "EPCGGenerationPhase::Walls" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGGenerationPhase::None", (int64)EPCGGenerationPhase::None },
		{ "EPCGGenerationPhase::Initialization", (int64)EPCGGenerationPhase::Initialization },
		{ "EPCGGenerationPhase::Terrain", (int64)EPCGGenerationPhase::Terrain },
		{ "EPCGGenerationPhase::Lanes", (int64)EPCGGenerationPhase::Lanes },
		{ "EPCGGenerationPhase::Objectives", (int64)EPCGGenerationPhase::Objectives },
		{ "EPCGGenerationPhase::Walls", (int64)EPCGGenerationPhase::Walls },
		{ "EPCGGenerationPhase::River", (int64)EPCGGenerationPhase::River },
		{ "EPCGGenerationPhase::Vegetation", (int64)EPCGGenerationPhase::Vegetation },
		{ "EPCGGenerationPhase::Props", (int64)EPCGGenerationPhase::Props },
		{ "EPCGGenerationPhase::Lighting", (int64)EPCGGenerationPhase::Lighting },
		{ "EPCGGenerationPhase::Finalization", (int64)EPCGGenerationPhase::Finalization },
		{ "EPCGGenerationPhase::Completed", (int64)EPCGGenerationPhase::Completed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGGenerationPhase",
	"EPCGGenerationPhase",
	Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGGenerationPhase()
{
	if (!Z_Registration_Info_UEnum_EPCGGenerationPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGGenerationPhase.InnerSingleton, Z_Construct_UEnum_Aura_EPCGGenerationPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGGenerationPhase.InnerSingleton;
}
// ********** End Enum EPCGGenerationPhase *********************************************************

// ********** Begin Enum EObjectiveType ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EObjectiveType;
static UEnum* EObjectiveType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EObjectiveType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EObjectiveType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EObjectiveType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EObjectiveType"));
	}
	return Z_Registration_Info_UEnum_EObjectiveType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EObjectiveType>()
{
	return EObjectiveType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EObjectiveType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Base.DisplayName", "Base" },
		{ "Base.Name", "EObjectiveType::Base" },
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "Nexus.DisplayName", "Nexus" },
		{ "Nexus.Name", "EObjectiveType::Nexus" },
		{ "Outpost.DisplayName", "Posto Avan\xc3\xa7""ado" },
		{ "Outpost.Name", "EObjectiveType::Outpost" },
		{ "Tower.DisplayName", "Torre" },
		{ "Tower.Name", "EObjectiveType::Tower" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EObjectiveType::Nexus", (int64)EObjectiveType::Nexus },
		{ "EObjectiveType::Base", (int64)EObjectiveType::Base },
		{ "EObjectiveType::Tower", (int64)EObjectiveType::Tower },
		{ "EObjectiveType::Outpost", (int64)EObjectiveType::Outpost },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EObjectiveType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EObjectiveType",
	"EObjectiveType",
	Z_Construct_UEnum_Aura_EObjectiveType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EObjectiveType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EObjectiveType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EObjectiveType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EObjectiveType()
{
	if (!Z_Registration_Info_UEnum_EObjectiveType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EObjectiveType.InnerSingleton, Z_Construct_UEnum_Aura_EObjectiveType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EObjectiveType.InnerSingleton;
}
// ********** End Enum EObjectiveType **************************************************************

// ********** Begin Enum EPropType *****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPropType;
static UEnum* EPropType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPropType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPropType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPropType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPropType"));
	}
	return Z_Registration_Info_UEnum_EPropType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPropType>()
{
	return EPropType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPropType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Barrel.DisplayName", "Barril" },
		{ "Barrel.Name", "EPropType::Barrel" },
		{ "Barricade.DisplayName", "Barricada" },
		{ "Barricade.Name", "EPropType::Barricade" },
		{ "BlueprintType", "true" },
		{ "Crate.DisplayName", "Caixa" },
		{ "Crate.Name", "EPropType::Crate" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "Pillar.DisplayName", "Pilar" },
		{ "Pillar.Name", "EPropType::Pillar" },
		{ "Rock.DisplayName", "Rocha" },
		{ "Rock.Name", "EPropType::Rock" },
		{ "Statue.DisplayName", "Est\xc3\xa1tua" },
		{ "Statue.Name", "EPropType::Statue" },
		{ "WatchTower.DisplayName", "Torre de Vigia" },
		{ "WatchTower.Name", "EPropType::WatchTower" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPropType::Rock", (int64)EPropType::Rock },
		{ "EPropType::Crate", (int64)EPropType::Crate },
		{ "EPropType::Barrel", (int64)EPropType::Barrel },
		{ "EPropType::Pillar", (int64)EPropType::Pillar },
		{ "EPropType::Statue", (int64)EPropType::Statue },
		{ "EPropType::Barricade", (int64)EPropType::Barricade },
		{ "EPropType::WatchTower", (int64)EPropType::WatchTower },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPropType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPropType",
	"EPropType",
	Z_Construct_UEnum_Aura_EPropType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPropType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPropType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPropType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPropType()
{
	if (!Z_Registration_Info_UEnum_EPropType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPropType.InnerSingleton, Z_Construct_UEnum_Aura_EPropType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPropType.InnerSingleton;
}
// ********** End Enum EPropType *******************************************************************

// ********** Begin ScriptStruct FGenerationProgress ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FGenerationProgress;
class UScriptStruct* FGenerationProgress::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FGenerationProgress.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FGenerationProgress.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FGenerationProgress, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("GenerationProgress"));
	}
	return Z_Registration_Info_UScriptStruct_FGenerationProgress.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FGenerationProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura de progresso de gera\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura de progresso de gera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanesGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectivesGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallsGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PropsGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingGenerated_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_TerrainGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_TerrainGenerated;
	static void NewProp_LanesGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_LanesGenerated;
	static void NewProp_ObjectivesGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ObjectivesGenerated;
	static void NewProp_WallsGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_WallsGenerated;
	static void NewProp_RiverGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_RiverGenerated;
	static void NewProp_VegetationGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_VegetationGenerated;
	static void NewProp_PropsGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_PropsGenerated;
	static void NewProp_LightingGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_LightingGenerated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FGenerationProgress>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_TerrainGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->TerrainGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_TerrainGenerated = { "TerrainGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_TerrainGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainGenerated_MetaData), NewProp_TerrainGenerated_MetaData) };
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LanesGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->LanesGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LanesGenerated = { "LanesGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LanesGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanesGenerated_MetaData), NewProp_LanesGenerated_MetaData) };
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_ObjectivesGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->ObjectivesGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_ObjectivesGenerated = { "ObjectivesGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_ObjectivesGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectivesGenerated_MetaData), NewProp_ObjectivesGenerated_MetaData) };
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_WallsGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->WallsGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_WallsGenerated = { "WallsGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_WallsGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallsGenerated_MetaData), NewProp_WallsGenerated_MetaData) };
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_RiverGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->RiverGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_RiverGenerated = { "RiverGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_RiverGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverGenerated_MetaData), NewProp_RiverGenerated_MetaData) };
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_VegetationGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->VegetationGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_VegetationGenerated = { "VegetationGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_VegetationGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationGenerated_MetaData), NewProp_VegetationGenerated_MetaData) };
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_PropsGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->PropsGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_PropsGenerated = { "PropsGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_PropsGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PropsGenerated_MetaData), NewProp_PropsGenerated_MetaData) };
void Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LightingGenerated_SetBit(void* Obj)
{
	((FGenerationProgress*)Obj)->LightingGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LightingGenerated = { "LightingGenerated", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FGenerationProgress), &Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LightingGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingGenerated_MetaData), NewProp_LightingGenerated_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_TerrainGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LanesGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_ObjectivesGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_WallsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_RiverGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_VegetationGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_PropsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewProp_LightingGenerated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FGenerationProgress_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"GenerationProgress",
	Z_Construct_UScriptStruct_FGenerationProgress_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGenerationProgress_Statics::PropPointers),
	sizeof(FGenerationProgress),
	alignof(FGenerationProgress),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGenerationProgress_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FGenerationProgress_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FGenerationProgress()
{
	if (!Z_Registration_Info_UScriptStruct_FGenerationProgress.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FGenerationProgress.InnerSingleton, Z_Construct_UScriptStruct_FGenerationProgress_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FGenerationProgress.InnerSingleton;
}
// ********** End ScriptStruct FGenerationProgress *************************************************

// ********** Begin ScriptStruct FExclusionZone ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FExclusionZone;
class UScriptStruct* FExclusionZone::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FExclusionZone.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FExclusionZone.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FExclusionZone, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ExclusionZone"));
	}
	return Z_Registration_Info_UScriptStruct_FExclusionZone.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FExclusionZone_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para zonas de exclus\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para zonas de exclus\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "Category", "Exclusion Zone" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Exclusion Zone" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bExcludeVegetation_MetaData[] = {
		{ "Category", "Exclusion Zone" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bExcludeProps_MetaData[] = {
		{ "Category", "Exclusion Zone" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_bExcludeVegetation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bExcludeVegetation;
	static void NewProp_bExcludeProps_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bExcludeProps;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FExclusionZone>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FExclusionZone, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FExclusionZone, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
void Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeVegetation_SetBit(void* Obj)
{
	((FExclusionZone*)Obj)->bExcludeVegetation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeVegetation = { "bExcludeVegetation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FExclusionZone), &Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeVegetation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bExcludeVegetation_MetaData), NewProp_bExcludeVegetation_MetaData) };
void Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeProps_SetBit(void* Obj)
{
	((FExclusionZone*)Obj)->bExcludeProps = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeProps = { "bExcludeProps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FExclusionZone), &Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeProps_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bExcludeProps_MetaData), NewProp_bExcludeProps_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FExclusionZone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeVegetation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FExclusionZone_Statics::NewProp_bExcludeProps,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FExclusionZone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FExclusionZone_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"ExclusionZone",
	Z_Construct_UScriptStruct_FExclusionZone_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FExclusionZone_Statics::PropPointers),
	sizeof(FExclusionZone),
	alignof(FExclusionZone),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FExclusionZone_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FExclusionZone_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FExclusionZone()
{
	if (!Z_Registration_Info_UScriptStruct_FExclusionZone.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FExclusionZone.InnerSingleton, Z_Construct_UScriptStruct_FExclusionZone_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FExclusionZone.InnerSingleton;
}
// ********** End ScriptStruct FExclusionZone ******************************************************

// ********** Begin ScriptStruct FTerrainGenerationConfig ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig;
class UScriptStruct* FTerrainGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTerrainGenerationConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("TerrainGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para configura\xc3\xa7\xc3\xa3o de terreno\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de terreno" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightVariation_MetaData[] = {
		{ "Category", "Terrain" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Terrain" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "Terrain" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoisePersistence_MetaData[] = {
		{ "Category", "Terrain" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoisePersistence;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTerrainGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_HeightVariation = { "HeightVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTerrainGenerationConfig, HeightVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightVariation_MetaData), NewProp_HeightVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTerrainGenerationConfig, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTerrainGenerationConfig, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_NoisePersistence = { "NoisePersistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTerrainGenerationConfig, NoisePersistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoisePersistence_MetaData), NewProp_NoisePersistence_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_HeightVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewProp_NoisePersistence,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"TerrainGenerationConfig",
	Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::PropPointers),
	sizeof(FTerrainGenerationConfig),
	alignof(FTerrainGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTerrainGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FTerrainGenerationConfig ********************************************

// ********** Begin ScriptStruct FVegetationConfig *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FVegetationConfig;
class UScriptStruct* FVegetationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FVegetationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FVegetationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FVegetationConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("VegetationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FVegetationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FVegetationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para configura\xc3\xa7\xc3\xa3o de vegeta\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de vegeta\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TreeDensity_MetaData[] = {
		{ "Category", "Vegetation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrassDensity_MetaData[] = {
		{ "Category", "Vegetation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinTreeScale_MetaData[] = {
		{ "Category", "Vegetation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTreeScale_MetaData[] = {
		{ "Category", "Vegetation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TreeDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GrassDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinTreeScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxTreeScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FVegetationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_TreeDensity = { "TreeDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVegetationConfig, TreeDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TreeDensity_MetaData), NewProp_TreeDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_GrassDensity = { "GrassDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVegetationConfig, GrassDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrassDensity_MetaData), NewProp_GrassDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_MinTreeScale = { "MinTreeScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVegetationConfig, MinTreeScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinTreeScale_MetaData), NewProp_MinTreeScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_MaxTreeScale = { "MaxTreeScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVegetationConfig, MaxTreeScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTreeScale_MetaData), NewProp_MaxTreeScale_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FVegetationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_TreeDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_GrassDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_MinTreeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewProp_MaxTreeScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVegetationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FVegetationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"VegetationConfig",
	Z_Construct_UScriptStruct_FVegetationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVegetationConfig_Statics::PropPointers),
	sizeof(FVegetationConfig),
	alignof(FVegetationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVegetationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FVegetationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FVegetationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FVegetationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FVegetationConfig.InnerSingleton, Z_Construct_UScriptStruct_FVegetationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FVegetationConfig.InnerSingleton;
}
// ********** End ScriptStruct FVegetationConfig ***************************************************

// ********** Begin Enum EPCGMapAssetType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGMapAssetType;
static UEnum* EPCGMapAssetType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGMapAssetType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGMapAssetType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGMapAssetType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGMapAssetType"));
	}
	return Z_Registration_Info_UEnum_EPCGMapAssetType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGMapAssetType>()
{
	return EPCGMapAssetType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Audio.DisplayName", "Audio" },
		{ "Audio.Name", "EPCGMapAssetType::Audio" },
		{ "Blueprint.DisplayName", "Blueprint" },
		{ "Blueprint.Name", "EPCGMapAssetType::Blueprint" },
		{ "BlueprintType", "true" },
		{ "Foliage.DisplayName", "Foliage" },
		{ "Foliage.Name", "EPCGMapAssetType::Foliage" },
		{ "Landscape.DisplayName", "Landscape" },
		{ "Landscape.Name", "EPCGMapAssetType::Landscape" },
		{ "Material.DisplayName", "Material" },
		{ "Material.Name", "EPCGMapAssetType::Material" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "Particle.DisplayName", "Particle System" },
		{ "Particle.Name", "EPCGMapAssetType::Particle" },
		{ "SkeletalMesh.DisplayName", "Skeletal Mesh" },
		{ "SkeletalMesh.Name", "EPCGMapAssetType::SkeletalMesh" },
		{ "StaticMesh.DisplayName", "Static Mesh" },
		{ "StaticMesh.Name", "EPCGMapAssetType::StaticMesh" },
		{ "Texture.DisplayName", "Texture" },
		{ "Texture.Name", "EPCGMapAssetType::Texture" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGMapAssetType::StaticMesh", (int64)EPCGMapAssetType::StaticMesh },
		{ "EPCGMapAssetType::SkeletalMesh", (int64)EPCGMapAssetType::SkeletalMesh },
		{ "EPCGMapAssetType::Material", (int64)EPCGMapAssetType::Material },
		{ "EPCGMapAssetType::Texture", (int64)EPCGMapAssetType::Texture },
		{ "EPCGMapAssetType::Landscape", (int64)EPCGMapAssetType::Landscape },
		{ "EPCGMapAssetType::Foliage", (int64)EPCGMapAssetType::Foliage },
		{ "EPCGMapAssetType::Particle", (int64)EPCGMapAssetType::Particle },
		{ "EPCGMapAssetType::Audio", (int64)EPCGMapAssetType::Audio },
		{ "EPCGMapAssetType::Blueprint", (int64)EPCGMapAssetType::Blueprint },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGMapAssetType",
	"EPCGMapAssetType",
	Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGMapAssetType()
{
	if (!Z_Registration_Info_UEnum_EPCGMapAssetType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGMapAssetType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGMapAssetType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGMapAssetType.InnerSingleton;
}
// ********** End Enum EPCGMapAssetType ************************************************************

// ********** Begin Enum EPCGDistributionType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGDistributionType;
static UEnum* EPCGDistributionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGDistributionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGDistributionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGDistributionType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGDistributionType"));
	}
	return Z_Registration_Info_UEnum_EPCGDistributionType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGDistributionType>()
{
	return EPCGDistributionType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGDistributionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Clustered.DisplayName", "Agrupado" },
		{ "Clustered.Name", "EPCGDistributionType::Clustered" },
		{ "Geometric.DisplayName", "Geom\xc3\xa9trico" },
		{ "Geometric.Name", "EPCGDistributionType::Geometric" },
		{ "Grid.DisplayName", "Grade" },
		{ "Grid.Name", "EPCGDistributionType::Grid" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
		{ "Poisson.DisplayName", "Poisson Disk" },
		{ "Poisson.Name", "EPCGDistributionType::Poisson" },
		{ "Random.DisplayName", "Aleat\xc3\xb3rio" },
		{ "Random.Name", "EPCGDistributionType::Random" },
		{ "Weighted.DisplayName", "Ponderado" },
		{ "Weighted.Name", "EPCGDistributionType::Weighted" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGDistributionType::Random", (int64)EPCGDistributionType::Random },
		{ "EPCGDistributionType::Grid", (int64)EPCGDistributionType::Grid },
		{ "EPCGDistributionType::Poisson", (int64)EPCGDistributionType::Poisson },
		{ "EPCGDistributionType::Geometric", (int64)EPCGDistributionType::Geometric },
		{ "EPCGDistributionType::Weighted", (int64)EPCGDistributionType::Weighted },
		{ "EPCGDistributionType::Clustered", (int64)EPCGDistributionType::Clustered },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGDistributionType",
	"EPCGDistributionType",
	Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGDistributionType()
{
	if (!Z_Registration_Info_UEnum_EPCGDistributionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGDistributionType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGDistributionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGDistributionType.InnerSingleton;
}
// ********** End Enum EPCGDistributionType ********************************************************

// ********** Begin ScriptStruct FPCGAssetReference ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGAssetReference;
class UScriptStruct* FPCGAssetReference::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGAssetReference.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGAssetReference.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGAssetReference, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGAssetReference"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGAssetReference.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGAssetReference_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== ESTRUTURAS DE DADOS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== ESTRUTURAS DE DADOS =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetType_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPath_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetName_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weight_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomRotation_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomScale_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleRange_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AssetType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AssetType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetPath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static void NewProp_bRandomRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomRotation;
	static void NewProp_bRandomScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGAssetReference>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType = { "AssetType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetType), Z_Construct_UEnum_Aura_EPCGMapAssetType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetType_MetaData), NewProp_AssetType_MetaData) }; // 2580812996
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetPath = { "AssetPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPath_MetaData), NewProp_AssetPath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetName = { "AssetName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, AssetName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetName_MetaData), NewProp_AssetName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Weight = { "Weight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, Weight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weight_MetaData), NewProp_Weight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
void Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation_SetBit(void* Obj)
{
	((FPCGAssetReference*)Obj)->bRandomRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation = { "bRandomRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGAssetReference), &Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomRotation_MetaData), NewProp_bRandomRotation_MetaData) };
void Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale_SetBit(void* Obj)
{
	((FPCGAssetReference*)Obj)->bRandomScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale = { "bRandomScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGAssetReference), &Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomScale_MetaData), NewProp_bRandomScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_ScaleRange = { "ScaleRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGAssetReference, ScaleRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleRange_MetaData), NewProp_ScaleRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_AssetName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Weight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_bRandomScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewProp_ScaleRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGAssetReference_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGAssetReference",
	Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAssetReference_Statics::PropPointers),
	sizeof(FPCGAssetReference),
	alignof(FPCGAssetReference),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGAssetReference_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGAssetReference_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGAssetReference()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGAssetReference.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGAssetReference.InnerSingleton, Z_Construct_UScriptStruct_FPCGAssetReference_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGAssetReference.InnerSingleton;
}
// ********** End ScriptStruct FPCGAssetReference **************************************************

// ********** Begin ScriptStruct FPCGSpawnRule *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGSpawnRule;
class UScriptStruct* FPCGSpawnRule::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGSpawnRule.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGSpawnRule.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGSpawnRule, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGSpawnRule"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGSpawnRule.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGSpawnRule_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleName_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistributionType_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Assets_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDistance_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDistance_MetaData[] = {
		{ "Category", "Spawn Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 1 metro\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "1 metro" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightRange_MetaData[] = {
		{ "Category", "Spawn Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 5 metros\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "5 metros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlopeRange_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToSurface_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAvoidOverlap_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRespectBounds_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusionTags_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Spawn Rule" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DistributionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DistributionType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Assets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Assets;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeightRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlopeRange;
	static void NewProp_bAlignToSurface_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToSurface;
	static void NewProp_bAvoidOverlap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvoidOverlap;
	static void NewProp_bRespectBounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRespectBounds;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExclusionTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExclusionTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredTags_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGSpawnRule>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RuleName = { "RuleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, RuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleName_MetaData), NewProp_RuleName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType = { "DistributionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, DistributionType), Z_Construct_UEnum_Aura_EPCGDistributionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistributionType_MetaData), NewProp_DistributionType_MetaData) }; // 103216633
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets_Inner = { "Assets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGAssetReference, METADATA_PARAMS(0, nullptr) }; // 3234866593
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets = { "Assets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, Assets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Assets_MetaData), NewProp_Assets_MetaData) }; // 3234866593
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MinDistance = { "MinDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, MinDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDistance_MetaData), NewProp_MinDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MaxDistance = { "MaxDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, MaxDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDistance_MetaData), NewProp_MaxDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_HeightRange = { "HeightRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, HeightRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightRange_MetaData), NewProp_HeightRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_SlopeRange = { "SlopeRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, SlopeRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlopeRange_MetaData), NewProp_SlopeRange_MetaData) };
void Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface_SetBit(void* Obj)
{
	((FPCGSpawnRule*)Obj)->bAlignToSurface = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface = { "bAlignToSurface", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGSpawnRule), &Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToSurface_MetaData), NewProp_bAlignToSurface_MetaData) };
void Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap_SetBit(void* Obj)
{
	((FPCGSpawnRule*)Obj)->bAvoidOverlap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap = { "bAvoidOverlap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGSpawnRule), &Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAvoidOverlap_MetaData), NewProp_bAvoidOverlap_MetaData) };
void Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds_SetBit(void* Obj)
{
	((FPCGSpawnRule*)Obj)->bRespectBounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds = { "bRespectBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGSpawnRule), &Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRespectBounds_MetaData), NewProp_bRespectBounds_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags_Inner = { "ExclusionTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags = { "ExclusionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, ExclusionTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusionTags_MetaData), NewProp_ExclusionTags_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags_Inner = { "RequiredTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSpawnRule, RequiredTags), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_DistributionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Assets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MinDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_MaxDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_HeightRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_SlopeRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAlignToSurface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bAvoidOverlap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_bRespectBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_ExclusionTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewProp_RequiredTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGSpawnRule",
	Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::PropPointers),
	sizeof(FPCGSpawnRule),
	alignof(FPCGSpawnRule),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGSpawnRule()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGSpawnRule.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGSpawnRule.InnerSingleton, Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGSpawnRule.InnerSingleton;
}
// ********** End ScriptStruct FPCGSpawnRule *******************************************************

// ********** Begin ScriptStruct FPCGGenerationConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGGenerationConfig;
class UScriptStruct* FPCGGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGGenerationConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutoGeneration_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRealTimeUpdates_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationSeed_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxIterations_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityThreshold_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultithreading_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadCount_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSaveGenerationData_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputDirectory_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableAutoGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutoGeneration;
	static void NewProp_bEnableRealTimeUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRealTimeUpdates;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationSeed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxIterations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityThreshold;
	static void NewProp_bUseMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bSaveGenerationData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSaveGenerationData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputDirectory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bEnableAutoGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration = { "bEnableAutoGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutoGeneration_MetaData), NewProp_bEnableAutoGeneration_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bEnableRealTimeUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates = { "bEnableRealTimeUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRealTimeUpdates_MetaData), NewProp_bEnableRealTimeUpdates_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_GenerationSeed = { "GenerationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, GenerationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationSeed_MetaData), NewProp_GenerationSeed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_MaxIterations = { "MaxIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, MaxIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxIterations_MetaData), NewProp_MaxIterations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_QualityThreshold = { "QualityThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, QualityThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityThreshold_MetaData), NewProp_QualityThreshold_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bUseMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading = { "bUseMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultithreading_MetaData), NewProp_bUseMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, ThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadCount_MetaData), NewProp_ThreadCount_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData_SetBit(void* Obj)
{
	((FPCGGenerationConfig*)Obj)->bSaveGenerationData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData = { "bSaveGenerationData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGGenerationConfig), &Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSaveGenerationData_MetaData), NewProp_bSaveGenerationData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_OutputDirectory = { "OutputDirectory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationConfig, OutputDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputDirectory_MetaData), NewProp_OutputDirectory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableAutoGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableRealTimeUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_GenerationSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_MaxIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_QualityThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bUseMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_ThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_bSaveGenerationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewProp_OutputDirectory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGGenerationConfig",
	Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::PropPointers),
	sizeof(FPCGGenerationConfig),
	alignof(FPCGGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGGenerationConfig ************************************************

// ********** Begin ScriptStruct FPCGGenerationProgress ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGGenerationProgress;
class UScriptStruct* FPCGGenerationProgress::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGGenerationProgress, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGGenerationProgress"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverallProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedAssets_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalAssets_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedEndTime_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentOperation_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedPhases_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessages_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OverallProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GeneratedAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalAssets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EstimatedEndTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentOperation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompletedPhases_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedPhases;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrorMessages;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGGenerationProgress>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, CurrentPhase), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 2886374832
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_PhaseProgress = { "PhaseProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, PhaseProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseProgress_MetaData), NewProp_PhaseProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_OverallProgress = { "OverallProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, OverallProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverallProgress_MetaData), NewProp_OverallProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_GeneratedAssets = { "GeneratedAssets", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, GeneratedAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedAssets_MetaData), NewProp_GeneratedAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_TotalAssets = { "TotalAssets", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, TotalAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalAssets_MetaData), NewProp_TotalAssets_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, StartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_EstimatedEndTime = { "EstimatedEndTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, EstimatedEndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedEndTime_MetaData), NewProp_EstimatedEndTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentOperation = { "CurrentOperation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, CurrentOperation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentOperation_MetaData), NewProp_CurrentOperation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases_Inner = { "CompletedPhases", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases = { "CompletedPhases", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, CompletedPhases), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedPhases_MetaData), NewProp_CompletedPhases_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages_Inner = { "ErrorMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages = { "ErrorMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGGenerationProgress, ErrorMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessages_MetaData), NewProp_ErrorMessages_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_PhaseProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_OverallProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_GeneratedAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_TotalAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_EstimatedEndTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CurrentOperation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_CompletedPhases,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewProp_ErrorMessages,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGGenerationProgress",
	Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::PropPointers),
	sizeof(FPCGGenerationProgress),
	alignof(FPCGGenerationProgress),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGGenerationProgress()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.InnerSingleton, Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGGenerationProgress.InnerSingleton;
}
// ********** End ScriptStruct FPCGGenerationProgress **********************************************

// ********** Begin ScriptStruct FLaneGenerationParams *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FLaneGenerationParams;
class UScriptStruct* FLaneGenerationParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneGenerationParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FLaneGenerationParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLaneGenerationParams, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("LaneGenerationParams"));
	}
	return Z_Registration_Info_UScriptStruct_FLaneGenerationParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FLaneGenerationParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapBounds_MetaData[] = {
		{ "Category", "Lane Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumLanes_MetaData[] = {
		{ "Category", "Lane Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneWidth_MetaData[] = {
		{ "Category", "Lane Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneSpacing_MetaData[] = {
		{ "Category", "Lane Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapBounds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumLanes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneSpacing;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLaneGenerationParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_MapBounds = { "MapBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGenerationParams, MapBounds), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapBounds_MetaData), NewProp_MapBounds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_NumLanes = { "NumLanes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGenerationParams, NumLanes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumLanes_MetaData), NewProp_NumLanes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_LaneWidth = { "LaneWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGenerationParams, LaneWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneWidth_MetaData), NewProp_LaneWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_LaneSpacing = { "LaneSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGenerationParams, LaneSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneSpacing_MetaData), NewProp_LaneSpacing_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_MapBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_NumLanes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_LaneWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewProp_LaneSpacing,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"LaneGenerationParams",
	Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::PropPointers),
	sizeof(FLaneGenerationParams),
	alignof(FLaneGenerationParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLaneGenerationParams()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneGenerationParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FLaneGenerationParams.InnerSingleton, Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FLaneGenerationParams.InnerSingleton;
}
// ********** End ScriptStruct FLaneGenerationParams ***********************************************

// ********** Begin ScriptStruct FObjectiveData ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FObjectiveData;
class UScriptStruct* FObjectiveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FObjectiveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FObjectiveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FObjectiveData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ObjectiveData"));
	}
	return Z_Registration_Info_UScriptStruct_FObjectiveData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FObjectiveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Objective" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "Objective" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamOwnership_MetaData[] = {
		{ "Category", "Objective" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Objective" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Objective" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Objective" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TeamOwnership_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TeamOwnership;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FObjectiveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectiveData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectiveData, Type), Z_Construct_UEnum_Aura_EObjectiveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) }; // 1386772319
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_TeamOwnership_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_TeamOwnership = { "TeamOwnership", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectiveData, TeamOwnership), Z_Construct_UEnum_Aura_ETeam, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamOwnership_MetaData), NewProp_TeamOwnership_MetaData) }; // **********
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectiveData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectiveData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
void Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FObjectiveData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FObjectiveData), &Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FObjectiveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_TeamOwnership_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_TeamOwnership,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectiveData_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectiveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FObjectiveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"ObjectiveData",
	Z_Construct_UScriptStruct_FObjectiveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectiveData_Statics::PropPointers),
	sizeof(FObjectiveData),
	alignof(FObjectiveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectiveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FObjectiveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FObjectiveData()
{
	if (!Z_Registration_Info_UScriptStruct_FObjectiveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FObjectiveData.InnerSingleton, Z_Construct_UScriptStruct_FObjectiveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FObjectiveData.InnerSingleton;
}
// ********** End ScriptStruct FObjectiveData ******************************************************

// ********** Begin ScriptStruct FWallData *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FWallData;
class UScriptStruct* FWallData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FWallData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FWallData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWallData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("WallData"));
	}
	return Z_Registration_Info_UScriptStruct_FWallData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FWallData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Wall" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dimensions_MetaData[] = {
		{ "Category", "Wall" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Wall" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Wall" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDestructible_MetaData[] = {
		{ "Category", "Wall" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialType_MetaData[] = {
		{ "Category", "Wall" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Dimensions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static void NewProp_bIsDestructible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDestructible;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MaterialType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MaterialType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWallData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWallData_Statics::NewProp_Dimensions = { "Dimensions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallData, Dimensions), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dimensions_MetaData), NewProp_Dimensions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWallData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
void Z_Construct_UScriptStruct_FWallData_Statics::NewProp_bIsDestructible_SetBit(void* Obj)
{
	((FWallData*)Obj)->bIsDestructible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWallData_Statics::NewProp_bIsDestructible = { "bIsDestructible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWallData), &Z_Construct_UScriptStruct_FWallData_Statics::NewProp_bIsDestructible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDestructible_MetaData), NewProp_bIsDestructible_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWallData_Statics::NewProp_MaterialType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWallData_Statics::NewProp_MaterialType = { "MaterialType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWallData, MaterialType), Z_Construct_UEnum_Aura_EWallMaterial, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialType_MetaData), NewProp_MaterialType_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWallData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallData_Statics::NewProp_Dimensions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallData_Statics::NewProp_bIsDestructible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallData_Statics::NewProp_MaterialType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWallData_Statics::NewProp_MaterialType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWallData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"WallData",
	Z_Construct_UScriptStruct_FWallData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallData_Statics::PropPointers),
	sizeof(FWallData),
	alignof(FWallData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWallData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWallData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWallData()
{
	if (!Z_Registration_Info_UScriptStruct_FWallData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FWallData.InnerSingleton, Z_Construct_UScriptStruct_FWallData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FWallData.InnerSingleton;
}
// ********** End ScriptStruct FWallData ***********************************************************

// ********** Begin ScriptStruct FPropData *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPropData;
class UScriptStruct* FPropData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPropData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPropData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPropData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PropData"));
	}
	return Z_Registration_Info_UScriptStruct_FPropData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPropData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Prop" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "Prop" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Prop" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Prop" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDestructible_MetaData[] = {
		{ "Category", "Prop" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Prop" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static void NewProp_bIsDestructible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDestructible;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPropData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPropData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPropData, Type), Z_Construct_UEnum_Aura_EPropType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) }; // 2974269535
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPropData, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPropData, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
void Z_Construct_UScriptStruct_FPropData_Statics::NewProp_bIsDestructible_SetBit(void* Obj)
{
	((FPropData*)Obj)->bIsDestructible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPropData_Statics::NewProp_bIsDestructible = { "bIsDestructible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPropData), &Z_Construct_UScriptStruct_FPropData_Statics::NewProp_bIsDestructible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDestructible_MetaData), NewProp_bIsDestructible_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPropData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPropData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPropData_Statics::NewProp_bIsDestructible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPropData_Statics::NewProp_Health,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPropData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPropData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PropData",
	Z_Construct_UScriptStruct_FPropData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPropData_Statics::PropPointers),
	sizeof(FPropData),
	alignof(FPropData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPropData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPropData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPropData()
{
	if (!Z_Registration_Info_UScriptStruct_FPropData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPropData.InnerSingleton, Z_Construct_UScriptStruct_FPropData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPropData.InnerSingleton;
}
// ********** End ScriptStruct FPropData ***********************************************************

// ********** Begin Delegate FOnPCGGenerationCompleted *********************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== DELEGATES =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== DELEGATES =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGGenerationCompleted__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationCompleted)
{
	OnPCGGenerationCompleted.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnPCGGenerationCompleted ***********************************************

// ********** Begin Delegate FOnPCGGenerationFailed ************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPCGGenerationFailed_Parms
	{
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGGenerationFailed_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGGenerationFailed__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::_Script_Aura_eventOnPCGGenerationFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::_Script_Aura_eventOnPCGGenerationFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGGenerationFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationFailed, const FString& ErrorMessage)
{
	struct _Script_Aura_eventOnPCGGenerationFailed_Parms
	{
		FString ErrorMessage;
	};
	_Script_Aura_eventOnPCGGenerationFailed_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	OnPCGGenerationFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGGenerationFailed **************************************************

// ********** Begin Delegate FOnPCGAssetGenerated **************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPCGAssetGenerated_Parms
	{
		FString AssetName;
		int32 Count;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_AssetName = { "AssetName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGAssetGenerated_Parms, AssetName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetName_MetaData), NewProp_AssetName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPCGAssetGenerated_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_AssetName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::NewProp_Count,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPCGAssetGenerated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::_Script_Aura_eventOnPCGAssetGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::_Script_Aura_eventOnPCGAssetGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPCGAssetGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnPCGAssetGenerated, const FString& AssetName, int32 Count)
{
	struct _Script_Aura_eventOnPCGAssetGenerated_Parms
	{
		FString AssetName;
		int32 Count;
	};
	_Script_Aura_eventOnPCGAssetGenerated_Parms Parms;
	Parms.AssetName=AssetName;
	Parms.Count=Count;
	OnPCGAssetGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPCGAssetGenerated ****************************************************

// ********** Begin Delegate FOnObjectiveConfigured ************************************************
struct Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnObjectiveConfigured_Parms
	{
		FObjectiveData ObjectiveData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates espec\xc3\xad""ficos para configura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates espec\xc3\xad""ficos para configura\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::NewProp_ObjectiveData = { "ObjectiveData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnObjectiveConfigured_Parms, ObjectiveData), Z_Construct_UScriptStruct_FObjectiveData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveData_MetaData), NewProp_ObjectiveData_MetaData) }; // 3478995327
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::NewProp_ObjectiveData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnObjectiveConfigured__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::_Script_Aura_eventOnObjectiveConfigured_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::_Script_Aura_eventOnObjectiveConfigured_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveConfigured, FObjectiveData const& ObjectiveData)
{
	struct _Script_Aura_eventOnObjectiveConfigured_Parms
	{
		FObjectiveData ObjectiveData;
	};
	_Script_Aura_eventOnObjectiveConfigured_Parms Parms;
	Parms.ObjectiveData=ObjectiveData;
	OnObjectiveConfigured.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveConfigured **************************************************

// ********** Begin Delegate FOnWallConfigured *****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnWallConfigured_Parms
	{
		FWallData WallData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WallData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::NewProp_WallData = { "WallData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnWallConfigured_Parms, WallData), Z_Construct_UScriptStruct_FWallData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallData_MetaData), NewProp_WallData_MetaData) }; // 1589948453
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::NewProp_WallData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnWallConfigured__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::_Script_Aura_eventOnWallConfigured_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::_Script_Aura_eventOnWallConfigured_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWallConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnWallConfigured, FWallData const& WallData)
{
	struct _Script_Aura_eventOnWallConfigured_Parms
	{
		FWallData WallData;
	};
	_Script_Aura_eventOnWallConfigured_Parms Parms;
	Parms.WallData=WallData;
	OnWallConfigured.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnWallConfigured *******************************************************

// ********** Begin Delegate FOnBridgeConfigured ***************************************************
struct Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnBridgeConfigured_Parms
	{
		FBridgeData BridgeData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BridgeData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BridgeData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::NewProp_BridgeData = { "BridgeData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnBridgeConfigured_Parms, BridgeData), Z_Construct_UScriptStruct_FBridgeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BridgeData_MetaData), NewProp_BridgeData_MetaData) }; // 2297366356
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::NewProp_BridgeData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnBridgeConfigured__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::_Script_Aura_eventOnBridgeConfigured_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::_Script_Aura_eventOnBridgeConfigured_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBridgeConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnBridgeConfigured, FBridgeData const& BridgeData)
{
	struct _Script_Aura_eventOnBridgeConfigured_Parms
	{
		FBridgeData BridgeData;
	};
	_Script_Aura_eventOnBridgeConfigured_Parms Parms;
	Parms.BridgeData=BridgeData;
	OnBridgeConfigured.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBridgeConfigured *****************************************************

// ********** Begin Delegate FOnPropConfigured *****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPropConfigured_Parms
	{
		FPropData PropData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PropData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PropData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::NewProp_PropData = { "PropData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPropConfigured_Parms, PropData), Z_Construct_UScriptStruct_FPropData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PropData_MetaData), NewProp_PropData_MetaData) }; // 1803884502
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::NewProp_PropData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPropConfigured__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::_Script_Aura_eventOnPropConfigured_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::_Script_Aura_eventOnPropConfigured_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPropConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnPropConfigured, FPropData const& PropData)
{
	struct _Script_Aura_eventOnPropConfigured_Parms
	{
		FPropData PropData;
	};
	_Script_Aura_eventOnPropConfigured_Parms Parms;
	Parms.PropData=PropData;
	OnPropConfigured.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPropConfigured *******************************************************

// ********** Begin Delegate FOnPhaseChanged *******************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPhaseChanged_Parms
	{
		EPCGGenerationPhase OldPhase;
		EPCGGenerationPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldPhase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase = { "OldPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhaseChanged_Parms, OldPhase), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 2886374832
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 2886374832
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_OldPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPhaseChanged__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::_Script_Aura_eventOnPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::_Script_Aura_eventOnPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPhaseChanged, EPCGGenerationPhase OldPhase, EPCGGenerationPhase NewPhase)
{
	struct _Script_Aura_eventOnPhaseChanged_Parms
	{
		EPCGGenerationPhase OldPhase;
		EPCGGenerationPhase NewPhase;
	};
	_Script_Aura_eventOnPhaseChanged_Parms Parms;
	Parms.OldPhase=OldPhase;
	Parms.NewPhase=NewPhase;
	OnPhaseChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPhaseChanged *********************************************************

// ********** Begin Delegate FOnProgressUpdated ****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnProgressUpdated_Parms
	{
		FString PhaseName;
		float Progress;
		float EstimatedTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PhaseName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::NewProp_PhaseName = { "PhaseName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnProgressUpdated_Parms, PhaseName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseName_MetaData), NewProp_PhaseName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnProgressUpdated_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::NewProp_EstimatedTime = { "EstimatedTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnProgressUpdated_Parms, EstimatedTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::NewProp_PhaseName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::NewProp_EstimatedTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnProgressUpdated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::_Script_Aura_eventOnProgressUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::_Script_Aura_eventOnProgressUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnProgressUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnProgressUpdated, const FString& PhaseName, float Progress, float EstimatedTime)
{
	struct _Script_Aura_eventOnProgressUpdated_Parms
	{
		FString PhaseName;
		float Progress;
		float EstimatedTime;
	};
	_Script_Aura_eventOnProgressUpdated_Parms Parms;
	Parms.PhaseName=PhaseName;
	Parms.Progress=Progress;
	Parms.EstimatedTime=EstimatedTime;
	OnProgressUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnProgressUpdated ******************************************************

// ********** Begin Class AProceduralMapGenerator Function AddSpawnRule ****************************
struct Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics
{
	struct ProceduralMapGenerator_eventAddSpawnRule_Parms
	{
		FPCGSpawnRule NewRule;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewRule_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewRule;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::NewProp_NewRule = { "NewRule", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventAddSpawnRule_Parms, NewRule), Z_Construct_UScriptStruct_FPCGSpawnRule, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewRule_MetaData), NewProp_NewRule_MetaData) }; // 931756877
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::NewProp_NewRule,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "AddSpawnRule", Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::ProceduralMapGenerator_eventAddSpawnRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::ProceduralMapGenerator_eventAddSpawnRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execAddSpawnRule)
{
	P_GET_STRUCT_REF(FPCGSpawnRule,Z_Param_Out_NewRule);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddSpawnRule(Z_Param_Out_NewRule);
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function AddSpawnRule ******************************

// ********** Begin Class AProceduralMapGenerator Function ClearGeneration *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ClearGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execClearGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ClearGeneration ***************************

// ********** Begin Class AProceduralMapGenerator Function ClearSpawnRules *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ClearSpawnRules", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execClearSpawnRules)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearSpawnRules();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ClearSpawnRules ***************************

// ********** Begin Class AProceduralMapGenerator Function GetCurrentPhase *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics
{
	struct ProceduralMapGenerator_eventGetCurrentPhase_Parms
	{
		EPCGGenerationPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetCurrentPhase_Parms, ReturnValue), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 2886374832
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetCurrentPhase", Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::ProceduralMapGenerator_eventGetCurrentPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::ProceduralMapGenerator_eventGetCurrentPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetCurrentPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EPCGGenerationPhase*)Z_Param__Result=P_THIS->GetCurrentPhase();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetCurrentPhase ***************************

// ********** Begin Class AProceduralMapGenerator Function GetGeneratedAssetCount ******************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics
{
	struct ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetGeneratedAssetCount", Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::ProceduralMapGenerator_eventGetGeneratedAssetCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetGeneratedAssetCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetGeneratedAssetCount();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetGeneratedAssetCount ********************

// ********** Begin Class AProceduralMapGenerator Function GetGenerationConfig *********************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics
{
	struct ProceduralMapGenerator_eventGetGenerationConfig_Parms
	{
		FPCGGenerationConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetGenerationConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGGenerationConfig, METADATA_PARAMS(0, nullptr) }; // 702549597
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetGenerationConfig", Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::ProceduralMapGenerator_eventGetGenerationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::ProceduralMapGenerator_eventGetGenerationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetGenerationConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGGenerationConfig*)Z_Param__Result=P_THIS->GetGenerationConfig();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetGenerationConfig ***********************

// ********** Begin Class AProceduralMapGenerator Function GetGenerationProgress *******************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics
{
	struct ProceduralMapGenerator_eventGetGenerationProgress_Parms
	{
		FPCGGenerationProgress ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES DE ESTADO =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES DE ESTADO =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetGenerationProgress_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGGenerationProgress, METADATA_PARAMS(0, nullptr) }; // 3844196119
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetGenerationProgress", Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::ProceduralMapGenerator_eventGetGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::ProceduralMapGenerator_eventGetGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetGenerationProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGGenerationProgress*)Z_Param__Result=P_THIS->GetGenerationProgress();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetGenerationProgress *********************

// ********** Begin Class AProceduralMapGenerator Function GetOverallProgress **********************
struct Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics
{
	struct ProceduralMapGenerator_eventGetOverallProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventGetOverallProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "GetOverallProgress", Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::ProceduralMapGenerator_eventGetOverallProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::ProceduralMapGenerator_eventGetOverallProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execGetOverallProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetOverallProgress();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function GetOverallProgress ************************

// ********** Begin Class AProceduralMapGenerator Function InitializeManagers **********************
struct Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES DE INTEGRA\xc3\x87\xc3\x83O =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES DE INTEGRA\xc3\x87\xc3\x83O =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "InitializeManagers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execInitializeManagers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeManagers();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function InitializeManagers ************************

// ********** Begin Class AProceduralMapGenerator Function IsGenerating ****************************
struct Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics
{
	struct ProceduralMapGenerator_eventIsGenerating_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralMapGenerator_eventIsGenerating_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralMapGenerator_eventIsGenerating_Parms), &Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "IsGenerating", Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::ProceduralMapGenerator_eventIsGenerating_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::ProceduralMapGenerator_eventIsGenerating_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execIsGenerating)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGenerating();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function IsGenerating ******************************

// ********** Begin Class AProceduralMapGenerator Function IsPaused ********************************
struct Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics
{
	struct ProceduralMapGenerator_eventIsPaused_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralMapGenerator_eventIsPaused_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralMapGenerator_eventIsPaused_Parms), &Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "IsPaused", Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::ProceduralMapGenerator_eventIsPaused_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::ProceduralMapGenerator_eventIsPaused_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_IsPaused()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_IsPaused_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execIsPaused)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPaused();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function IsPaused **********************************

// ********** Begin Class AProceduralMapGenerator Function PauseGeneration *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "PauseGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execPauseGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function PauseGeneration ***************************

// ********** Begin Class AProceduralMapGenerator Function RemoveSpawnRule *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics
{
	struct ProceduralMapGenerator_eventRemoveSpawnRule_Parms
	{
		FString RuleName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuleName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::NewProp_RuleName = { "RuleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventRemoveSpawnRule_Parms, RuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleName_MetaData), NewProp_RuleName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::NewProp_RuleName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "RemoveSpawnRule", Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::ProceduralMapGenerator_eventRemoveSpawnRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::ProceduralMapGenerator_eventRemoveSpawnRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execRemoveSpawnRule)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RuleName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveSpawnRule(Z_Param_RuleName);
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function RemoveSpawnRule ***************************

// ********** Begin Class AProceduralMapGenerator Function RestartGeneration ***********************
struct Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "RestartGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execRestartGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RestartGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function RestartGeneration *************************

// ********** Begin Class AProceduralMapGenerator Function ResumeGeneration ************************
struct Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ResumeGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execResumeGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ResumeGeneration **************************

// ********** Begin Class AProceduralMapGenerator Function SetGenerationConfig *********************
struct Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics
{
	struct ProceduralMapGenerator_eventSetGenerationConfig_Parms
	{
		FPCGGenerationConfig NewConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES DE CONFIGURA\xc3\x87\xc3\x83O =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES DE CONFIGURA\xc3\x87\xc3\x83O =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralMapGenerator_eventSetGenerationConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FPCGGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 702549597
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::NewProp_NewConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "SetGenerationConfig", Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::ProceduralMapGenerator_eventSetGenerationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::ProceduralMapGenerator_eventSetGenerationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execSetGenerationConfig)
{
	P_GET_STRUCT_REF(FPCGGenerationConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGenerationConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function SetGenerationConfig ***********************

// ********** Begin Class AProceduralMapGenerator Function StartGeneration *************************
struct Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== FUN\xc3\x87\xc3\x95""ES PRINCIPAIS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== FUN\xc3\x87\xc3\x95""ES PRINCIPAIS =====" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "StartGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execStartGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function StartGeneration ***************************

// ********** Begin Class AProceduralMapGenerator Function StopGeneration **************************
struct Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "StopGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execStopGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopGeneration();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function StopGeneration ****************************

// ********** Begin Class AProceduralMapGenerator Function SynchronizeWithMapManager ***************
struct Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "SynchronizeWithMapManager", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execSynchronizeWithMapManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithMapManager();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function SynchronizeWithMapManager *****************

// ********** Begin Class AProceduralMapGenerator Function UpdatePCGFromGeometry *******************
struct Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "UpdatePCGFromGeometry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execUpdatePCGFromGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePCGFromGeometry();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function UpdatePCGFromGeometry *********************

// ********** Begin Class AProceduralMapGenerator Function ValidateManagerReferences ***************
struct Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Integration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AProceduralMapGenerator, nullptr, "ValidateManagerReferences", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralMapGenerator::execValidateManagerReferences)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateManagerReferences();
	P_NATIVE_END;
}
// ********** End Class AProceduralMapGenerator Function ValidateManagerReferences *****************

// ********** Begin Class AProceduralMapGenerator **************************************************
void AProceduralMapGenerator::StaticRegisterNativesAProceduralMapGenerator()
{
	UClass* Class = AProceduralMapGenerator::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddSpawnRule", &AProceduralMapGenerator::execAddSpawnRule },
		{ "ClearGeneration", &AProceduralMapGenerator::execClearGeneration },
		{ "ClearSpawnRules", &AProceduralMapGenerator::execClearSpawnRules },
		{ "GetCurrentPhase", &AProceduralMapGenerator::execGetCurrentPhase },
		{ "GetGeneratedAssetCount", &AProceduralMapGenerator::execGetGeneratedAssetCount },
		{ "GetGenerationConfig", &AProceduralMapGenerator::execGetGenerationConfig },
		{ "GetGenerationProgress", &AProceduralMapGenerator::execGetGenerationProgress },
		{ "GetOverallProgress", &AProceduralMapGenerator::execGetOverallProgress },
		{ "InitializeManagers", &AProceduralMapGenerator::execInitializeManagers },
		{ "IsGenerating", &AProceduralMapGenerator::execIsGenerating },
		{ "IsPaused", &AProceduralMapGenerator::execIsPaused },
		{ "PauseGeneration", &AProceduralMapGenerator::execPauseGeneration },
		{ "RemoveSpawnRule", &AProceduralMapGenerator::execRemoveSpawnRule },
		{ "RestartGeneration", &AProceduralMapGenerator::execRestartGeneration },
		{ "ResumeGeneration", &AProceduralMapGenerator::execResumeGeneration },
		{ "SetGenerationConfig", &AProceduralMapGenerator::execSetGenerationConfig },
		{ "StartGeneration", &AProceduralMapGenerator::execStartGeneration },
		{ "StopGeneration", &AProceduralMapGenerator::execStopGeneration },
		{ "SynchronizeWithMapManager", &AProceduralMapGenerator::execSynchronizeWithMapManager },
		{ "UpdatePCGFromGeometry", &AProceduralMapGenerator::execUpdatePCGFromGeometry },
		{ "ValidateManagerReferences", &AProceduralMapGenerator::execValidateManagerReferences },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AProceduralMapGenerator;
UClass* AProceduralMapGenerator::GetPrivateStaticClass()
{
	using TClass = AProceduralMapGenerator;
	if (!Z_Registration_Info_UClass_AProceduralMapGenerator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("ProceduralMapGenerator"),
			Z_Registration_Info_UClass_AProceduralMapGenerator.InnerSingleton,
			StaticRegisterNativesAProceduralMapGenerator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AProceduralMapGenerator.InnerSingleton;
}
UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister()
{
	return AProceduralMapGenerator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AProceduralMapGenerator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== CLASSE PRINCIPAL =====\n" },
#endif
		{ "IncludePath", "AProceduralMapGenerator.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== CLASSE PRINCIPAL =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== COMPONENTES =====\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== COMPONENTES =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoundsVisualization_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationConfig_MetaData[] = {
		{ "Category", "PCG Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== CONFIGURA\xc3\x87\xc3\x83O =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== CONFIGURA\xc3\x87\xc3\x83O =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnRules_MetaData[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraphAsset_MetaData[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapBounds_MetaData[] = {
		{ "Category", "PCG Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapCenter_MetaData[] = {
		{ "Category", "PCG Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 50km x 50km x 2km\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "50km x 50km x 2km" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneManager_MetaData[] = {
		{ "Category", "Managers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== REFER\xc3\x8aNCIAS DOS MANAGERS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== REFER\xc3\x8aNCIAS DOS MANAGERS =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaronManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometricValidator_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationProgress_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== ESTADO =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== ESTADO =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerating_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPaused_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedActors_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGenerationCompleted_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== DELEGATES =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== DELEGATES =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGenerationFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetGenerated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentProgress_MetaData[] = {
		{ "Category", "Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== VARI\xc3\x81VEIS DE PROGRESSO =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== VARI\xc3\x81VEIS DE PROGRESSO =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedTimeRemaining_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationStartTime_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerationComplete_MetaData[] = {
		{ "Category", "Generation State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugLines_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "Generation State" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainMeshes_MetaData[] = {
		{ "Category", "Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== ASSETS E MATERIAIS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== ASSETS E MATERIAIS =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationMeshes_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PropMeshes_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainMaterial_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterMaterial_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnDensity_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== CONFIGURA\xc3\x87\xc3\x95""ES =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== CONFIGURA\xc3\x87\xc3\x95""ES =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainConfig_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationConfig_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExclusionZones_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveManager_MetaData[] = {
		{ "Category", "Managers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== MANAGERS ADICIONAIS =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== MANAGERS ADICIONAIS =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveConfigured_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ===== DELEGATES =====\n" },
#endif
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "===== DELEGATES =====" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWallConfigured_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBridgeConfigured_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPropConfigured_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPhaseChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnProgressUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AProceduralMapGenerator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BoundsVisualization;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnRules_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnRules;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PCGGraphAsset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapBounds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapCenter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaronManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragonManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WallManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RiverManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MinionManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MapManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeometricValidator;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationProgress;
	static void NewProp_bIsGenerating_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerating;
	static void NewProp_bIsPaused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPaused;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedActors;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGenerationCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGenerationFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetGenerated;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedTimeRemaining;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationStartTime;
	static void NewProp_bIsGenerationComplete_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerationComplete;
	static void NewProp_bShowDebugLines_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugLines;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TerrainMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TerrainMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VegetationMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VegetationMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PropMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PropMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TerrainMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WaterMaterial;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnDensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TerrainConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VegetationConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExclusionZones_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExclusionZones;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveManager;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveConfigured;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWallConfigured;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBridgeConfigured;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPropConfigured;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPhaseChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnProgressUpdated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AProceduralMapGenerator_AddSpawnRule, "AddSpawnRule" }, // 1944940994
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ClearGeneration, "ClearGeneration" }, // 2552607104
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ClearSpawnRules, "ClearSpawnRules" }, // 3121219462
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetCurrentPhase, "GetCurrentPhase" }, // 1992987361
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetGeneratedAssetCount, "GetGeneratedAssetCount" }, // 476861668
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationConfig, "GetGenerationConfig" }, // 979441392
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetGenerationProgress, "GetGenerationProgress" }, // 491872281
		{ &Z_Construct_UFunction_AProceduralMapGenerator_GetOverallProgress, "GetOverallProgress" }, // 144586471
		{ &Z_Construct_UFunction_AProceduralMapGenerator_InitializeManagers, "InitializeManagers" }, // 280277284
		{ &Z_Construct_UFunction_AProceduralMapGenerator_IsGenerating, "IsGenerating" }, // 4122472116
		{ &Z_Construct_UFunction_AProceduralMapGenerator_IsPaused, "IsPaused" }, // 1963397404
		{ &Z_Construct_UFunction_AProceduralMapGenerator_PauseGeneration, "PauseGeneration" }, // 3575664076
		{ &Z_Construct_UFunction_AProceduralMapGenerator_RemoveSpawnRule, "RemoveSpawnRule" }, // 6684046
		{ &Z_Construct_UFunction_AProceduralMapGenerator_RestartGeneration, "RestartGeneration" }, // 433070153
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ResumeGeneration, "ResumeGeneration" }, // 2299003040
		{ &Z_Construct_UFunction_AProceduralMapGenerator_SetGenerationConfig, "SetGenerationConfig" }, // 60156516
		{ &Z_Construct_UFunction_AProceduralMapGenerator_StartGeneration, "StartGeneration" }, // 1690989740
		{ &Z_Construct_UFunction_AProceduralMapGenerator_StopGeneration, "StopGeneration" }, // 3329786590
		{ &Z_Construct_UFunction_AProceduralMapGenerator_SynchronizeWithMapManager, "SynchronizeWithMapManager" }, // 3622304507
		{ &Z_Construct_UFunction_AProceduralMapGenerator_UpdatePCGFromGeometry, "UpdatePCGFromGeometry" }, // 2772828719
		{ &Z_Construct_UFunction_AProceduralMapGenerator_ValidateManagerReferences, "ValidateManagerReferences" }, // 1424320463
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AProceduralMapGenerator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BoundsVisualization = { "BoundsVisualization", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, BoundsVisualization), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoundsVisualization_MetaData), NewProp_BoundsVisualization_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationConfig = { "GenerationConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GenerationConfig), Z_Construct_UScriptStruct_FPCGGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationConfig_MetaData), NewProp_GenerationConfig_MetaData) }; // 702549597
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules_Inner = { "SpawnRules", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGSpawnRule, METADATA_PARAMS(0, nullptr) }; // 931756877
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules = { "SpawnRules", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, SpawnRules), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnRules_MetaData), NewProp_SpawnRules_MetaData) }; // 931756877
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGGraphAsset = { "PCGGraphAsset", nullptr, (EPropertyFlags)0x0024080000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, PCGGraphAsset), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraphAsset_MetaData), NewProp_PCGGraphAsset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapBounds = { "MapBounds", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MapBounds), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapBounds_MetaData), NewProp_MapBounds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapCenter = { "MapCenter", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MapCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapCenter_MetaData), NewProp_MapCenter_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_LaneManager = { "LaneManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, LaneManager), Z_Construct_UClass_ALaneManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneManager_MetaData), NewProp_LaneManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BaronManager = { "BaronManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, BaronManager), Z_Construct_UClass_ABaronAuracronManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaronManager_MetaData), NewProp_BaronManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_DragonManager = { "DragonManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, DragonManager), Z_Construct_UClass_ADragonPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonManager_MetaData), NewProp_DragonManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_WallManager = { "WallManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, WallManager), Z_Construct_UClass_AWallCollisionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallManager_MetaData), NewProp_WallManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RiverManager = { "RiverManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, RiverManager), Z_Construct_UClass_ARiverPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverManager_MetaData), NewProp_RiverManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MinionManager = { "MinionManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MinionManager), Z_Construct_UClass_AMinionWaveManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionManager_MetaData), NewProp_MinionManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapManager = { "MapManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, MapManager), Z_Construct_UClass_AMapManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapManager_MetaData), NewProp_MapManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeometricValidator = { "GeometricValidator", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GeometricValidator), Z_Construct_UClass_AGeometricValidator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometricValidator_MetaData), NewProp_GeometricValidator_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationProgress = { "GenerationProgress", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GenerationProgress), Z_Construct_UScriptStruct_FPCGGenerationProgress, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationProgress_MetaData), NewProp_GenerationProgress_MetaData) }; // 3844196119
void Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating_SetBit(void* Obj)
{
	((AProceduralMapGenerator*)Obj)->bIsGenerating = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating = { "bIsGenerating", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AProceduralMapGenerator), &Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerating_MetaData), NewProp_bIsGenerating_MetaData) };
void Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused_SetBit(void* Obj)
{
	((AProceduralMapGenerator*)Obj)->bIsPaused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused = { "bIsPaused", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AProceduralMapGenerator), &Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPaused_MetaData), NewProp_bIsPaused_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors_Inner = { "GeneratedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors = { "GeneratedActors", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GeneratedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedActors_MetaData), NewProp_GeneratedActors_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationCompleted = { "OnGenerationCompleted", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnGenerationCompleted), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGenerationCompleted_MetaData), NewProp_OnGenerationCompleted_MetaData) }; // 1857565218
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationFailed = { "OnGenerationFailed", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnGenerationFailed), Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGenerationFailed_MetaData), NewProp_OnGenerationFailed_MetaData) }; // 4128819394
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnAssetGenerated = { "OnAssetGenerated", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnAssetGenerated), Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetGenerated_MetaData), NewProp_OnAssetGenerated_MetaData) }; // 3017971507
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_CurrentProgress = { "CurrentProgress", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, CurrentProgress), Z_Construct_UScriptStruct_FGenerationProgress, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentProgress_MetaData), NewProp_CurrentProgress_MetaData) }; // 1879085191
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TotalProgress = { "TotalProgress", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, TotalProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalProgress_MetaData), NewProp_TotalProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_EstimatedTimeRemaining = { "EstimatedTimeRemaining", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, EstimatedTimeRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedTimeRemaining_MetaData), NewProp_EstimatedTimeRemaining_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationStartTime = { "GenerationStartTime", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, GenerationStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationStartTime_MetaData), NewProp_GenerationStartTime_MetaData) };
void Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerationComplete_SetBit(void* Obj)
{
	((AProceduralMapGenerator*)Obj)->bIsGenerationComplete = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerationComplete = { "bIsGenerationComplete", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AProceduralMapGenerator), &Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerationComplete_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerationComplete_MetaData), NewProp_bIsGenerationComplete_MetaData) };
void Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bShowDebugLines_SetBit(void* Obj)
{
	((AProceduralMapGenerator*)Obj)->bShowDebugLines = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bShowDebugLines = { "bShowDebugLines", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AProceduralMapGenerator), &Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bShowDebugLines_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugLines_MetaData), NewProp_bShowDebugLines_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, CurrentPhase), Z_Construct_UEnum_Aura_EPCGGenerationPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 2886374832
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainMeshes_Inner = { "TerrainMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainMeshes = { "TerrainMeshes", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, TerrainMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainMeshes_MetaData), NewProp_TerrainMeshes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_VegetationMeshes_Inner = { "VegetationMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_VegetationMeshes = { "VegetationMeshes", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, VegetationMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationMeshes_MetaData), NewProp_VegetationMeshes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PropMeshes_Inner = { "PropMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PropMeshes = { "PropMeshes", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, PropMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PropMeshes_MetaData), NewProp_PropMeshes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainMaterial = { "TerrainMaterial", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, TerrainMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainMaterial_MetaData), NewProp_TerrainMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_WaterMaterial = { "WaterMaterial", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, WaterMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterMaterial_MetaData), NewProp_WaterMaterial_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnDensity = { "SpawnDensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, SpawnDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnDensity_MetaData), NewProp_SpawnDensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainConfig = { "TerrainConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, TerrainConfig), Z_Construct_UScriptStruct_FTerrainGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainConfig_MetaData), NewProp_TerrainConfig_MetaData) }; // 1622077273
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_VegetationConfig = { "VegetationConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, VegetationConfig), Z_Construct_UScriptStruct_FVegetationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationConfig_MetaData), NewProp_VegetationConfig_MetaData) }; // 4224588085
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_ExclusionZones_Inner = { "ExclusionZones", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FExclusionZone, METADATA_PARAMS(0, nullptr) }; // 4133118692
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_ExclusionZones = { "ExclusionZones", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, ExclusionZones), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExclusionZones_MetaData), NewProp_ExclusionZones_MetaData) }; // 4133118692
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_ObjectiveManager = { "ObjectiveManager", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, ObjectiveManager), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveManager_MetaData), NewProp_ObjectiveManager_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnObjectiveConfigured = { "OnObjectiveConfigured", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnObjectiveConfigured), Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveConfigured_MetaData), NewProp_OnObjectiveConfigured_MetaData) }; // 2370023284
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnWallConfigured = { "OnWallConfigured", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnWallConfigured), Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWallConfigured_MetaData), NewProp_OnWallConfigured_MetaData) }; // 3522730955
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnBridgeConfigured = { "OnBridgeConfigured", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnBridgeConfigured), Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBridgeConfigured_MetaData), NewProp_OnBridgeConfigured_MetaData) }; // 44594843
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnPropConfigured = { "OnPropConfigured", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnPropConfigured), Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPropConfigured_MetaData), NewProp_OnPropConfigured_MetaData) }; // 3152099387
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnPhaseChanged = { "OnPhaseChanged", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnPhaseChanged), Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPhaseChanged_MetaData), NewProp_OnPhaseChanged_MetaData) }; // 1806635013
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnProgressUpdated = { "OnProgressUpdated", nullptr, (EPropertyFlags)0x0020080010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralMapGenerator, OnProgressUpdated), Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnProgressUpdated_MetaData), NewProp_OnProgressUpdated_MetaData) }; // 4050072441
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BoundsVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PCGGraphAsset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_LaneManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_BaronManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_DragonManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_WallManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_RiverManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MinionManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_MapManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeometricValidator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsPaused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GeneratedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnGenerationFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnAssetGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_CurrentProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TotalProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_EstimatedTimeRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_GenerationStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bIsGenerationComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_bShowDebugLines,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_VegetationMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_VegetationMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PropMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_PropMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_WaterMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_SpawnDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_TerrainConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_VegetationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_ExclusionZones_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_ExclusionZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_ObjectiveManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnObjectiveConfigured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnWallConfigured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnBridgeConfigured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnPropConfigured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnPhaseChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralMapGenerator_Statics::NewProp_OnProgressUpdated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AProceduralMapGenerator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AProceduralMapGenerator_Statics::ClassParams = {
	&AProceduralMapGenerator::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralMapGenerator_Statics::Class_MetaDataParams), Z_Construct_UClass_AProceduralMapGenerator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AProceduralMapGenerator()
{
	if (!Z_Registration_Info_UClass_AProceduralMapGenerator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AProceduralMapGenerator.OuterSingleton, Z_Construct_UClass_AProceduralMapGenerator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AProceduralMapGenerator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AProceduralMapGenerator);
AProceduralMapGenerator::~AProceduralMapGenerator() {}
// ********** End Class AProceduralMapGenerator ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGGenerationPhase_StaticEnum, TEXT("EPCGGenerationPhase"), &Z_Registration_Info_UEnum_EPCGGenerationPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2886374832U) },
		{ EObjectiveType_StaticEnum, TEXT("EObjectiveType"), &Z_Registration_Info_UEnum_EObjectiveType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1386772319U) },
		{ EPropType_StaticEnum, TEXT("EPropType"), &Z_Registration_Info_UEnum_EPropType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2974269535U) },
		{ EPCGMapAssetType_StaticEnum, TEXT("EPCGMapAssetType"), &Z_Registration_Info_UEnum_EPCGMapAssetType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2580812996U) },
		{ EPCGDistributionType_StaticEnum, TEXT("EPCGDistributionType"), &Z_Registration_Info_UEnum_EPCGDistributionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 103216633U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FGenerationProgress::StaticStruct, Z_Construct_UScriptStruct_FGenerationProgress_Statics::NewStructOps, TEXT("GenerationProgress"), &Z_Registration_Info_UScriptStruct_FGenerationProgress, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FGenerationProgress), 1879085191U) },
		{ FExclusionZone::StaticStruct, Z_Construct_UScriptStruct_FExclusionZone_Statics::NewStructOps, TEXT("ExclusionZone"), &Z_Registration_Info_UScriptStruct_FExclusionZone, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FExclusionZone), 4133118692U) },
		{ FTerrainGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics::NewStructOps, TEXT("TerrainGenerationConfig"), &Z_Registration_Info_UScriptStruct_FTerrainGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTerrainGenerationConfig), 1622077273U) },
		{ FVegetationConfig::StaticStruct, Z_Construct_UScriptStruct_FVegetationConfig_Statics::NewStructOps, TEXT("VegetationConfig"), &Z_Registration_Info_UScriptStruct_FVegetationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FVegetationConfig), 4224588085U) },
		{ FPCGAssetReference::StaticStruct, Z_Construct_UScriptStruct_FPCGAssetReference_Statics::NewStructOps, TEXT("PCGAssetReference"), &Z_Registration_Info_UScriptStruct_FPCGAssetReference, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGAssetReference), 3234866593U) },
		{ FPCGSpawnRule::StaticStruct, Z_Construct_UScriptStruct_FPCGSpawnRule_Statics::NewStructOps, TEXT("PCGSpawnRule"), &Z_Registration_Info_UScriptStruct_FPCGSpawnRule, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGSpawnRule), 931756877U) },
		{ FPCGGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics::NewStructOps, TEXT("PCGGenerationConfig"), &Z_Registration_Info_UScriptStruct_FPCGGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGGenerationConfig), 702549597U) },
		{ FPCGGenerationProgress::StaticStruct, Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics::NewStructOps, TEXT("PCGGenerationProgress"), &Z_Registration_Info_UScriptStruct_FPCGGenerationProgress, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGGenerationProgress), 3844196119U) },
		{ FLaneGenerationParams::StaticStruct, Z_Construct_UScriptStruct_FLaneGenerationParams_Statics::NewStructOps, TEXT("LaneGenerationParams"), &Z_Registration_Info_UScriptStruct_FLaneGenerationParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLaneGenerationParams), 1104394811U) },
		{ FObjectiveData::StaticStruct, Z_Construct_UScriptStruct_FObjectiveData_Statics::NewStructOps, TEXT("ObjectiveData"), &Z_Registration_Info_UScriptStruct_FObjectiveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FObjectiveData), 3478995327U) },
		{ FWallData::StaticStruct, Z_Construct_UScriptStruct_FWallData_Statics::NewStructOps, TEXT("WallData"), &Z_Registration_Info_UScriptStruct_FWallData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWallData), 1589948453U) },
		{ FPropData::StaticStruct, Z_Construct_UScriptStruct_FPropData_Statics::NewStructOps, TEXT("PropData"), &Z_Registration_Info_UScriptStruct_FPropData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPropData), 1803884502U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AProceduralMapGenerator, AProceduralMapGenerator::StaticClass, TEXT("AProceduralMapGenerator"), &Z_Registration_Info_UClass_AProceduralMapGenerator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AProceduralMapGenerator), 1365579114U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_1134748413(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
