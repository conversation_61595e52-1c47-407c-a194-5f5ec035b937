#include "AWallCollisionManager.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SceneComponent.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "Math/UnrealMathUtility.h"
#include "TimerManager.h"
#include "Materials/MaterialInterface.h"
#include "UObject/ConstructorHelpers.h"

AWallCollisionManager::AWallCollisionManager()
{
    PrimaryActorTick.bCanEverTick = true;

    // Configurar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Inicializar configurações padrão
    MapBoundarySize = MAP_BOUNDARY_SIZE;
    WallHeight = WALL_HEIGHT;
    WallThickness = WALL_THICKNESS;
    CornerRadius = CORNER_RADIUS;
    WallSegments = WALL_SEGMENTS;
    CollisionTolerance = COLLISION_TOLERANCE;
    bEnableCollisionLogging = true;
    MaxCollisionHistory = 100;

    // Inicializar arrays
    WallSections.Empty();
    Gates.Empty();
    WallMeshes.Empty();
    CollisionBoxes.Empty();
    RecentCollisions.Empty();

    // Configurar material padrão
    MaterialConfig = FWallMaterialConfig();
    MaterialConfig.MaterialType = EWallMaterial::Stone;
    MaterialConfig.Durability = 1000.0f;
    MaterialConfig.ReflectionCoefficient = 0.8f;
    MaterialConfig.bIsDestructible = false;
    MaterialConfig.EmissiveColor = FLinearColor::Black;

    // Configurar área de patrulha padrão
    PatrolArea = FPatrolArea();
    PatrolArea.PatrolRadius = 500.0f;
    PatrolArea.bIsCircular = true;
    PatrolArea.PatrolSpeed = 300.0f;

    // Variáveis internas
    LastUpdateTime = 0.0f;
    bIsInitialized = false;
    ActorDistanceCache.Empty();
}

void AWallCollisionManager::BeginPlay()
{
    Super::BeginPlay();

    // Inicializar sistema de paredes
    InitializeWallSystem();
    
    // Configurar timer de limpeza de colisões
    GetWorld()->GetTimerManager().SetTimer(
        CollisionCleanupTimer,
        this,
        &AWallCollisionManager::CleanupOldCollisions,
        10.0f, // Limpar a cada 10 segundos
        true
    );

    UE_LOG(LogTemp, Warning, TEXT("Wall Collision Manager inicializado com %d seções de parede"), WallSections.Num());
}

void AWallCollisionManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    LastUpdateTime += DeltaTime;
    
    // Atualizar cache de distâncias a cada segundo
    if (FMath::Fmod(LastUpdateTime, 1.0f) < DeltaTime)
    {
        ActorDistanceCache.Empty();
    }
}

// Funções principais de inicialização
void AWallCollisionManager::InitializeWallSystem()
{
    if (bIsInitialized)
    {
        return;
    }

    // Calcular limites das paredes
    CalculateWallBoundaries();
    
    // Gerar geometria das paredes
    GenerateWallGeometry();
    
    // Criar portões
    CreateGates();
    
    // Configurar sistema de colisões
    SetupCollisionSystem();
    
    // Gerar caminho de patrulha
    PatrolArea.PatrolPoints = GeneratePatrolPath();
    
    bIsInitialized = true;
    
    UE_LOG(LogTemp, Warning, TEXT("Sistema de paredes inicializado com sucesso"));
}

void AWallCollisionManager::GenerateWallGeometry()
{
    // Limpar geometria existente
    WallSections.Empty();
    
    // Inicializar seções padrão das paredes
    InitializeDefaultWallSections();
    
    // Criar meshes das paredes
    CreateWallMeshes();
    
    UE_LOG(LogTemp, Warning, TEXT("Geometria das paredes gerada: %d seções"), WallSections.Num());
}

void AWallCollisionManager::SetupCollisionSystem()
{
    // Limpar colisões existentes
    for (UBoxComponent* CollisionBox : CollisionBoxes)
    {
        if (CollisionBox)
        {
            CollisionBox->DestroyComponent();
        }
    }
    CollisionBoxes.Empty();
    
    // Criar caixas de colisão para cada seção de parede
    for (int32 i = 0; i < WallSections.Num(); i++)
    {
        const FWallSection& WallSection = WallSections[i];
        
        UBoxComponent* CollisionBox = CreateDefaultSubobject<UBoxComponent>(*FString::Printf(TEXT("WallCollision_%d"), i));
        CollisionBox->SetupAttachment(RootComponent);
        
        // Calcular tamanho e posição da caixa de colisão
        FVector WallCenter = GetWallCenter(WallSection);
        float WallLength = CalculateWallLength(WallSection);
        
        CollisionBox->SetBoxExtent(FVector(WallLength * 0.5f, WallThickness * 0.5f, WallHeight * 0.5f));
        CollisionBox->SetWorldLocation(WallCenter);
        
        // Calcular rotação baseada na orientação da parede
        FVector WallDirection = (WallSection.EndPosition - WallSection.StartPosition).GetSafeNormal();
        FRotator WallRotation = WallDirection.Rotation();
        CollisionBox->SetWorldRotation(WallRotation);
        
        // Configurar colisão
        CollisionBox->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        CollisionBox->SetCollisionResponseToAllChannels(ECR_Block);
        CollisionBox->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
        CollisionBox->SetCollisionResponseToChannel(ECC_Vehicle, ECR_Block);
        
        CollisionBoxes.Add(CollisionBox);
    }
    
    // Configurar callbacks de colisão
    SetupCollisionCallbacks();
    
    UE_LOG(LogTemp, Warning, TEXT("Sistema de colisões configurado: %d caixas de colisão"), CollisionBoxes.Num());
}

// Funções de geometria das paredes
void AWallCollisionManager::CalculateWallBoundaries()
{
    // As paredes formam um quadrado ao redor do mapa
    float HalfSize = MapBoundarySize * 0.5f;
    
    // Definir os quatro cantos do mapa
    FVector TopLeft(-HalfSize, -HalfSize, 0.0f);
    FVector TopRight(HalfSize, -HalfSize, 0.0f);
    FVector BottomRight(HalfSize, HalfSize, 0.0f);
    FVector BottomLeft(-HalfSize, HalfSize, 0.0f);
    
    UE_LOG(LogTemp, Warning, TEXT("Limites das paredes calculados: %f x %f"), MapBoundarySize, MapBoundarySize);
}

TArray<FVector> AWallCollisionManager::GenerateStraightWallPoints(const FVector& StartPos, const FVector& EndPos) const
{
    TArray<FVector> Points;
    
    // Para parede reta, apenas os pontos inicial e final
    Points.Add(StartPos);
    Points.Add(EndPos);
    
    return Points;
}

TArray<FVector> AWallCollisionManager::GenerateCornerWallPoints(const FVector& CornerPos, EWallOrientation Orientation) const
{
    TArray<FVector> Points;
    
    // Gerar pontos para canto arredondado
    int32 CornerSegments = WallSegments / 4; // 1/4 dos segmentos para cada canto
    
    float StartAngle = 0.0f;
    float EndAngle = 90.0f;
    
    // Ajustar ângulos baseado na orientação
    switch (Orientation)
    {
        case EWallOrientation::NorthEast:
            StartAngle = 0.0f;
            EndAngle = 90.0f;
            break;
        case EWallOrientation::NorthWest:
            StartAngle = 90.0f;
            EndAngle = 180.0f;
            break;
        case EWallOrientation::SouthWest:
            StartAngle = 180.0f;
            EndAngle = 270.0f;
            break;
        case EWallOrientation::SouthEast:
            StartAngle = 270.0f;
            EndAngle = 360.0f;
            break;
    }
    
    for (int32 i = 0; i <= CornerSegments; i++)
    {
        float Angle = FMath::Lerp(StartAngle, EndAngle, (float)i / CornerSegments);
        float AngleRadians = FMath::DegreesToRadians(Angle);
        
        FVector Point = CornerPos + FVector(
            CornerRadius * FMath::Cos(AngleRadians),
            CornerRadius * FMath::Sin(AngleRadians),
            0.0f
        );
        
        Points.Add(Point);
    }
    
    return Points;
}

TArray<FVector> AWallCollisionManager::GenerateCurvedWallPoints(const FVector& StartPos, const FVector& EndPos, float CurveRadius) const
{
    TArray<FVector> Points;
    
    // Calcular centro da curva
    FVector MidPoint = (StartPos + EndPos) * 0.5f;
    FVector Direction = (EndPos - StartPos).GetSafeNormal();
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();
    FVector CurveCenter = MidPoint + (Perpendicular * CurveRadius);
    
    // Gerar pontos ao longo da curva
    for (int32 i = 0; i <= WallSegments; i++)
    {
        float T = (float)i / WallSegments;
        
        // Interpolação cúbica para curva suave
        float Angle = FMath::Lerp(-90.0f, 90.0f, T);
        float AngleRadians = FMath::DegreesToRadians(Angle);
        
        FVector Point = CurveCenter + FVector(
            CurveRadius * FMath::Cos(AngleRadians),
            CurveRadius * FMath::Sin(AngleRadians),
            0.0f
        );
        
        Points.Add(Point);
    }
    
    return Points;
}

FVector AWallCollisionManager::CalculateWallNormal(const FVector& StartPos, const FVector& EndPos) const
{
    FVector WallDirection = (EndPos - StartPos).GetSafeNormal();
    FVector WallNormal = FVector::CrossProduct(WallDirection, FVector::UpVector).GetSafeNormal();
    return WallNormal;
}

float AWallCollisionManager::CalculateWallLength(const FWallSection& WallSection) const
{
    if (WallSection.WallType == EWallType::Straight)
    {
        return FVector::Dist(WallSection.StartPosition, WallSection.EndPosition);
    }
    else if (WallSection.WallType == EWallType::Corner)
    {
        // Comprimento do arco do canto
        return (PI * CornerRadius) / 2.0f; // 1/4 de círculo
    }
    else if (WallSection.WallType == EWallType::Curved)
    {
        // Aproximação do comprimento da curva
        float ChordLength = FVector::Dist(WallSection.StartPosition, WallSection.EndPosition);
        return ChordLength * 1.1f; // Aproximação
    }
    
    return 0.0f;
}

FVector AWallCollisionManager::GetWallCenter(const FWallSection& WallSection) const
{
    return (WallSection.StartPosition + WallSection.EndPosition) * 0.5f;
}

// Funções de criação de meshes
void AWallCollisionManager::CreateWallMeshes()
{
    // Limpar meshes existentes
    for (UStaticMeshComponent* WallMesh : WallMeshes)
    {
        if (WallMesh)
        {
            WallMesh->DestroyComponent();
        }
    }
    WallMeshes.Empty();
    
    // Criar mesh para cada seção de parede
    for (int32 i = 0; i < WallSections.Num(); i++)
    {
        const FWallSection& WallSection = WallSections[i];
        
        for (int32 j = 0; j < WallSection.SegmentPoints.Num() - 1; j++)
        {
            UStaticMeshComponent* WallMesh = CreateWallSegment(WallSection, j);
            if (WallMesh)
            {
                WallMeshes.Add(WallMesh);
            }
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("Meshes das paredes criadas: %d segmentos"), WallMeshes.Num());
}

UStaticMeshComponent* AWallCollisionManager::CreateWallSegment(const FWallSection& WallSection, int32 SegmentIndex)
{
    if (SegmentIndex >= WallSection.SegmentPoints.Num() - 1)
    {
        return nullptr;
    }
    
    FString ComponentName = FString::Printf(TEXT("WallSegment_%d_%d"), 
                                          WallSections.IndexOfByKey(WallSection), SegmentIndex);
    
    UStaticMeshComponent* WallMesh = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
    WallMesh->SetupAttachment(RootComponent);
    
    // Calcular posição e rotação do segmento
    FVector StartPoint = WallSection.SegmentPoints[SegmentIndex];
    FVector EndPoint = WallSection.SegmentPoints[SegmentIndex + 1];
    FVector SegmentCenter = (StartPoint + EndPoint) * 0.5f;
    
    WallMesh->SetWorldLocation(SegmentCenter);
    
    // Calcular rotação
    FVector SegmentDirection = (EndPoint - StartPoint).GetSafeNormal();
    FRotator SegmentRotation = SegmentDirection.Rotation();
    WallMesh->SetWorldRotation(SegmentRotation);
    
    // Aplicar material
    ApplyWallMaterial(WallMesh, MaterialConfig);
    
    // Configurar colisão
    WallMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    WallMesh->SetCollisionResponseToAllChannels(ECR_Block);
    
    return WallMesh;
}

void AWallCollisionManager::ApplyWallMaterial(UStaticMeshComponent* WallMesh, const FWallMaterialConfig& WallMaterialConfig)
{
    if (!WallMesh || !WallMaterialConfig.Material)
    {
        return;
    }

    WallMesh->SetMaterial(0, WallMaterialConfig.Material);

    // Configurar propriedades baseadas no tipo de material
    switch (WallMaterialConfig.MaterialType)
    {
        case EWallMaterial::Stone:
            WallMesh->SetMassOverrideInKg(NAME_None, 1000.0f);
            break;
        case EWallMaterial::Metal:
            WallMesh->SetMassOverrideInKg(NAME_None, 800.0f);
            break;
        case EWallMaterial::Wood:
            WallMesh->SetMassOverrideInKg(NAME_None, 200.0f);
            break;
        case EWallMaterial::Crystal:
            WallMesh->SetMassOverrideInKg(NAME_None, 500.0f);
            break;
        case EWallMaterial::Magical:
            WallMesh->SetMassOverrideInKg(NAME_None, 100.0f);
            break;
    }
}

void AWallCollisionManager::UpdateWallMeshes()
{
    // Recriar todas as meshes
    CreateWallMeshes();
}

// Sistema de colisões
bool AWallCollisionManager::CheckCollisionWithWalls(const FVector& StartPos, const FVector& EndPos, FWallCollisionData& OutCollisionData) const
{
    FVector Direction = (EndPos - StartPos).GetSafeNormal();
    float Distance = FVector::Dist(StartPos, EndPos);
    
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = true;
    
    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        StartPos,
        EndPos,
        ECC_WorldStatic,
        QueryParams
    );
    
    if (bHit)
    {
        OutCollisionData.CollisionPoint = HitResult.Location;
        OutCollisionData.CollisionNormal = HitResult.Normal;
        OutCollisionData.CollisionDistance = HitResult.Distance;
        OutCollisionData.CollidingActor = HitResult.GetActor();
        OutCollisionData.CollisionTime = FDateTime::Now();
        OutCollisionData.ImpactVelocity = Direction * Distance;
        
        return true;
    }
    
    return false;
}

bool AWallCollisionManager::IsPositionInsideWalls(const FVector& Position) const
{
    // Verificar se a posição está dentro dos limites das paredes
    float HalfSize = MapBoundarySize * 0.5f;
    
    return (Position.X > -HalfSize && Position.X < HalfSize &&
            Position.Y > -HalfSize && Position.Y < HalfSize);
}

FVector AWallCollisionManager::GetClosestPointOnWalls(const FVector& Position) const
{
    FVector ClosestPoint = Position;
    float MinDistance = FLT_MAX;
    
    for (const FWallSection& WallSection : WallSections)
    {
        for (int32 i = 0; i < WallSection.SegmentPoints.Num() - 1; i++)
        {
            FVector SegmentStart = WallSection.SegmentPoints[i];
            FVector SegmentEnd = WallSection.SegmentPoints[i + 1];
            
            FVector ProjectedPoint = ProjectPointOntoLine(Position, SegmentStart, SegmentEnd);
            float Distance = FVector::Dist(Position, ProjectedPoint);
            
            if (Distance < MinDistance)
            {
                MinDistance = Distance;
                ClosestPoint = ProjectedPoint;
            }
        }
    }
    
    return ClosestPoint;
}

float AWallCollisionManager::GetDistanceToNearestWall(const FVector& Position) const
{
    // Usar cache se disponível
    if (ActorDistanceCache.Contains(nullptr)) // Simplificado para exemplo
    {
        // return ActorDistanceCache[nullptr];
    }
    
    FVector ClosestPoint = GetClosestPointOnWalls(Position);
    return FVector::Dist(Position, ClosestPoint);
}

TArray<FVector> AWallCollisionManager::GetWallIntersectionPoints(const FVector& LineStart, const FVector& LineEnd) const
{
    TArray<FVector> IntersectionPoints;
    
    for (const FWallSection& WallSection : WallSections)
    {
        FVector IntersectionPoint;
        if (LineIntersectsWall(LineStart, LineEnd, WallSection, IntersectionPoint))
        {
            IntersectionPoints.Add(IntersectionPoint);
        }
    }
    
    return IntersectionPoints;
}

bool AWallCollisionManager::LineIntersectsWall(const FVector& LineStart, const FVector& LineEnd, const FWallSection& WallSection, FVector& OutIntersectionPoint) const
{
    // Verificar interseção com cada segmento da parede
    for (int32 i = 0; i < WallSection.SegmentPoints.Num() - 1; i++)
    {
        FVector WallStart = WallSection.SegmentPoints[i];
        FVector WallEnd = WallSection.SegmentPoints[i + 1];
        
        // Calcular interseção de duas linhas 2D usando FVector para compatibilidade com UE5.6
        FVector Line1Start3D(LineStart.X, LineStart.Y, 0.0f);
        FVector Line1End3D(LineEnd.X, LineEnd.Y, 0.0f);
        FVector Line2Start3D(WallStart.X, WallStart.Y, 0.0f);
        FVector Line2End3D(WallEnd.X, WallEnd.Y, 0.0f);
        
        FVector Intersection;
        if (FMath::SegmentIntersection2D(Line1Start3D, Line1End3D, Line2Start3D, Line2End3D, Intersection))
        {
            OutIntersectionPoint = FVector(Intersection.X, Intersection.Y, LineStart.Z);
            return true;
        }
    }
    
    return false;
}

// Sistema de portões
void AWallCollisionManager::CreateGates()
{
    // Limpar portões existentes
    Gates.Empty();
    
    // Inicializar portões padrão
    InitializeDefaultGates();
    
    UE_LOG(LogTemp, Warning, TEXT("Portões criados: %d"), Gates.Num());
}

void AWallCollisionManager::OpenGate(int32 GateIndex)
{
    if (GateIndex >= 0 && GateIndex < Gates.Num())
    {
        Gates[GateIndex].bIsOpen = true;
        UE_LOG(LogTemp, Warning, TEXT("Portão %d aberto"), GateIndex);
    }
}

void AWallCollisionManager::CloseGate(int32 GateIndex)
{
    if (GateIndex >= 0 && GateIndex < Gates.Num())
    {
        Gates[GateIndex].bIsOpen = false;
        UE_LOG(LogTemp, Warning, TEXT("Portão %d fechado"), GateIndex);
    }
}

bool AWallCollisionManager::IsGateOpen(int32 GateIndex) const
{
    if (GateIndex >= 0 && GateIndex < Gates.Num())
    {
        return Gates[GateIndex].bIsOpen;
    }
    return false;
}

bool AWallCollisionManager::CanActorPassThroughGate(AActor* Actor, int32 GateIndex) const
{
    if (!Actor || GateIndex < 0 || GateIndex >= Gates.Num())
    {
        return false;
    }
    
    const FGateData& Gate = Gates[GateIndex];
    
    // Verificar se o portão está aberto
    if (!Gate.bIsOpen)
    {
        return false;
    }
    
    // Verificar se o ator está autorizado
    return Gate.AuthorizedActors.Contains(Actor);
}

void AWallCollisionManager::AuthorizeActorForGate(AActor* Actor, int32 GateIndex)
{
    if (Actor && GateIndex >= 0 && GateIndex < Gates.Num())
    {
        Gates[GateIndex].AuthorizedActors.AddUnique(Actor);
        UE_LOG(LogTemp, Warning, TEXT("Ator %s autorizado para portão %d"), *Actor->GetName(), GateIndex);
    }
}

FVector AWallCollisionManager::GetNearestGatePosition(const FVector& Position) const
{
    if (Gates.Num() == 0)
    {
        return Position;
    }
    
    FVector NearestGatePos = Gates[0].Position;
    float MinDistance = FVector::Dist(Position, NearestGatePos);
    
    for (int32 i = 1; i < Gates.Num(); i++)
    {
        float Distance = FVector::Dist(Position, Gates[i].Position);
        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            NearestGatePos = Gates[i].Position;
        }
    }
    
    return NearestGatePos;
}

// Funções de pathfinding e navegação
TArray<FVector> AWallCollisionManager::FindPathAroundWalls(const FVector& StartPos, const FVector& EndPos) const
{
    TArray<FVector> Path;
    
    // Verificar se o caminho direto está livre
    if (IsPathClearOfWalls(StartPos, EndPos))
    {
        Path.Add(StartPos);
        Path.Add(EndPos);
        return Path;
    }
    
    // Implementar pathfinding simples ao redor das paredes
    // Por simplicidade, usar os cantos das paredes como waypoints
    Path.Add(StartPos);
    
    // Adicionar pontos de contorno das paredes
    float HalfSize = MapBoundarySize * 0.5f;
    float Offset = 100.0f; // Offset das paredes
    
    // Determinar qual lado contornar baseado na posição
    if (StartPos.X < 0 && EndPos.X > 0) // Atravessar de oeste para leste
    {
        Path.Add(FVector(-HalfSize - Offset, StartPos.Y, StartPos.Z));
        Path.Add(FVector(-HalfSize - Offset, EndPos.Y, EndPos.Z));
    }
    else if (StartPos.X > 0 && EndPos.X < 0) // Atravessar de leste para oeste
    {
        Path.Add(FVector(HalfSize + Offset, StartPos.Y, StartPos.Z));
        Path.Add(FVector(HalfSize + Offset, EndPos.Y, EndPos.Z));
    }
    
    Path.Add(EndPos);
    
    return Path;
}

bool AWallCollisionManager::IsPathClearOfWalls(const FVector& StartPos, const FVector& EndPos) const
{
    FWallCollisionData CollisionData;
    return !CheckCollisionWithWalls(StartPos, EndPos, CollisionData);
}

FVector AWallCollisionManager::GetSafePositionNearWalls(const FVector& DesiredPosition, float SafeDistance) const
{
    FVector SafePosition = DesiredPosition;
    
    float DistanceToWall = GetDistanceToNearestWall(DesiredPosition);
    
    if (DistanceToWall < SafeDistance)
    {
        FVector ClosestWallPoint = GetClosestPointOnWalls(DesiredPosition);
        FVector DirectionFromWall = (DesiredPosition - ClosestWallPoint).GetSafeNormal();
        SafePosition = ClosestWallPoint + (DirectionFromWall * SafeDistance);
    }
    
    return SafePosition;
}

TArray<FVector> AWallCollisionManager::GeneratePatrolPath() const
{
    TArray<FVector> PatrolPoints;
    
    if (PatrolArea.bIsCircular)
    {
        // Gerar pontos em círculo ao redor do centro do mapa
        FVector MapCenter = GetMapCenter();
        int32 NumPoints = 8; // 8 pontos de patrulha
        
        for (int32 i = 0; i < NumPoints; i++)
        {
            float Angle = (2.0f * PI * i) / NumPoints;
            FVector PatrolPoint = MapCenter + FVector(
                PatrolArea.PatrolRadius * FMath::Cos(Angle),
                PatrolArea.PatrolRadius * FMath::Sin(Angle),
                0.0f
            );
            
            PatrolPoints.Add(PatrolPoint);
        }
    }
    else
    {
        // Usar pontos personalizados
        PatrolPoints = PatrolArea.PatrolPoints;
    }
    
    return PatrolPoints;
}

// Funções de validação e debug
bool AWallCollisionManager::ValidateWallGeometry() const
{
    for (const FWallSection& WallSection : WallSections)
    {
        if (!ValidateWallSection(WallSection))
        {
            return false;
        }
    }
    
    return true;
}

bool AWallCollisionManager::ValidateCollisionSetup() const
{
    if (CollisionBoxes.Num() != WallSections.Num())
    {
        UE_LOG(LogTemp, Error, TEXT("Número de caixas de colisão não corresponde ao número de seções de parede"));
        return false;
    }
    
    for (UBoxComponent* CollisionBox : CollisionBoxes)
    {
        if (!CollisionBox || !CollisionBox->IsValidLowLevel())
        {
            UE_LOG(LogTemp, Error, TEXT("Caixa de colisão inválida encontrada"));
            return false;
        }
    }
    
    return true;
}

void AWallCollisionManager::DrawDebugWalls() const
{
    if (!GetWorld()) return;
    
    for (const FWallSection& WallSection : WallSections)
    {
        FColor WallColor = FColor::Blue;
        
        // Cor baseada no tipo de parede
        switch (WallSection.WallType)
        {
            case EWallType::Straight:
                WallColor = FColor::Blue;
                break;
            case EWallType::Corner:
                WallColor = FColor::Green;
                break;
            case EWallType::Gate:
                WallColor = FColor::Yellow;
                break;
            case EWallType::Curved:
                WallColor = FColor::Purple;
                break;
        }
        
        // Desenhar segmentos da parede
        for (int32 i = 0; i < WallSection.SegmentPoints.Num() - 1; i++)
        {
            FVector Start = WallSection.SegmentPoints[i];
            FVector End = WallSection.SegmentPoints[i + 1];
            
            DrawDebugLine(GetWorld(), Start, End, WallColor, false, 1.0f, 0, 5.0f);
            
            // Desenhar altura da parede
            FVector StartTop = Start + FVector(0.0f, 0.0f, WallSection.Height);
            FVector EndTop = End + FVector(0.0f, 0.0f, WallSection.Height);
            
            DrawDebugLine(GetWorld(), Start, StartTop, WallColor, false, 1.0f, 0, 2.0f);
            DrawDebugLine(GetWorld(), End, EndTop, WallColor, false, 1.0f, 0, 2.0f);
            DrawDebugLine(GetWorld(), StartTop, EndTop, WallColor, false, 1.0f, 0, 5.0f);
        }
    }
}

void AWallCollisionManager::DrawDebugCollisions() const
{
    if (!GetWorld()) return;
    
    for (const FWallCollisionData& Collision : RecentCollisions)
    {
        // Desenhar ponto de colisão
        DrawDebugSphere(GetWorld(), Collision.CollisionPoint, 20.0f, 8, FColor::Red, false, 1.0f);
        
        // Desenhar normal da colisão
        FVector NormalEnd = Collision.CollisionPoint + (Collision.CollisionNormal * 100.0f);
        DrawDebugLine(GetWorld(), Collision.CollisionPoint, NormalEnd, FColor::Orange, false, 1.0f, 0, 3.0f);
    }
}

void AWallCollisionManager::DrawDebugGates() const
{
    if (!GetWorld()) return;
    
    for (const FGateData& Gate : Gates)
    {
        FColor GateColor = Gate.bIsOpen ? FColor::Green : FColor::Red;
        
        // Desenhar posição do portão
        DrawDebugSphere(GetWorld(), Gate.Position, 50.0f, 12, GateColor, false, 1.0f);
        
        // Desenhar largura do portão
        FVector GateStart = Gate.Position - FVector(Gate.Width * 0.5f, 0.0f, 0.0f);
        FVector GateEnd = Gate.Position + FVector(Gate.Width * 0.5f, 0.0f, 0.0f);
        
        DrawDebugLine(GetWorld(), GateStart, GateEnd, GateColor, false, 1.0f, 0, 8.0f);
    }
}

void AWallCollisionManager::DrawDebugPatrolPath() const
{
    if (!GetWorld() || PatrolArea.PatrolPoints.Num() < 2) return;
    
    for (int32 i = 0; i < PatrolArea.PatrolPoints.Num() - 1; i++)
    {
        DrawDebugLine(
            GetWorld(),
            PatrolArea.PatrolPoints[i],
            PatrolArea.PatrolPoints[i + 1],
            FColor::Cyan,
            false,
            1.0f,
            0,
            4.0f
        );
        
        DrawDebugSphere(
            GetWorld(),
            PatrolArea.PatrolPoints[i],
            30.0f,
            8,
            FColor::Cyan,
            false,
            1.0f
        );
    }
    
    // Conectar último ao primeiro se for circular
    if (PatrolArea.bIsCircular && PatrolArea.PatrolPoints.Num() > 2)
    {
        DrawDebugLine(
            GetWorld(),
            PatrolArea.PatrolPoints.Last(),
            PatrolArea.PatrolPoints[0],
            FColor::Cyan,
            false,
            1.0f,
            0,
            4.0f
        );
    }
}

// Eventos de colisão
void AWallCollisionManager::OnWallHit(UPrimitiveComponent* HitComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, FVector NormalImpulse, const FHitResult& Hit)
{
    if (!OtherActor) return;
    
    FWallCollisionData CollisionData;
    CollisionData.CollisionPoint = Hit.Location;
    CollisionData.CollisionNormal = Hit.Normal;
    CollisionData.CollisionDistance = Hit.Distance;
    CollisionData.CollidingActor = OtherActor;
    CollisionData.CollisionTime = FDateTime::Now();
    CollisionData.ImpactVelocity = NormalImpulse;
    
    UpdateCollisionHistory(CollisionData);
    
    if (bEnableCollisionLogging)
    {
        LogCollisionEvent(CollisionData);
    }
}

void AWallCollisionManager::OnActorBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Lógica para quando um ator entra na área das paredes
    UE_LOG(LogTemp, Warning, TEXT("Ator %s entrou na área das paredes"), OtherActor ? *OtherActor->GetName() : TEXT("Unknown"));
}

void AWallCollisionManager::OnActorEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex)
{
    // Lógica para quando um ator sai da área das paredes
    UE_LOG(LogTemp, Warning, TEXT("Ator %s saiu da área das paredes"), OtherActor ? *OtherActor->GetName() : TEXT("Unknown"));
}

// Funções de utilidade matemática
FVector AWallCollisionManager::RotatePointAroundCenter(const FVector& Point, const FVector& Center, float AngleDegrees) const
{
    float AngleRadians = FMath::DegreesToRadians(AngleDegrees);
    FVector LocalPoint = Point - Center;
    
    float CosAngle = FMath::Cos(AngleRadians);
    float SinAngle = FMath::Sin(AngleRadians);
    
    FVector RotatedPoint;
    RotatedPoint.X = LocalPoint.X * CosAngle - LocalPoint.Y * SinAngle;
    RotatedPoint.Y = LocalPoint.X * SinAngle + LocalPoint.Y * CosAngle;
    RotatedPoint.Z = LocalPoint.Z;
    
    return Center + RotatedPoint;
}

float AWallCollisionManager::CalculateAngleBetweenVectors(const FVector& Vector1, const FVector& Vector2) const
{
    float DotProduct = FVector::DotProduct(Vector1.GetSafeNormal(), Vector2.GetSafeNormal());
    return FMath::RadiansToDegrees(FMath::Acos(FMath::Clamp(DotProduct, -1.0f, 1.0f)));
}

bool AWallCollisionManager::IsPointInsidePolygon(const FVector& Point, const TArray<FVector>& PolygonVertices) const
{
    if (PolygonVertices.Num() < 3) return false;
    
    int32 Intersections = 0;
    
    for (int32 i = 0; i < PolygonVertices.Num(); i++)
    {
        int32 NextIndex = (i + 1) % PolygonVertices.Num();
        
        FVector V1 = PolygonVertices[i];
        FVector V2 = PolygonVertices[NextIndex];
        
        // Ray casting algorithm
        if (((V1.Y > Point.Y) != (V2.Y > Point.Y)) &&
            (Point.X < (V2.X - V1.X) * (Point.Y - V1.Y) / (V2.Y - V1.Y) + V1.X))
        {
            Intersections++;
        }
    }
    
    return (Intersections % 2) == 1;
}

FVector AWallCollisionManager::ProjectPointOntoLine(const FVector& Point, const FVector& LineStart, const FVector& LineEnd) const
{
    FVector LineDirection = (LineEnd - LineStart).GetSafeNormal();
    FVector PointToStart = Point - LineStart;
    
    float ProjectionLength = FVector::DotProduct(PointToStart, LineDirection);
    
    // Clampar à linha
    float LineLength = FVector::Dist(LineStart, LineEnd);
    ProjectionLength = FMath::Clamp(ProjectionLength, 0.0f, LineLength);
    
    return LineStart + (LineDirection * ProjectionLength);
}

float AWallCollisionManager::DistancePointToLine(const FVector& Point, const FVector& LineStart, const FVector& LineEnd) const
{
    FVector ProjectedPoint = ProjectPointOntoLine(Point, LineStart, LineEnd);
    return FVector::Dist(Point, ProjectedPoint);
}

// Getters
float AWallCollisionManager::GetTotalWallLength() const
{
    float TotalLength = 0.0f;
    
    for (const FWallSection& WallSection : WallSections)
    {
        TotalLength += CalculateWallLength(WallSection);
    }
    
    return TotalLength;
}

float AWallCollisionManager::GetWallPerimeter() const
{
    return GetTotalWallLength();
}

FVector AWallCollisionManager::GetMapCenter() const
{
    return FVector::ZeroVector; // Centro do mapa na origem
}

float AWallCollisionManager::GetMapArea() const
{
    return MapBoundarySize * MapBoundarySize;
}

// Setters
void AWallCollisionManager::SetWallMaterial(const FWallMaterialConfig& NewMaterialConfig)
{
    MaterialConfig = NewMaterialConfig;
    UpdateWallMeshes();
}

void AWallCollisionManager::SetWallHeight(float NewHeight)
{
    WallHeight = NewHeight;
    
    for (FWallSection& WallSection : WallSections)
    {
        WallSection.Height = NewHeight;
    }
    
    UpdateWallMeshes();
}

void AWallCollisionManager::SetWallThickness(float NewThickness)
{
    WallThickness = NewThickness;
    
    for (FWallSection& WallSection : WallSections)
    {
        WallSection.Thickness = NewThickness;
    }
    
    UpdateWallMeshes();
}

// Funções auxiliares internas
void AWallCollisionManager::InitializeDefaultWallSections()
{
    float HalfSize = MapBoundarySize * 0.5f;
    
    // Parede Norte
    FWallSection NorthWall;
    NorthWall.StartPosition = FVector(-HalfSize, -HalfSize, 0.0f);
    NorthWall.EndPosition = FVector(HalfSize, -HalfSize, 0.0f);
    NorthWall.WallType = EWallType::Straight;
    NorthWall.Orientation = EWallOrientation::North;
    NorthWall.Height = WallHeight;
    NorthWall.Thickness = WallThickness;
    NorthWall.SegmentPoints = GenerateStraightWallPoints(NorthWall.StartPosition, NorthWall.EndPosition);
    WallSections.Add(NorthWall);
    
    // Parede Sul
    FWallSection SouthWall;
    SouthWall.StartPosition = FVector(HalfSize, HalfSize, 0.0f);
    SouthWall.EndPosition = FVector(-HalfSize, HalfSize, 0.0f);
    SouthWall.WallType = EWallType::Straight;
    SouthWall.Orientation = EWallOrientation::South;
    SouthWall.Height = WallHeight;
    SouthWall.Thickness = WallThickness;
    SouthWall.SegmentPoints = GenerateStraightWallPoints(SouthWall.StartPosition, SouthWall.EndPosition);
    WallSections.Add(SouthWall);
    
    // Parede Leste
    FWallSection EastWall;
    EastWall.StartPosition = FVector(HalfSize, -HalfSize, 0.0f);
    EastWall.EndPosition = FVector(HalfSize, HalfSize, 0.0f);
    EastWall.WallType = EWallType::Straight;
    EastWall.Orientation = EWallOrientation::East;
    EastWall.Height = WallHeight;
    EastWall.Thickness = WallThickness;
    EastWall.SegmentPoints = GenerateStraightWallPoints(EastWall.StartPosition, EastWall.EndPosition);
    WallSections.Add(EastWall);
    
    // Parede Oeste
    FWallSection WestWall;
    WestWall.StartPosition = FVector(-HalfSize, HalfSize, 0.0f);
    WestWall.EndPosition = FVector(-HalfSize, -HalfSize, 0.0f);
    WestWall.WallType = EWallType::Straight;
    WestWall.Orientation = EWallOrientation::West;
    WestWall.Height = WallHeight;
    WestWall.Thickness = WallThickness;
    WestWall.SegmentPoints = GenerateStraightWallPoints(WestWall.StartPosition, WestWall.EndPosition);
    WallSections.Add(WestWall);
}

void AWallCollisionManager::InitializeDefaultGates()
{
    float HalfSize = MapBoundarySize * 0.5f;
    
    // Portão Norte
    FGateData NorthGate;
    NorthGate.Position = FVector(0.0f, -HalfSize, 0.0f);
    NorthGate.Orientation = EWallOrientation::North;
    NorthGate.Width = GATE_WIDTH;
    NorthGate.Height = WallHeight;
    NorthGate.bIsOpen = false;
    NorthGate.bIsLocked = true;
    Gates.Add(NorthGate);
    
    // Portão Sul
    FGateData SouthGate;
    SouthGate.Position = FVector(0.0f, HalfSize, 0.0f);
    SouthGate.Orientation = EWallOrientation::South;
    SouthGate.Width = GATE_WIDTH;
    SouthGate.Height = WallHeight;
    SouthGate.bIsOpen = false;
    SouthGate.bIsLocked = true;
    Gates.Add(SouthGate);
}

void AWallCollisionManager::SetupCollisionCallbacks()
{
    for (UBoxComponent* CollisionBox : CollisionBoxes)
    {
        if (CollisionBox)
        {
            CollisionBox->OnComponentHit.AddDynamic(this, &AWallCollisionManager::OnWallHit);
            CollisionBox->OnComponentBeginOverlap.AddDynamic(this, &AWallCollisionManager::OnActorBeginOverlap);
            CollisionBox->OnComponentEndOverlap.AddDynamic(this, &AWallCollisionManager::OnActorEndOverlap);
        }
    }
}

void AWallCollisionManager::UpdateCollisionHistory(const FWallCollisionData& CollisionData)
{
    RecentCollisions.Add(CollisionData);
    
    // Manter apenas as colisões mais recentes
    if (RecentCollisions.Num() > MaxCollisionHistory)
    {
        RecentCollisions.RemoveAt(0);
    }
}

void AWallCollisionManager::CleanupOldCollisions()
{
    FDateTime CurrentTime = FDateTime::Now();
    FTimespan MaxAge = FTimespan::FromMinutes(5.0); // Manter colisões por 5 minutos
    
    RecentCollisions.RemoveAll([CurrentTime, MaxAge](const FWallCollisionData& Collision)
    {
        return (CurrentTime - Collision.CollisionTime) > MaxAge;
    });
}

FVector AWallCollisionManager::CalculateCornerPosition(EWallOrientation Orientation, float Radius) const
{
    float HalfSize = MapBoundarySize * 0.5f;
    
    switch (Orientation)
    {
        case EWallOrientation::NorthEast:
            return FVector(HalfSize - Radius, -HalfSize + Radius, 0.0f);
        case EWallOrientation::NorthWest:
            return FVector(-HalfSize + Radius, -HalfSize + Radius, 0.0f);
        case EWallOrientation::SouthWest:
            return FVector(-HalfSize + Radius, HalfSize - Radius, 0.0f);
        case EWallOrientation::SouthEast:
            return FVector(HalfSize - Radius, HalfSize - Radius, 0.0f);
        default:
            return FVector::ZeroVector;
    }
}

TArray<FVector> AWallCollisionManager::InterpolateWallPoints(const TArray<FVector>& ControlPoints, int32 Segments) const
{
    TArray<FVector> InterpolatedPoints;
    
    if (ControlPoints.Num() < 2)
    {
        return InterpolatedPoints;
    }
    
    for (int32 i = 0; i < ControlPoints.Num() - 1; i++)
    {
        FVector StartPoint = ControlPoints[i];
        FVector EndPoint = ControlPoints[i + 1];
        
        for (int32 j = 0; j <= Segments; j++)
        {
            float T = (float)j / Segments;
            FVector InterpolatedPoint = FMath::Lerp(StartPoint, EndPoint, T);
            InterpolatedPoints.Add(InterpolatedPoint);
        }
    }
    
    return InterpolatedPoints;
}

bool AWallCollisionManager::ValidateWallSection(const FWallSection& WallSection) const
{
    if (WallSection.Height <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("Altura da parede deve ser positiva"));
        return false;
    }
    
    if (WallSection.Thickness <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("Espessura da parede deve ser positiva"));
        return false;
    }
    
    if (WallSection.SegmentPoints.Num() < 2)
    {
        UE_LOG(LogTemp, Error, TEXT("Seção de parede deve ter pelo menos 2 pontos"));
        return false;
    }
    
    return true;
}

void AWallCollisionManager::LogCollisionEvent(const FWallCollisionData& CollisionData) const
{
    UE_LOG(LogTemp, Warning, TEXT("Colisão com parede: Ator=%s, Ponto=%s, Normal=%s, Distância=%f"),
           CollisionData.CollidingActor ? *CollisionData.CollidingActor->GetName() : TEXT("Unknown"),
           *CollisionData.CollisionPoint.ToString(),
           *CollisionData.CollisionNormal.ToString(),
           CollisionData.CollisionDistance);
}