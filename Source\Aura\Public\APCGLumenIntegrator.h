#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Rendering/NaniteResources.h"
#include "RenderGraphBuilder.h"
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "DataDrivenShaderPlatformInfo.h"
#include "RHICommandList.h"
// UE5.6 Rendering API includes - using public interfaces
#include "ShaderParameters.h"
#include "Engine/RendererSettings.h"
#include "Engine/PostProcessVolume.h"
#include "Components/PostProcessComponent.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/Transform.h"
#include "Math/Color.h"
#include "HAL/ThreadSafeBool.h"
#include "Async/AsyncWork.h"
#include "Async/TaskGraphInterfaces.h"
#include "APCGLumenIntegrator.generated.h"

// Forward Declarations
class UStaticMeshComponent;
class ULightComponent;
class UMaterialInterface;
class UPostProcessComponent;
class APostProcessVolume;
class UReflectionCaptureComponent;
class FRDGBuilder;
class URendererSettings;
class APostProcessVolume;

// Enums
UENUM(BlueprintType)
enum class EPCGLumenQuality : uint8
{
    Low         UMETA(DisplayName = "Low Quality"),
    Medium      UMETA(DisplayName = "Medium Quality"),
    High        UMETA(DisplayName = "High Quality"),
    Epic        UMETA(DisplayName = "Epic Quality"),
    Cinematic   UMETA(DisplayName = "Cinematic Quality")
};

UENUM(BlueprintType)
enum class EPCGLumenLightType : uint8
{
    Directional     UMETA(DisplayName = "Directional Light"),
    Point           UMETA(DisplayName = "Point Light"),
    Spot            UMETA(DisplayName = "Spot Light"),
    Sky             UMETA(DisplayName = "Sky Light"),
    Area            UMETA(DisplayName = "Area Light"),
    Emissive        UMETA(DisplayName = "Emissive Surface")
};

UENUM(BlueprintType)
enum class EPCGLumenUpdateMode : uint8
{
    Static          UMETA(DisplayName = "Static - No Updates"),
    Dynamic         UMETA(DisplayName = "Dynamic - Real-time Updates"),
    Adaptive        UMETA(DisplayName = "Adaptive - Performance Based"),
    OnDemand        UMETA(DisplayName = "On Demand - Manual Updates")
};

UENUM(BlueprintType)
enum class EPCGLumenReflectionMode : uint8
{
    ScreenSpace     UMETA(DisplayName = "Screen Space Reflections"),
    RayTraced       UMETA(DisplayName = "Ray Traced Reflections"),
    Hybrid          UMETA(DisplayName = "Hybrid Reflections"),
    Disabled        UMETA(DisplayName = "Disabled")
};

// Structures
USTRUCT(BlueprintType)
struct AURA_API FPCGLumenConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EPCGLumenQuality Quality = EPCGLumenQuality::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EPCGLumenUpdateMode UpdateMode = EPCGLumenUpdateMode::Adaptive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reflections")
    EPCGLumenReflectionMode ReflectionMode = EPCGLumenReflectionMode::Hybrid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float GlobalIlluminationIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float ReflectionIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "8"))
    int32 MaxBounces = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float UpdateFrequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableTemporalUpsampling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableHardwareRayTracing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableAsyncCompute = true;

    FPCGLumenConfig()
    {
        Quality = EPCGLumenQuality::High;
        UpdateMode = EPCGLumenUpdateMode::Adaptive;
        ReflectionMode = EPCGLumenReflectionMode::Hybrid;
        GlobalIlluminationIntensity = 1.0f;
        ReflectionIntensity = 1.0f;
        MaxBounces = 3;
        UpdateFrequency = 1.0f;
        bEnableTemporalUpsampling = true;
        bEnableHardwareRayTracing = true;
        bEnableAsyncCompute = true;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGLumenLightData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light")
    EPCGLumenLightType LightType = EPCGLumenLightType::Point;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light")
    FVector Position = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light")
    FLinearColor Color = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light", meta = (ClampMin = "0.0", ClampMax = "100000.0"))
    float Intensity = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float AttenuationRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light")
    bool bCastShadows = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light")
    bool bAffectGlobalIllumination = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Light")
    TWeakObjectPtr<ULightComponent> LightComponent;

    FPCGLumenLightData()
    {
        LightType = EPCGLumenLightType::Point;
        Position = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Color = FLinearColor::White;
        Intensity = 1000.0f;
        AttenuationRadius = 1000.0f;
        bCastShadows = true;
        bAffectGlobalIllumination = true;
        LightComponent = nullptr;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGLumenSurfaceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface")
    TWeakObjectPtr<UStaticMeshComponent> MeshComponent;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface")
    TWeakObjectPtr<UMaterialInterface> Material;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface")
    FLinearColor EmissiveColor = FLinearColor::Black;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface", meta = (ClampMin = "0.0", ClampMax = "100.0"))
    float EmissiveIntensity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Roughness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Metallic = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface")
    bool bTwoSided = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface")
    bool bAffectGlobalIllumination = true;

    FPCGLumenSurfaceData()
    {
        MeshComponent = nullptr;
        Material = nullptr;
        EmissiveColor = FLinearColor::Black;
        EmissiveIntensity = 0.0f;
        Roughness = 0.5f;
        Metallic = 0.0f;
        bTwoSided = false;
        bAffectGlobalIllumination = true;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGLumenPerformanceStats
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float GlobalIlluminationTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float ReflectionTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float SurfaceCacheTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float RadianceCacheTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 ActiveLights = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 ActiveSurfaces = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 MemoryUsageMB = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float FrameTime = 0.0f;

    FPCGLumenPerformanceStats()
    {
        GlobalIlluminationTime = 0.0f;
        ReflectionTime = 0.0f;
        SurfaceCacheTime = 0.0f;
        RadianceCacheTime = 0.0f;
        ActiveLights = 0;
        ActiveSurfaces = 0;
        MemoryUsageMB = 0;
        FrameTime = 0.0f;
    }
};

/**
 * APCGLumenIntegrator - Advanced Lumen integration for procedural content generation
 * Provides dynamic global illumination and reflection management for PCG systems
 * Optimized for UE5.6 with modern rendering pipeline integration
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API APCGLumenIntegrator : public AActor
{
    GENERATED_BODY()

public:
    APCGLumenIntegrator();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lumen Config")
    FPCGLumenConfig LumenConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceMonitoring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bAutoOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDebugInfo = false;

    // Light Management
    UFUNCTION(BlueprintCallable, Category = "Lumen Lights")
    void RegisterLight(ULightComponent* LightComponent, const FPCGLumenLightData& LightData);

    UFUNCTION(BlueprintCallable, Category = "Lumen Lights")
    void UnregisterLight(ULightComponent* LightComponent);

    UFUNCTION(BlueprintCallable, Category = "Lumen Lights")
    void UpdateLightData(ULightComponent* LightComponent, const FPCGLumenLightData& NewLightData);

    UFUNCTION(BlueprintCallable, Category = "Lumen Lights")
    void SetGlobalLightIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Lumen Lights")
    TArray<FPCGLumenLightData> GetActiveLights() const;

    // Surface Management
    UFUNCTION(BlueprintCallable, Category = "Lumen Surfaces")
    void RegisterSurface(UStaticMeshComponent* MeshComponent, const FPCGLumenSurfaceData& SurfaceData);

    UFUNCTION(BlueprintCallable, Category = "Lumen Surfaces")
    void UnregisterSurface(UStaticMeshComponent* MeshComponent);

    UFUNCTION(BlueprintCallable, Category = "Lumen Surfaces")
    void UpdateSurfaceData(UStaticMeshComponent* MeshComponent, const FPCGLumenSurfaceData& NewSurfaceData);

    UFUNCTION(BlueprintCallable, Category = "Lumen Surfaces")
    void SetEmissiveSurface(UStaticMeshComponent* MeshComponent, const FLinearColor& EmissiveColor, float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Lumen Surfaces")
    TArray<FPCGLumenSurfaceData> GetActiveSurfaces() const;

    // Global Illumination Control
    UFUNCTION(BlueprintCallable, Category = "Lumen GI")
    void SetGlobalIlluminationIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Lumen GI")
    void SetReflectionIntensity(float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Lumen GI")
    void SetLumenQuality(EPCGLumenQuality Quality);

    UFUNCTION(BlueprintCallable, Category = "Lumen GI")
    void SetUpdateMode(EPCGLumenUpdateMode UpdateMode);

    UFUNCTION(BlueprintCallable, Category = "Lumen GI")
    void ForceGlobalIlluminationUpdate();

    // Reflection Control
    UFUNCTION(BlueprintCallable, Category = "Lumen Reflections")
    void SetReflectionMode(EPCGLumenReflectionMode ReflectionMode);

    UFUNCTION(BlueprintCallable, Category = "Lumen Reflections")
    void UpdateReflectionCaptures();

    UFUNCTION(BlueprintCallable, Category = "Lumen Reflections")
    void SetMaxReflectionBounces(int32 MaxBounces);

    // Performance Management
    UFUNCTION(BlueprintCallable, Category = "Lumen Performance")
    FPCGLumenPerformanceStats GetPerformanceStats() const;

    UFUNCTION(BlueprintCallable, Category = "Lumen Performance")
    void OptimizeForPerformance();

    UFUNCTION(BlueprintCallable, Category = "Lumen Performance")
    void SetAdaptiveQuality(bool bEnable);

    // Integration with other systems
    UFUNCTION(BlueprintCallable, Category = "Lumen Integration")
    void IntegrateWithNanite(class APCGNaniteOptimizer* NaniteOptimizer);

    UFUNCTION(BlueprintCallable, Category = "Lumen Integration")
    void IntegrateWithWorldPartition(class APCGWorldPartitionManager* WorldPartitionManager);

    UFUNCTION(BlueprintCallable, Category = "Lumen Integration")
    void SynchronizeWithPCGSystem();

private:
    // Internal data
    UPROPERTY()
    TMap<TWeakObjectPtr<ULightComponent>, FPCGLumenLightData> RegisteredLights;

    UPROPERTY()
    TMap<TWeakObjectPtr<UStaticMeshComponent>, FPCGLumenSurfaceData> RegisteredSurfaces;

    UPROPERTY()
    FPCGLumenPerformanceStats CurrentStats;

    // Internal state
    FThreadSafeBool bIsInitialized;
    FThreadSafeBool bNeedsUpdate;
    float LastUpdateTime;
    float AccumulatedDeltaTime;

    // Integration references
    UPROPERTY()
    TWeakObjectPtr<class APCGNaniteOptimizer> NaniteOptimizerRef;

    UPROPERTY()
    TWeakObjectPtr<class APCGWorldPartitionManager> WorldPartitionManagerRef;

    // Internal functions
    void InitializeLumenSystem();
    void UpdateLumenSystem(float DeltaTime);
    void UpdatePerformanceStats();
    void ApplyQualitySettings();
    void OptimizeLighting();
    void UpdateGlobalIllumination();
    void UpdateReflections();
    void CleanupInvalidReferences();
    void ProcessAdaptiveQuality();

    // Rendering integration
    void RegisterRenderingCallbacks();
    void UnregisterRenderingCallbacks();
    void OnPreRender();
    void OnPostRender();

    // Event handlers
    UFUNCTION()
    void OnLightComponentDestroyed(UActorComponent* DestroyedComponent);

    UFUNCTION()
    void OnMeshComponentDestroyed(UActorComponent* DestroyedComponent);

    // Owner destruction handler (binds to AActor::OnDestroyed)
    UFUNCTION()
    void OnOwnerDestroyed(AActor* DestroyedActor);

    // Async operations
    void StartAsyncLumenUpdate();
    void CompleteAsyncLumenUpdate();

    // Memory management
    void OptimizeMemoryUsage();
    void ClearUnusedData();
};