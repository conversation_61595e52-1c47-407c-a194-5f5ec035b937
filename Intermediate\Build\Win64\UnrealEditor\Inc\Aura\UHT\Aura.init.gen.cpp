// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAura_init() {}
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature();
	AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_Aura;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_Aura()
	{
		if (!Z_Registration_Info_UPackage__Script_Aura.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnBridgeConfigured__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnCacheEntryEvicted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnCacheEntryLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnCacheError__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnCacheStatisticsUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnObjectiveConfigured__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPCGAssetGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPCGGenerationCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPCGGenerationFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPhaseChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPhysicsBodyDestroyed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintBroken__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPhysicsConstraintCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPhysicsEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPhysicsPerformanceUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnProgressUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnPropConfigured__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_Aura_OnWallConfigured__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/Aura",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x53693FF3,
				0xE1D06957,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_Aura.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_Aura.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_Aura(Z_Construct_UPackage__Script_Aura, TEXT("/Script/Aura"), Z_Registration_Info_UPackage__Script_Aura, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x53693FF3, 0xE1D06957));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
