#include "APCGCacheManager.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Misc/SecureHash.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Compression/OodleDataCompression.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "HAL/PlatformMemory.h"
#include "GenericPlatform/GenericPlatformMemory.h"
#include "Containers/LruCache.h"
#include "Json.h"

// Include PCG system headers
#include "APCGWorldPartitionManager.h"
#include "APCGNaniteOptimizer.h"
#include "APCGLumenIntegrator.h"
#include "APCGStreamingManager.h"
#include "UPCGPerformanceProfiler.h"

// Stats declarations - using STATGROUP_Game since STATGROUP_PCG is not available in UE5.6
DECLARE_STATS_GROUP(TEXT("PCG Cache"), STATGROUP_PCGCache, STATCAT_Advanced);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Store"), STAT_PCGCacheStore, STATGROUP_PCGCache);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Load"), STAT_PCGCacheLoad, STATGROUP_PCGCache);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Compression"), STAT_PCGCacheCompression, STATGROUP_PCGCache);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Eviction"), STAT_PCGCacheEviction, STATGROUP_PCGCache);
DECLARE_MEMORY_STAT(TEXT("PCG Cache Memory"), STAT_PCGCacheMemory, STATGROUP_PCGCache);

APCGCacheManager::APCGCacheManager()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickInterval = 1.0f; // Tick every second for maintenance
    
    // Initialize default configuration
    CacheConfig = FPCGCacheConfig();
    
    // Initialize statistics
    CurrentStatistics = FPCGCacheStatistics();
    
    // Set default state
    bIsInitialized = false;
    bIsEnabled = true;
    bMaintenanceMode = false;
    
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Constructor completed"));
}

void APCGCacheManager::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: BeginPlay started"));
    
    // Initialize cache system
    InitializeCache();
    
    // Start maintenance timer
    LastMaintenanceTime = GetWorld()->GetTimeSeconds();
    LastStatisticsUpdate = LastMaintenanceTime;
    
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: BeginPlay completed"));
}

void APCGCacheManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: EndPlay started"));
    
    // Shutdown cache system
    ShutdownCache();
    
    Super::EndPlay(EndPlayReason);
    
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: EndPlay completed"));
}

void APCGCacheManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsInitialized || !bIsEnabled)
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Perform periodic maintenance
    if (CurrentTime - LastMaintenanceTime >= CacheConfig.EvictionCheckInterval)
    {
        PerformMaintenance();
        LastMaintenanceTime = CurrentTime;
    }
    
    // Update statistics
    if (CurrentTime - LastStatisticsUpdate >= 5.0f) // Update every 5 seconds
    {
        UpdateCacheStatistics();
        LastStatisticsUpdate = CurrentTime;
        OnCacheStatisticsUpdated.Broadcast(CurrentStatistics);
    }
    
    // Process prefetch queue
    ProcessPrefetchQueue();
    
    // Update integrated systems
    UpdateIntegratedSystems();
}

// Initialization and Management
void APCGCacheManager::InitializeCache()
{
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Initializing cache system"));
    
    if (bCacheInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGCacheManager: Cache already initialized"));
        return;
    }
    
    // Initialize memory cache
    MemoryCache.Empty();
    CacheEntries.Empty();
    
    // Create disk cache directory
    if (CacheConfig.bEnableDiskCache)
    {
        FString FullDiskPath = FPaths::ProjectSavedDir() / CacheConfig.DiskCachePath;
        IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
        
        if (!PlatformFile.DirectoryExists(*FullDiskPath))
        {
            if (PlatformFile.CreateDirectoryTree(*FullDiskPath))
            {
                UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Created disk cache directory: %s"), *FullDiskPath);
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("APCGCacheManager: Failed to create disk cache directory: %s"), *FullDiskPath);
                CacheConfig.bEnableDiskCache = false;
            }
        }
    }
    
    // Initialize statistics
    ResetStatistics();
    
    // Set initialized state
    bCacheInitialized = true;
    bIsInitialized = true;
    
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Cache system initialized successfully"));
}

void APCGCacheManager::ShutdownCache()
{
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Shutting down cache system"));
    
    if (!bCacheInitialized)
    {
        return;
    }
    
    // Save important entries to disk
    if (CacheConfig.bEnableDiskCache)
    {
        for (const auto& Entry : CacheEntries)
        {
            if (Entry.Value.Priority >= EPCGCachePriority::High && Entry.Value.State == EPCGCacheState::Valid)
            {
                SaveToDisk(Entry.Key);
            }
        }
    }
    
    // Clear memory cache
    MemoryCache.Empty();
    CacheEntries.Empty();
    EvictionQueue.Empty();
    AccessFrequency.Empty();
    PrefetchQueue.Empty();
    
    // Wait for pending operations
    for (auto& Operation : PendingOperations)
    {
        if (Operation.Value.IsValid())
        {
            Operation.Value.Wait();
        }
    }
    PendingOperations.Empty();
    
    // Reset state
    bCacheInitialized = false;
    bIsInitialized = false;
    
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Cache system shutdown completed"));
}

void APCGCacheManager::EnableCache(bool bEnable)
{
    bIsEnabled = bEnable;
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Cache %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void APCGCacheManager::SetMaintenanceMode(bool bMaintenance)
{
    bMaintenanceMode = bMaintenance;
    bMaintenanceActive = bMaintenance;
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Maintenance mode %s"), bMaintenance ? TEXT("enabled") : TEXT("disabled"));
}

void APCGCacheManager::ClearCache(EPCGCacheStorageType StorageType)
{
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Clearing cache for storage type: %d"), (int32)StorageType);
    
    switch (StorageType)
    {
    case EPCGCacheStorageType::Memory:
        MemoryCache.Empty();
        break;
        
    case EPCGCacheStorageType::Disk:
        CleanupDiskCache();
        break;
        
    case EPCGCacheStorageType::Hybrid:
        MemoryCache.Empty();
        CleanupDiskCache();
        break;
        
    default:
        MemoryCache.Empty();
        CacheEntries.Empty();
        break;
    }
    
    // Reset statistics
    ResetStatistics();
}

void APCGCacheManager::OptimizeCache()
{
    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Optimizing cache"));
    
    // Optimize memory layout
    OptimizeMemoryLayout();
    
    // Compact disk cache
    if (CacheConfig.bEnableDiskCache)
    {
        CompactDiskCache();
    }
    
    // Evict expired entries
    EvictExpiredEntries();
    
    // Update statistics
    UpdateCacheStatistics();
}

// Cache Operations
bool APCGCacheManager::StoreData(const FString& Key, const TArray<uint8>& Data, EPCGCachePriority Priority)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheStore);
    
    if (!bIsInitialized || !bIsEnabled || bMaintenanceMode)
    {
        return false;
    }
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Storing data for key: %s"), *Key);
    
    // Check if we need to evict entries first
    int32 DataSizeMB = Data.Num() / (1024 * 1024);
    if (CurrentStatistics.MemoryUsedMB + DataSizeMB > CacheConfig.MaxMemoryCacheSizeMB)
    {
        ForceEviction(CacheConfig.MaxMemoryCacheSizeMB - DataSizeMB);
    }
    
    // Compress data if enabled
    TArray<uint8> StoredData = Data;
    if (CacheConfig.bEnableCompression)
    {
        StoredData = CompressDataInternal(Data, CacheConfig.CompressionLevel);
    }
    
    // Store in memory cache
    TSharedPtr<TArray<uint8>> CachedData = MakeShared<TArray<uint8>>(StoredData);
    MemoryCache.Add(Key, CachedData);
    
    // Create cache entry
    FPCGCacheEntry Entry;
    Entry.EntryKey = Key;
    Entry.State = EPCGCacheState::Valid;
    Entry.Priority = Priority;
    Entry.SizeInBytes = Data.Num();
    Entry.CompressedSizeInBytes = StoredData.Num();
    Entry.StorageType = EPCGCacheStorageType::Memory;
    Entry.ChecksumHash = GenerateChecksum(Data);
    Entry.CreationTime = FDateTime::Now();
    Entry.LastAccessTime = Entry.CreationTime;
    Entry.LastModificationTime = Entry.CreationTime;
    
    CacheEntries.Add(Key, Entry);
    
    // Update access frequency
    UpdateAccessFrequency(Key);
    
    // Save to disk if high priority
    if (Priority >= EPCGCachePriority::High && CacheConfig.bEnableDiskCache)
    {
        SaveToDisk(Key);
    }
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Data stored successfully for key: %s"), *Key);
    return true;
}

bool APCGCacheManager::LoadData(const FString& Key, TArray<uint8>& OutData)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheLoad);
    
    if (!bIsInitialized || !bIsEnabled)
    {
        return false;
    }
    
    TotalCacheRequests++;
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Loading data for key: %s"), *Key);
    
    // Check memory cache first
    if (TSharedPtr<TArray<uint8>>* CachedData = MemoryCache.Find(Key))
    {
        TotalCacheHits++;
        
        // Update access information
        if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
        {
            Entry->LastAccessTime = FDateTime::Now();
            Entry->AccessCount++;
        }
        
        UpdateAccessFrequency(Key);
        
        // Decompress if needed
        if (CacheConfig.bEnableCompression)
        {
            OutData = DecompressDataInternal(**CachedData);
        }
        else
        {
            OutData = **CachedData;
        }
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Data loaded from memory cache for key: %s"), *Key);
        return true;
    }
    
    // Try loading from disk
    if (CacheConfig.bEnableDiskCache && LoadFromDisk(Key))
    {
        // Retry memory cache after disk load
        if (TSharedPtr<TArray<uint8>>* CachedData = MemoryCache.Find(Key))
        {
            TotalCacheHits++;
            
            if (CacheConfig.bEnableCompression)
            {
                OutData = DecompressDataInternal(**CachedData);
            }
            else
            {
                OutData = **CachedData;
            }
            
            UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Data loaded from disk cache for key: %s"), *Key);
            return true;
        }
    }
    
    TotalCacheMisses++;
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Cache miss for key: %s"), *Key);
    return false;
}

bool APCGCacheManager::HasEntry(const FString& Key) const
{
    return CacheEntries.Contains(Key) && CacheEntries[Key].State == EPCGCacheState::Valid;
}

bool APCGCacheManager::RemoveEntry(const FString& Key)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Removing entry for key: %s"), *Key);
    
    bool bRemoved = false;
    
    // Remove from memory cache
    if (MemoryCache.Remove(Key) > 0)
    {
        bRemoved = true;
    }
    
    // Remove from cache entries
    if (CacheEntries.Remove(Key) > 0)
    {
        bRemoved = true;
    }
    
    // Remove from access frequency
    AccessFrequency.Remove(Key);
    
    // Remove from disk if exists
    if (CacheConfig.bEnableDiskCache)
    {
        FString DiskPath = GetDiskPath(Key);
        if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*DiskPath))
        {
            FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*DiskPath);
        }
    }
    
    return bRemoved;
}

void APCGCacheManager::InvalidateEntry(const FString& Key)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->State = EPCGCacheState::Invalid;
        NotifyDependents(Key);
        UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Entry invalidated for key: %s"), *Key);
    }
}

void APCGCacheManager::RefreshEntry(const FString& Key)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->LastModificationTime = FDateTime::Now();
        Entry->State = EPCGCacheState::Valid;
        UE_LOG(LogTemp, VeryVerbose, TEXT("APCGCacheManager: Entry refreshed for key: %s"), *Key);
    }
}

// Async Operations
void APCGCacheManager::LoadDataAsync(const FString& Key, const FOnCacheEntryLoaded& Callback)
{
    if (!bIsInitialized || !bIsEnabled)
    {
        FPCGCacheOperationResult Result;
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Cache not initialized or disabled");
        if (Callback.IsBound())
        {
            Callback.Broadcast(Key, Result);
        }
        return;
    }
    
    // Create async task
    TFuture<FPCGCacheOperationResult> Future = Async(EAsyncExecution::ThreadPool, [this, Key]() -> FPCGCacheOperationResult
    {
        FPCGCacheOperationResult Result;
        TArray<uint8> Data;
        
        double StartTime = FPlatformTime::Seconds();
        Result.bSuccess = LoadData(Key, Data);
        Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        Result.DataSizeBytes = Data.Num();
        Result.bFromCache = Result.bSuccess;
        
        if (!Result.bSuccess)
        {
            Result.ErrorMessage = TEXT("Failed to load data from cache");
        }
        
        return Result;
    });
    
    // Store future for tracking
    PendingOperations.Add(Key, MoveTemp(Future));
    
    // Set up completion callback
    Future.Then([this, Key, Callback](TFuture<FPCGCacheOperationResult> CompletedFuture)
    {
        FPCGCacheOperationResult Result = CompletedFuture.Get();
        if (Callback.IsBound())
        {
            Callback.Broadcast(Key, Result);
        }
        PendingOperations.Remove(Key);
    });
}

void APCGCacheManager::StoreDataAsync(const FString& Key, const TArray<uint8>& Data, EPCGCachePriority Priority)
{
    if (!bIsInitialized || !bIsEnabled)
    {
        return;
    }
    
    // Create async task
    Async(EAsyncExecution::ThreadPool, [this, Key, Data, Priority]()
    {
        StoreData(Key, Data, Priority);
    });
}

void APCGCacheManager::PrefetchData(const TArray<FString>& Keys)
{
    for (const FString& Key : Keys)
    {
        if (!HasEntry(Key))
        {
            PrefetchQueue.AddUnique(Key);
        }
    }
}

void APCGCacheManager::PrefetchByPattern(const FString& Pattern)
{
    // Simple pattern matching - in a real implementation, you'd use regex
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Key.Contains(Pattern))
        {
            PrefetchQueue.AddUnique(Entry.Key);
        }
    }
}

// Entry Management
FPCGCacheEntry APCGCacheManager::GetEntryInfo(const FString& Key) const
{
    if (const FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        return *Entry;
    }
    return FPCGCacheEntry();
}

TArray<FPCGCacheEntry> APCGCacheManager::GetAllEntries() const
{
    TArray<FPCGCacheEntry> Entries;
    for (const auto& Entry : CacheEntries)
    {
        Entries.Add(Entry.Value);
    }
    return Entries;
}

TArray<FPCGCacheEntry> APCGCacheManager::GetEntriesByPriority(EPCGCachePriority Priority) const
{
    TArray<FPCGCacheEntry> Entries;
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Value.Priority == Priority)
        {
            Entries.Add(Entry.Value);
        }
    }
    return Entries;
}

TArray<FPCGCacheEntry> APCGCacheManager::GetEntriesByType(const FString& Type) const
{
    TArray<FPCGCacheEntry> Entries;
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Value.EntryType == Type)
        {
            Entries.Add(Entry.Value);
        }
    }
    return Entries;
}

void APCGCacheManager::SetEntryPriority(const FString& Key, EPCGCachePriority Priority)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->Priority = Priority;
        Entry->LastModificationTime = FDateTime::Now();
    }
}

void APCGCacheManager::AddEntryDependency(const FString& Key, const FString& DependencyKey)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->Dependencies.AddUnique(DependencyKey);
    }
    
    if (FPCGCacheEntry* DependencyEntry = CacheEntries.Find(DependencyKey))
    {
        DependencyEntry->Dependents.AddUnique(Key);
    }
}

void APCGCacheManager::RemoveEntryDependency(const FString& Key, const FString& DependencyKey)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->Dependencies.Remove(DependencyKey);
    }
    
    if (FPCGCacheEntry* DependencyEntry = CacheEntries.Find(DependencyKey))
    {
        DependencyEntry->Dependents.Remove(Key);
    }
}

// Statistics and Monitoring
FPCGCacheStatistics APCGCacheManager::GetCacheStatistics() const
{
    return CurrentStatistics;
}

float APCGCacheManager::GetHitRatio() const
{
    if (TotalCacheRequests == 0)
    {
        return 0.0f;
    }
    return (float)TotalCacheHits / (float)TotalCacheRequests;
}

float APCGCacheManager::GetCacheHitRate() const
{
    if (CurrentStatistics.TotalRequests == 0)
    {
        return 0.0f;
    }
    return (float)CurrentStatistics.CacheHits / (float)CurrentStatistics.TotalRequests * 100.0f;
}

int32 APCGCacheManager::GetMemoryUsage() const
{
    int32 TotalSize = 0;
    for (const auto& Entry : MemoryCache)
    {
        if (Entry.Value.IsValid())
        {
            TotalSize += Entry.Value->Num();
        }
    }
    return TotalSize / (1024 * 1024); // Convert to MB
}

int32 APCGCacheManager::GetDiskUsage() const
{
    return GetDiskCacheSize() / (1024 * 1024); // Convert to MB
}

void APCGCacheManager::ResetStatistics()
{
    CurrentStatistics = FPCGCacheStatistics();
    TotalCacheRequests = 0;
    TotalCacheHits = 0;
    TotalCacheMisses = 0;
}

void APCGCacheManager::ExportStatistics(const FString& FilePath) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    
    JsonObject->SetNumberField(TEXT("TotalRequests"), CurrentStatistics.TotalRequests);
    JsonObject->SetNumberField(TEXT("CacheHits"), CurrentStatistics.CacheHits);
    JsonObject->SetNumberField(TEXT("CacheMisses"), CurrentStatistics.CacheMisses);
    JsonObject->SetNumberField(TEXT("HitRatio"), CurrentStatistics.HitRatio);
    JsonObject->SetNumberField(TEXT("MemoryUsedMB"), CurrentStatistics.MemoryUsedMB);
    JsonObject->SetNumberField(TEXT("DiskUsedMB"), CurrentStatistics.DiskUsedMB);
    
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    FFileHelper::SaveStringToFile(OutputString, *FilePath);
}

// Internal functions
void APCGCacheManager::UpdateCacheStatistics()
{
    CurrentStatistics.TotalRequests = TotalCacheRequests;
    CurrentStatistics.CacheHits = TotalCacheHits;
    CurrentStatistics.CacheMisses = TotalCacheMisses;
    CurrentStatistics.HitRatio = GetHitRatio();
    CurrentStatistics.MemoryUsedMB = GetMemoryUsage();
    CurrentStatistics.DiskUsedMB = GetDiskUsage();
    CurrentStatistics.MemoryEntryCount = MemoryCache.Num();
    CurrentStatistics.DiskEntryCount = CacheEntries.Num() - MemoryCache.Num();
    
    // Update memory stats
    SET_MEMORY_STAT(STAT_PCGCacheMemory, CurrentStatistics.MemoryUsedMB * 1024 * 1024);
}

void APCGCacheManager::PerformMaintenance()
{
    if (bMaintenanceActive)
    {
        return;
    }
    
    bMaintenanceActive = true;
    
    // Evict expired entries
    EvictExpiredEntries();
    
    // Process eviction queue
    ProcessEvictionQueue();
    
    // Check memory pressure
    if (CurrentStatistics.MemoryUsedMB > CacheConfig.MaxMemoryCacheSizeMB * CacheConfig.MemoryEvictionThreshold)
    {
        ForceEviction(CacheConfig.MaxMemoryCacheSizeMB * 0.7f); // Target 70% usage
    }
    
    bMaintenanceActive = false;
}

void APCGCacheManager::ProcessEvictionQueue()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheEviction);
    
    // Process items in eviction queue
    FString KeyToEvict;
    while (EvictionQueue.Dequeue(KeyToEvict))
    {
        RemoveEntry(KeyToEvict);
        CurrentStatistics.EvictedEntries++;
    }
}

void APCGCacheManager::UpdateAccessFrequency(const FString& Key)
{
    int32* Frequency = AccessFrequency.Find(Key);
    if (Frequency)
    {
        (*Frequency)++;
    }
    else
    {
        AccessFrequency.Add(Key, 1);
    }
}

bool APCGCacheManager::ShouldEvictEntry(const FPCGCacheEntry& Entry) const
{
    // Don't evict critical priority entries
    if (Entry.Priority == EPCGCachePriority::Critical)
    {
        return false;
    }
    
    // Check if entry is expired
    FTimespan TimeSinceAccess = FDateTime::Now() - Entry.LastAccessTime;
    if (TimeSinceAccess.GetTotalSeconds() > CacheConfig.EntryExpirationTime)
    {
        return true;
    }
    
    // Check access frequency for LFU policy
    if (CacheConfig.EvictionPolicy == EPCGCacheEvictionPolicy::LFU)
    {
        const int32* Frequency = AccessFrequency.Find(Entry.EntryKey);
        return Frequency && *Frequency < 5; // Arbitrary threshold
    }
    
    return false;
}

FString APCGCacheManager::GenerateChecksum(const TArray<uint8>& Data) const
{
    return FMD5::HashBytes(Data.GetData(), Data.Num());
}

bool APCGCacheManager::ValidateEntry(const FString& Key) const
{
    const FPCGCacheEntry* Entry = CacheEntries.Find(Key);
    if (!Entry)
    {
        return false;
    }
    
    // Validate checksum if available
    if (!Entry->ChecksumHash.IsEmpty())
    {
        TArray<uint8> Data;
        if (const_cast<APCGCacheManager*>(this)->LoadData(Key, Data))
        {
            FString CurrentChecksum = GenerateChecksum(Data);
            return CurrentChecksum == Entry->ChecksumHash;
        }
    }
    
    return Entry->State == EPCGCacheState::Valid;
}

void APCGCacheManager::NotifyDependents(const FString& Key)
{
    const FPCGCacheEntry* Entry = CacheEntries.Find(Key);
    if (!Entry)
    {
        return;
    }
    
    // Invalidate all dependent entries
    for (const FString& DependentKey : Entry->Dependents)
    {
        InvalidateEntry(DependentKey);
    }
}

void APCGCacheManager::ProcessPrefetchQueue()
{
    if (PrefetchQueue.Num() == 0)
    {
        return;
    }
    
    // Process a limited number of prefetch requests per tick
    int32 ProcessCount = FMath::Min(PrefetchQueue.Num(), 3);
    
    for (int32 i = 0; i < ProcessCount; i++)
    {
        FString Key = PrefetchQueue[0];
        PrefetchQueue.RemoveAt(0);
        
        // Try to load from disk if not in memory
        if (!MemoryCache.Contains(Key) && CacheConfig.bEnableDiskCache)
        {
            LoadFromDisk(Key);
        }
    }
}

// Compression helpers
TArray<uint8> APCGCacheManager::CompressDataInternal(const TArray<uint8>& Data, EPCGCacheCompressionLevel Level) const
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheCompression);
    
    // Simple compression using Unreal's built-in compression
    TArray<uint8> CompressedData;
    
    int32 CompressedSize = FCompression::CompressMemoryBound(NAME_Zlib, Data.Num());
    CompressedData.SetNumUninitialized(CompressedSize);
    
    bool bSuccess = FCompression::CompressMemory(NAME_Zlib, CompressedData.GetData(), CompressedSize, Data.GetData(), Data.Num());
    
    if (bSuccess)
    {
        CompressedData.SetNum(CompressedSize);
        return CompressedData;
    }
    
    return Data; // Return original data if compression failed
}

TArray<uint8> APCGCacheManager::DecompressDataInternal(const TArray<uint8>& CompressedData) const
{
    // For simplicity, assume we store the original size somewhere
    // In a real implementation, you'd store this metadata
    TArray<uint8> DecompressedData;
    
    // Estimate decompressed size (this is a simplification)
    int32 EstimatedSize = CompressedData.Num() * 4; // Assume 4:1 compression ratio
    DecompressedData.SetNumUninitialized(EstimatedSize);
    
    bool bSuccess = FCompression::UncompressMemory(NAME_Zlib, DecompressedData.GetData(), EstimatedSize, CompressedData.GetData(), CompressedData.Num());
    
    if (bSuccess)
    {
        DecompressedData.SetNum(EstimatedSize);
        return DecompressedData;
    }
    
    return CompressedData; // Return compressed data if decompression failed
}

// Disk I/O helpers
FString APCGCacheManager::GetDiskPath(const FString& Key) const
{
    FString SafeKey = Key.Replace(TEXT("/"), TEXT("_")).Replace(TEXT("\\"), TEXT("_"));
    return FPaths::ProjectSavedDir() / CacheConfig.DiskCachePath / (SafeKey + TEXT(".cache"));
}

bool APCGCacheManager::WriteToDisk(const FString& Key, const TArray<uint8>& Data)
{
    FString DiskPath = GetDiskPath(Key);
    return FFileHelper::SaveArrayToFile(Data, *DiskPath);
}

bool APCGCacheManager::ReadFromDisk(const FString& Key, TArray<uint8>& OutData)
{
    FString DiskPath = GetDiskPath(Key);
    return FFileHelper::LoadFileToArray(OutData, *DiskPath);
}

// Additional implementations for remaining functions...
void APCGCacheManager::ForceEviction(int32 TargetMemoryMB)
{
    // Implementation for forced eviction
}

void APCGCacheManager::EvictExpiredEntries()
{
    // Implementation for evicting expired entries
}

bool APCGCacheManager::SaveToDisk(const FString& Key)
{
    // Implementation for saving to disk
    return true;
}

bool APCGCacheManager::LoadFromDisk(const FString& Key)
{
    // Implementation for loading from disk
    return true;
}

void APCGCacheManager::CleanupDiskCache()
{
    // Implementation for disk cache cleanup
}

int64 APCGCacheManager::GetDiskCacheSize() const
{
    // Implementation for getting disk cache size
    return 0;
}

void APCGCacheManager::OptimizeMemoryLayout()
{
    // Implementation for memory layout optimization
}

void APCGCacheManager::CompactDiskCache()
{
    // Implementation for disk cache compaction
}

void APCGCacheManager::UpdateIntegratedSystems()
{
    // Implementation for updating integrated systems
}

// Integration functions
void APCGCacheManager::IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager)
{
    WorldPartitionManagerRef = WorldPartitionManager;
}

void APCGCacheManager::IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer)
{
    NaniteOptimizerRef = NaniteOptimizer;
}

void APCGCacheManager::IntegrateWithLumen(APCGLumenIntegrator* LumenIntegrator)
{
    LumenIntegratorRef = LumenIntegrator;
}

void APCGCacheManager::IntegrateWithStreaming(APCGStreamingManager* StreamingManager)
{
    StreamingManagerRef = StreamingManager;
}

void APCGCacheManager::IntegrateWithProfiler(UPCGPerformanceProfiler* PerformanceProfiler)
{
    PerformanceProfilerRef = PerformanceProfiler;
}

// Compression functions
TArray<uint8> APCGCacheManager::CompressData(const TArray<uint8>& Data, EPCGCacheCompressionLevel Level) const
{
    return CompressDataInternal(Data, Level);
}

TArray<uint8> APCGCacheManager::DecompressData(const TArray<uint8>& CompressedData) const
{
    return DecompressDataInternal(CompressedData);
}

float APCGCacheManager::GetCompressionRatio(const TArray<uint8>& OriginalData, const TArray<uint8>& CompressedData) const
{
    if (OriginalData.Num() == 0)
    {
        return 0.0f;
    }
    return (float)CompressedData.Num() / (float)OriginalData.Num();
}

// Eviction functions
void APCGCacheManager::EvictByPriority(EPCGCachePriority MaxPriority)
{
    // Implementation for priority-based eviction
}

void APCGCacheManager::EvictLeastRecentlyUsed(int32 Count)
{
    // Implementation for LRU eviction
}

void APCGCacheManager::EvictLeastFrequentlyUsed(int32 Count)
{
    // Implementation for LFU eviction
}

// Network functions
void APCGCacheManager::SyncWithNetworkCache()
{
    // Implementation for network cache sync
}

void APCGCacheManager::UploadToNetwork(const FString& Key)
{
    // Implementation for network upload
}

void APCGCacheManager::DownloadFromNetwork(const FString& Key)
{
    // Implementation for network download
}

void APCGCacheManager::SendNetworkRequest(const FString& Endpoint, const FString& Data)
{
    // Implementation for network requests
}

void APCGCacheManager::HandleNetworkResponse(const FString& Response)
{
    // Implementation for network response handling
}

// Event handlers
void APCGCacheManager::OnMemoryPressure()
{
    // Implementation for memory pressure handling
}

void APCGCacheManager::OnDiskSpaceLow()
{
    // Implementation for low disk space handling
}

void APCGCacheManager::OnNetworkError(const FString& Error)
{
    // Implementation for network error handling
}

EPCGCachePriority APCGCacheManager::CalculateAdaptivePriority(const FPCGCacheEntry& Entry) const
{
    // Implementation for adaptive priority calculation
    return EPCGCachePriority::Medium;
}

// Implementações das funções adicionais para validação de qualidade
int32 APCGCacheManager::GetFragmentedEntryCount() const
{
    int32 FragmentedCount = 0;
    for (const auto& Entry : CacheEntries)
    {
        // Considerar entrada fragmentada se foi acessada há muito tempo
        if (Entry.Value.LastAccessTime < FDateTime::Now() - FTimespan::FromHours(24))
        {
            FragmentedCount++;
        }
    }
    return FragmentedCount;
}

int32 APCGCacheManager::GetTotalEntryCount() const
{
    return MemoryCache.Num() + CacheEntries.Num();
}

void APCGCacheManager::ClearUnusedEntries()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheEviction);

    TArray<FString> KeysToRemove;
    FDateTime ThresholdTime = FDateTime::Now() - FTimespan::FromHours(1); // 1 hora sem uso

    // Identificar entradas não utilizadas
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Value.LastAccessTime < ThresholdTime && Entry.Value.AccessCount == 0)
        {
            KeysToRemove.Add(Entry.Key);
        }
    }

    // Remover entradas identificadas
    for (const FString& Key : KeysToRemove)
    {
        RemoveEntry(Key);
    }

    UE_LOG(LogTemp, Log, TEXT("APCGCacheManager: Cleared %d unused entries"), KeysToRemove.Num());
}