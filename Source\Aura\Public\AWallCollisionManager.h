#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "AWallCollisionManager.generated.h"

// Constantes para dimensões das paredes (1 UU = 1 cm)
static constexpr float WALL_HEIGHT = 1000.0f;           // 10 metros de altura
static constexpr float WALL_THICKNESS = 50.0f;          // 50 cm de espessura
static constexpr float CORNER_RADIUS = 200.0f;          // Raio dos cantos arredondados
static constexpr float MAP_BOUNDARY_SIZE = 15000.0f;    // 150 metros (tamanho do mapa)
static constexpr float GATE_WIDTH = 400.0f;             // 4 metros de largura dos portões
static constexpr int32 WALL_SEGMENTS = 32;              // Segmentos para paredes curvas
static constexpr float COLLISION_TOLERANCE = 10.0f;     // Tolerância para colisões

// Enums para tipos de parede
UENUM(BlueprintType)
enum class EWallType : uint8
{
    Straight    UMETA(DisplayName = "Straight Wall"),
    Corner      UMETA(DisplayName = "Corner Wall"),
    Gate        UMETA(DisplayName = "Gate Wall"),
    Curved      UMETA(DisplayName = "Curved Wall")
};

// Enum para orientação da parede
UENUM(BlueprintType)
enum class EWallOrientation : uint8
{
    North       UMETA(DisplayName = "North"),
    South       UMETA(DisplayName = "South"),
    East        UMETA(DisplayName = "East"),
    West        UMETA(DisplayName = "West"),
    NorthEast   UMETA(DisplayName = "North East"),
    NorthWest   UMETA(DisplayName = "North West"),
    SouthEast   UMETA(DisplayName = "South East"),
    SouthWest   UMETA(DisplayName = "South West")
};

// Enum para material da parede
UENUM(BlueprintType)
enum class EWallMaterial : uint8
{
    Stone       UMETA(DisplayName = "Stone"),
    Metal       UMETA(DisplayName = "Metal"),
    Wood        UMETA(DisplayName = "Wood"),
    Crystal     UMETA(DisplayName = "Crystal"),
    Magical     UMETA(DisplayName = "Magical")
};

// Estrutura para dados de uma seção de parede
USTRUCT(BlueprintType)
struct FWallSection
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    FVector StartPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    FVector EndPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    EWallType WallType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    EWallOrientation Orientation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    float Height;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    float Thickness;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    bool bHasGate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    FVector GatePosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    float GateWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall Section")
    TArray<FVector> SegmentPoints;

    FWallSection()
    {
        StartPosition = FVector::ZeroVector;
        EndPosition = FVector::ZeroVector;
        WallType = EWallType::Straight;
        Orientation = EWallOrientation::North;
        Height = WALL_HEIGHT;
        Thickness = WALL_THICKNESS;
        bHasGate = false;
        GatePosition = FVector::ZeroVector;
        GateWidth = GATE_WIDTH;
        SegmentPoints.Empty();
    }

    // Operador de comparação para uso em TArray::Find
    bool operator==(const FWallSection& Other) const
    {
        return StartPosition.Equals(Other.StartPosition, 1.0f) &&
               EndPosition.Equals(Other.EndPosition, 1.0f) &&
               WallType == Other.WallType &&
               Orientation == Other.Orientation &&
               FMath::IsNearlyEqual(Height, Other.Height, 1.0f) &&
               FMath::IsNearlyEqual(Thickness, Other.Thickness, 1.0f) &&
               bHasGate == Other.bHasGate;
    }
};

// Estrutura para dados de colisão
USTRUCT(BlueprintType)
struct FWallCollisionData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FVector CollisionPoint;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FVector CollisionNormal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    float CollisionDistance;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    AActor* CollidingActor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FDateTime CollisionTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision")
    FVector ImpactVelocity;

    FWallCollisionData()
    {
        CollisionPoint = FVector::ZeroVector;
        CollisionNormal = FVector::ZeroVector;
        CollisionDistance = 0.0f;
        CollidingActor = nullptr;
        CollisionTime = FDateTime::Now();
        ImpactVelocity = FVector::ZeroVector;
    }
};

// Estrutura para configuração de material
USTRUCT(BlueprintType)
struct FWallMaterialConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    EWallMaterial MaterialType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UMaterialInterface* Material;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Durability;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float ReflectionCoefficient;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    bool bIsDestructible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FLinearColor EmissiveColor;

    FWallMaterialConfig()
    {
        MaterialType = EWallMaterial::Stone;
        Material = nullptr;
        Durability = 1000.0f;
        ReflectionCoefficient = 0.8f;
        bIsDestructible = false;
        EmissiveColor = FLinearColor::Black;
    }
};

// Estrutura para dados de portão
USTRUCT(BlueprintType)
struct FGateData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gate")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gate")
    EWallOrientation Orientation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gate")
    float Width;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gate")
    float Height;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gate")
    bool bIsOpen;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gate")
    bool bIsLocked;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gate")
    TArray<AActor*> AuthorizedActors;

    FGateData()
    {
        Position = FVector::ZeroVector;
        Orientation = EWallOrientation::North;
        Width = GATE_WIDTH;
        Height = WALL_HEIGHT;
        bIsOpen = false;
        bIsLocked = true;
        AuthorizedActors.Empty();
    }
};

// Estrutura para área de patrulha
USTRUCT(BlueprintType)
struct FPatrolArea
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
    TArray<FVector> PatrolPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
    float PatrolRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
    bool bIsCircular;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
    float PatrolSpeed;

    FPatrolArea()
    {
        PatrolPoints.Empty();
        PatrolRadius = 500.0f;
        bIsCircular = true;
        PatrolSpeed = 300.0f;
    }
};

UCLASS()
class AURA_API AWallCollisionManager : public AActor
{
    GENERATED_BODY()

public:
    AWallCollisionManager();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Componentes principais
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<UStaticMeshComponent*> WallMeshes;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<UBoxComponent*> CollisionBoxes;

    // Dados das paredes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall System")
    TArray<FWallSection> WallSections;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall System")
    TArray<FGateData> Gates;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall System")
    FWallMaterialConfig MaterialConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wall System")
    FPatrolArea PatrolArea;

    // Configurações de geometria
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float MapBoundarySize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float WallHeight;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float WallThickness;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float CornerRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    int32 WallSegments;

    // Sistema de colisões
    UPROPERTY(BlueprintReadOnly, Category = "Collision System")
    TArray<FWallCollisionData> RecentCollisions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision System")
    float CollisionTolerance;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision System")
    bool bEnableCollisionLogging;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision System")
    int32 MaxCollisionHistory;

    // Funções principais de inicialização
    UFUNCTION(BlueprintCallable, Category = "Wall System")
    void InitializeWallSystem();

    UFUNCTION(BlueprintCallable, Category = "Wall System")
    void GenerateWallGeometry();

    UFUNCTION(BlueprintCallable, Category = "Wall System")
    void SetupCollisionSystem();

    // Funções de geometria das paredes
    UFUNCTION(BlueprintCallable, Category = "Geometry")
    void CalculateWallBoundaries();

    UFUNCTION(BlueprintCallable, Category = "Geometry")
    TArray<FVector> GenerateStraightWallPoints(const FVector& StartPos, const FVector& EndPos) const;

    UFUNCTION(BlueprintCallable, Category = "Geometry")
    TArray<FVector> GenerateCornerWallPoints(const FVector& CornerPos, EWallOrientation Orientation) const;

    UFUNCTION(BlueprintCallable, Category = "Geometry")
    TArray<FVector> GenerateCurvedWallPoints(const FVector& StartPos, const FVector& EndPos, float CurveRadius) const;

    UFUNCTION(BlueprintCallable, Category = "Geometry")
    FVector CalculateWallNormal(const FVector& StartPos, const FVector& EndPos) const;

    UFUNCTION(BlueprintCallable, Category = "Geometry")
    float CalculateWallLength(const FWallSection& WallSection) const;

    UFUNCTION(BlueprintCallable, Category = "Geometry")
    FVector GetWallCenter(const FWallSection& WallSection) const;

    // Funções de criação de meshes
    UFUNCTION(BlueprintCallable, Category = "Mesh Generation")
    void CreateWallMeshes();

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation")
    UStaticMeshComponent* CreateWallSegment(const FWallSection& WallSection, int32 SegmentIndex);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation")
    void ApplyWallMaterial(UStaticMeshComponent* WallMesh, const FWallMaterialConfig& InMaterialConfig);

    UFUNCTION(BlueprintCallable, Category = "Mesh Generation")
    void UpdateWallMeshes();

    // Sistema de colisões
    UFUNCTION(BlueprintCallable, Category = "Collision")
    bool CheckCollisionWithWalls(const FVector& StartPos, const FVector& EndPos, FWallCollisionData& OutCollisionData) const;

    UFUNCTION(BlueprintCallable, Category = "Collision")
    bool IsPositionInsideWalls(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Collision")
    FVector GetClosestPointOnWalls(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Collision")
    float GetDistanceToNearestWall(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Collision")
    TArray<FVector> GetWallIntersectionPoints(const FVector& LineStart, const FVector& LineEnd) const;

    UFUNCTION(BlueprintCallable, Category = "Collision")
    bool LineIntersectsWall(const FVector& LineStart, const FVector& LineEnd, const FWallSection& WallSection, FVector& OutIntersectionPoint) const;

    // Sistema de portões
    UFUNCTION(BlueprintCallable, Category = "Gates")
    void CreateGates();

    UFUNCTION(BlueprintCallable, Category = "Gates")
    void OpenGate(int32 GateIndex);

    UFUNCTION(BlueprintCallable, Category = "Gates")
    void CloseGate(int32 GateIndex);

    UFUNCTION(BlueprintCallable, Category = "Gates")
    bool IsGateOpen(int32 GateIndex) const;

    UFUNCTION(BlueprintCallable, Category = "Gates")
    bool CanActorPassThroughGate(AActor* Actor, int32 GateIndex) const;

    UFUNCTION(BlueprintCallable, Category = "Gates")
    void AuthorizeActorForGate(AActor* Actor, int32 GateIndex);

    UFUNCTION(BlueprintCallable, Category = "Gates")
    FVector GetNearestGatePosition(const FVector& Position) const;

    // Funções de pathfinding e navegação
    UFUNCTION(BlueprintCallable, Category = "Navigation")
    TArray<FVector> FindPathAroundWalls(const FVector& StartPos, const FVector& EndPos) const;

    UFUNCTION(BlueprintCallable, Category = "Navigation")
    bool IsPathClearOfWalls(const FVector& StartPos, const FVector& EndPos) const;

    UFUNCTION(BlueprintCallable, Category = "Navigation")
    FVector GetSafePositionNearWalls(const FVector& DesiredPosition, float SafeDistance) const;

    UFUNCTION(BlueprintCallable, Category = "Navigation")
    TArray<FVector> GeneratePatrolPath() const;

    // Funções de validação e debug
    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateWallGeometry() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateCollisionSetup() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugWalls() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugCollisions() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugGates() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void DrawDebugPatrolPath() const;

    // Eventos de colisão
    UFUNCTION()
    void OnWallHit(UPrimitiveComponent* HitComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, FVector NormalImpulse, const FHitResult& Hit);

    UFUNCTION()
    void OnActorBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnActorEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex);

    // Funções de utilidade matemática
    UFUNCTION(BlueprintCallable, Category = "Math Utils")
    FVector RotatePointAroundCenter(const FVector& Point, const FVector& Center, float AngleDegrees) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utils")
    float CalculateAngleBetweenVectors(const FVector& Vector1, const FVector& Vector2) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utils")
    bool IsPointInsidePolygon(const FVector& Point, const TArray<FVector>& PolygonVertices) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utils")
    FVector ProjectPointOntoLine(const FVector& Point, const FVector& LineStart, const FVector& LineEnd) const;

    UFUNCTION(BlueprintCallable, Category = "Math Utils")
    float DistancePointToLine(const FVector& Point, const FVector& LineStart, const FVector& LineEnd) const;

    // Getters e Setters
    UFUNCTION(BlueprintCallable, Category = "Getters")
    TArray<FWallSection> GetWallSections() const { return WallSections; }

    UFUNCTION(BlueprintCallable, Category = "Getters")
    TArray<FGateData> GetGates() const { return Gates; }

    UFUNCTION(BlueprintCallable, Category = "Getters")
    FWallMaterialConfig GetMaterialConfig() const { return MaterialConfig; }

    UFUNCTION(BlueprintCallable, Category = "Getters")
    float GetTotalWallLength() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    float GetWallPerimeter() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    FVector GetMapCenter() const;

    UFUNCTION(BlueprintCallable, Category = "Getters")
    float GetMapArea() const;

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetWallMaterial(const FWallMaterialConfig& NewMaterialConfig);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetWallHeight(float NewHeight);

    UFUNCTION(BlueprintCallable, Category = "Setters")
    void SetWallThickness(float NewThickness);

private:
    // Funções auxiliares internas
    void InitializeDefaultWallSections();
    void InitializeDefaultGates();
    void SetupCollisionCallbacks();
    void UpdateCollisionHistory(const FWallCollisionData& CollisionData);
    void CleanupOldCollisions();
    FVector CalculateCornerPosition(EWallOrientation Orientation, float Radius) const;
    TArray<FVector> InterpolateWallPoints(const TArray<FVector>& ControlPoints, int32 Segments) const;
    bool ValidateWallSection(const FWallSection& WallSection) const;
    void LogCollisionEvent(const FWallCollisionData& CollisionData) const;

    // Variáveis internas
    float LastUpdateTime;
    bool bIsInitialized;
    TMap<AActor*, float> ActorDistanceCache;
    FTimerHandle CollisionCleanupTimer;
};