// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "APCGWorldPartitionManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAPCGWorldPartitionManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGDataLayerType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGStreamingPriority();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGWorldPartitionState();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGDataLayerConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingCell();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGWorldPartitionConfig();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UDataLayerManager_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldPartitionSubsystem_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSubsystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGWorldPartitionState ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGWorldPartitionState;
static UEnum* EPCGWorldPartitionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGWorldPartitionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGWorldPartitionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGWorldPartitionState, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGWorldPartitionState"));
	}
	return Z_Registration_Info_UEnum_EPCGWorldPartitionState.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGWorldPartitionState>()
{
	return EPCGWorldPartitionState_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGWorldPartitionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EPCGWorldPartitionState::Active" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for World Partition Management\n" },
#endif
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EPCGWorldPartitionState::Error" },
		{ "Initializing.DisplayName", "Initializing" },
		{ "Initializing.Name", "EPCGWorldPartitionState::Initializing" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
		{ "Paused.DisplayName", "Paused" },
		{ "Paused.Name", "EPCGWorldPartitionState::Paused" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EPCGWorldPartitionState::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for World Partition Management" },
#endif
		{ "Uninitialized.DisplayName", "Uninitialized" },
		{ "Uninitialized.Name", "EPCGWorldPartitionState::Uninitialized" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGWorldPartitionState::Uninitialized", (int64)EPCGWorldPartitionState::Uninitialized },
		{ "EPCGWorldPartitionState::Initializing", (int64)EPCGWorldPartitionState::Initializing },
		{ "EPCGWorldPartitionState::Active", (int64)EPCGWorldPartitionState::Active },
		{ "EPCGWorldPartitionState::Streaming", (int64)EPCGWorldPartitionState::Streaming },
		{ "EPCGWorldPartitionState::Paused", (int64)EPCGWorldPartitionState::Paused },
		{ "EPCGWorldPartitionState::Error", (int64)EPCGWorldPartitionState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGWorldPartitionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGWorldPartitionState",
	"EPCGWorldPartitionState",
	Z_Construct_UEnum_Aura_EPCGWorldPartitionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGWorldPartitionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGWorldPartitionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGWorldPartitionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGWorldPartitionState()
{
	if (!Z_Registration_Info_UEnum_EPCGWorldPartitionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGWorldPartitionState.InnerSingleton, Z_Construct_UEnum_Aura_EPCGWorldPartitionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGWorldPartitionState.InnerSingleton;
}
// ********** End Enum EPCGWorldPartitionState *****************************************************

// ********** Begin Enum EPCGStreamingPriority *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGStreamingPriority;
static UEnum* EPCGStreamingPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGStreamingPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGStreamingPriority, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGStreamingPriority"));
	}
	return Z_Registration_Info_UEnum_EPCGStreamingPriority.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGStreamingPriority>()
{
	return EPCGStreamingPriority_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGStreamingPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Critical.DisplayName", "Critical Priority" },
		{ "Critical.Name", "EPCGStreamingPriority::Critical" },
		{ "High.DisplayName", "High Priority" },
		{ "High.Name", "EPCGStreamingPriority::High" },
		{ "Low.DisplayName", "Low Priority" },
		{ "Low.Name", "EPCGStreamingPriority::Low" },
		{ "Medium.DisplayName", "Medium Priority" },
		{ "Medium.Name", "EPCGStreamingPriority::Medium" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGStreamingPriority::Low", (int64)EPCGStreamingPriority::Low },
		{ "EPCGStreamingPriority::Medium", (int64)EPCGStreamingPriority::Medium },
		{ "EPCGStreamingPriority::High", (int64)EPCGStreamingPriority::High },
		{ "EPCGStreamingPriority::Critical", (int64)EPCGStreamingPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGStreamingPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGStreamingPriority",
	"EPCGStreamingPriority",
	Z_Construct_UEnum_Aura_EPCGStreamingPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGStreamingPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGStreamingPriority()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGStreamingPriority.InnerSingleton, Z_Construct_UEnum_Aura_EPCGStreamingPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGStreamingPriority.InnerSingleton;
}
// ********** End Enum EPCGStreamingPriority *******************************************************

// ********** Begin Enum EPCGDataLayerType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGDataLayerType;
static UEnum* EPCGDataLayerType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGDataLayerType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGDataLayerType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGDataLayerType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGDataLayerType"));
	}
	return Z_Registration_Info_UEnum_EPCGDataLayerType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGDataLayerType>()
{
	return EPCGDataLayerType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGDataLayerType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Audio.DisplayName", "Audio Layer" },
		{ "Audio.Name", "EPCGDataLayerType::Audio" },
		{ "BlueprintType", "true" },
		{ "Interactive.DisplayName", "Interactive Layer" },
		{ "Interactive.Name", "EPCGDataLayerType::Interactive" },
		{ "Lighting.DisplayName", "Lighting Layer" },
		{ "Lighting.Name", "EPCGDataLayerType::Lighting" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
		{ "Structures.DisplayName", "Structures Layer" },
		{ "Structures.Name", "EPCGDataLayerType::Structures" },
		{ "Terrain.DisplayName", "Terrain Layer" },
		{ "Terrain.Name", "EPCGDataLayerType::Terrain" },
		{ "Vegetation.DisplayName", "Vegetation Layer" },
		{ "Vegetation.Name", "EPCGDataLayerType::Vegetation" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGDataLayerType::Terrain", (int64)EPCGDataLayerType::Terrain },
		{ "EPCGDataLayerType::Vegetation", (int64)EPCGDataLayerType::Vegetation },
		{ "EPCGDataLayerType::Structures", (int64)EPCGDataLayerType::Structures },
		{ "EPCGDataLayerType::Interactive", (int64)EPCGDataLayerType::Interactive },
		{ "EPCGDataLayerType::Lighting", (int64)EPCGDataLayerType::Lighting },
		{ "EPCGDataLayerType::Audio", (int64)EPCGDataLayerType::Audio },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGDataLayerType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGDataLayerType",
	"EPCGDataLayerType",
	Z_Construct_UEnum_Aura_EPCGDataLayerType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGDataLayerType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGDataLayerType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGDataLayerType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGDataLayerType()
{
	if (!Z_Registration_Info_UEnum_EPCGDataLayerType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGDataLayerType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGDataLayerType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGDataLayerType.InnerSingleton;
}
// ********** End Enum EPCGDataLayerType ***********************************************************

// ********** Begin ScriptStruct FPCGWorldPartitionConfig ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig;
class UScriptStruct* FPCGWorldPartitionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGWorldPartitionConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGWorldPartitionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures for World Partition Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures for World Partition Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridSize_MetaData[] = {
		{ "Category", "World Partition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid size for world partition cells (in UE units, 1 UU = 1 cm)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid size for world partition cells (in UE units, 1 UU = 1 cm)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingRadius_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Loading radius around player (in UE units)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading radius around player (in UE units)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadingRadius_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unloading radius (should be larger than loading radius)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unloading radius (should be larger than loading radius)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentStreams_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Maximum number of concurrent streaming operations\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum number of concurrent streaming operations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHLOD_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enable hierarchical LOD for distant cells\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable hierarchical LOD for distant cells" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HLODTransitionDistance_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// HLOD transition distance\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HLOD transition distance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadingRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentStreams;
	static void NewProp_bEnableHLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HLODTransitionDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGWorldPartitionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_GridSize = { "GridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGWorldPartitionConfig, GridSize), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridSize_MetaData), NewProp_GridSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_LoadingRadius = { "LoadingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGWorldPartitionConfig, LoadingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingRadius_MetaData), NewProp_LoadingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_UnloadingRadius = { "UnloadingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGWorldPartitionConfig, UnloadingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadingRadius_MetaData), NewProp_UnloadingRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_MaxConcurrentStreams = { "MaxConcurrentStreams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGWorldPartitionConfig, MaxConcurrentStreams), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentStreams_MetaData), NewProp_MaxConcurrentStreams_MetaData) };
void Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_bEnableHLOD_SetBit(void* Obj)
{
	((FPCGWorldPartitionConfig*)Obj)->bEnableHLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_bEnableHLOD = { "bEnableHLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGWorldPartitionConfig), &Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_bEnableHLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHLOD_MetaData), NewProp_bEnableHLOD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_HLODTransitionDistance = { "HLODTransitionDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGWorldPartitionConfig, HLODTransitionDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HLODTransitionDistance_MetaData), NewProp_HLODTransitionDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_GridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_LoadingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_UnloadingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_MaxConcurrentStreams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_bEnableHLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewProp_HLODTransitionDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGWorldPartitionConfig",
	Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::PropPointers),
	sizeof(FPCGWorldPartitionConfig),
	alignof(FPCGWorldPartitionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGWorldPartitionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGWorldPartitionConfig ********************************************

// ********** Begin ScriptStruct FPCGStreamingCell *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGStreamingCell;
class UScriptStruct* FPCGStreamingCell::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingCell.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGStreamingCell.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGStreamingCell, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGStreamingCell"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingCell.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGStreamingCell_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "Category", "Cell" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell coordinates in the world partition grid\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell coordinates in the world partition grid" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldBounds_MetaData[] = {
		{ "Category", "Cell" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World bounds of this cell\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World bounds of this cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponents_MetaData[] = {
		{ "Category", "PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG components in this cell\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG components in this cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayers_MetaData[] = {
		{ "Category", "Data Layers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layers associated with this cell\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layers associated with this cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPriority_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming priority for this cell\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming priority for this cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLoaded_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Is this cell currently loaded?\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is this cell currently loaded?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerating_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Is this cell currently generating PCG content?\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is this cell currently generating PCG content?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationProgress_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Generation progress (0.0 to 1.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation progress (0.0 to 1.0)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldBounds;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_PCGComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PCGComponents;
	static const UECodeGen_Private::FNamePropertyParams NewProp_DataLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DataLayers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingPriority;
	static void NewProp_bIsLoaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoaded;
	static void NewProp_bIsGenerating_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerating;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationProgress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGStreamingCell>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingCell, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_WorldBounds = { "WorldBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingCell, WorldBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldBounds_MetaData), NewProp_WorldBounds_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_PCGComponents_Inner = { "PCGComponents", nullptr, (EPropertyFlags)0x0004000000080008, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_PCGComponents = { "PCGComponents", nullptr, (EPropertyFlags)0x0014008000000009, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingCell, PCGComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponents_MetaData), NewProp_PCGComponents_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_DataLayers_Inner = { "DataLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_DataLayers = { "DataLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingCell, DataLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayers_MetaData), NewProp_DataLayers_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_StreamingPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_StreamingPriority = { "StreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingCell, StreamingPriority), Z_Construct_UEnum_Aura_EPCGStreamingPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPriority_MetaData), NewProp_StreamingPriority_MetaData) }; // 3992031022
void Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsLoaded_SetBit(void* Obj)
{
	((FPCGStreamingCell*)Obj)->bIsLoaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsLoaded = { "bIsLoaded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingCell), &Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsLoaded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLoaded_MetaData), NewProp_bIsLoaded_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsGenerating_SetBit(void* Obj)
{
	((FPCGStreamingCell*)Obj)->bIsGenerating = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsGenerating = { "bIsGenerating", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingCell), &Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsGenerating_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerating_MetaData), NewProp_bIsGenerating_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_GenerationProgress = { "GenerationProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingCell, GenerationProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationProgress_MetaData), NewProp_GenerationProgress_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_CellCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_WorldBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_PCGComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_PCGComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_DataLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_DataLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_StreamingPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_StreamingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_bIsGenerating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewProp_GenerationProgress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGStreamingCell",
	Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::PropPointers),
	sizeof(FPCGStreamingCell),
	alignof(FPCGStreamingCell),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingCell()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingCell.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGStreamingCell.InnerSingleton, Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingCell.InnerSingleton;
}
// ********** End ScriptStruct FPCGStreamingCell ***************************************************

// ********** Begin ScriptStruct FPCGDataLayerConfig ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig;
class UScriptStruct* FPCGDataLayerConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGDataLayerConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGDataLayerConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "Category", "Data Layer" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer name\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerType_MetaData[] = {
		{ "Category", "Data Layer" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data layer type\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data layer type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraph_MetaData[] = {
		{ "Category", "PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG graph to use for this layer\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG graph to use for this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLoadByDefault_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Should this layer be loaded by default?\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Should this layer be loaded by default?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPriority_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming priority for this layer\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming priority for this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryBudgetMB_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory budget for this layer (in MB)\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory budget for this layer (in MB)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PCGGraph;
	static void NewProp_bLoadByDefault_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLoadByDefault;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryBudgetMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGDataLayerConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGDataLayerConfig, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_LayerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_LayerType = { "LayerType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGDataLayerConfig, LayerType), Z_Construct_UEnum_Aura_EPCGDataLayerType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerType_MetaData), NewProp_LayerType_MetaData) }; // 2038321418
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_PCGGraph = { "PCGGraph", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGDataLayerConfig, PCGGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraph_MetaData), NewProp_PCGGraph_MetaData) };
void Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_bLoadByDefault_SetBit(void* Obj)
{
	((FPCGDataLayerConfig*)Obj)->bLoadByDefault = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_bLoadByDefault = { "bLoadByDefault", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGDataLayerConfig), &Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_bLoadByDefault_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLoadByDefault_MetaData), NewProp_bLoadByDefault_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_StreamingPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_StreamingPriority = { "StreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGDataLayerConfig, StreamingPriority), Z_Construct_UEnum_Aura_EPCGStreamingPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPriority_MetaData), NewProp_StreamingPriority_MetaData) }; // 3992031022
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_MemoryBudgetMB = { "MemoryBudgetMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGDataLayerConfig, MemoryBudgetMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryBudgetMB_MetaData), NewProp_MemoryBudgetMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_LayerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_LayerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_PCGGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_bLoadByDefault,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_StreamingPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_StreamingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewProp_MemoryBudgetMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGDataLayerConfig",
	Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::PropPointers),
	sizeof(FPCGDataLayerConfig),
	alignof(FPCGDataLayerConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGDataLayerConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGDataLayerConfig *************************************************

// ********** Begin Class APCGWorldPartitionManager Function AddStreamingSource ********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics
{
	struct PCGWorldPartitionManager_eventAddStreamingSource_Parms
	{
		FVector WorldLocation;
		float Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming" },
		{ "CPP_Default_Priority", "1.000000" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventAddStreamingSource_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventAddStreamingSource_Parms, Priority), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "AddStreamingSource", Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::PCGWorldPartitionManager_eventAddStreamingSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::PCGWorldPartitionManager_eventAddStreamingSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execAddStreamingSource)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddStreamingSource(Z_Param_Out_WorldLocation,Z_Param_Priority);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function AddStreamingSource **********************

// ********** Begin Class APCGWorldPartitionManager Function ClearStreamingSources *****************
struct Z_Construct_UFunction_APCGWorldPartitionManager_ClearStreamingSources_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_ClearStreamingSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "ClearStreamingSources", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_ClearStreamingSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_ClearStreamingSources_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_ClearStreamingSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_ClearStreamingSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execClearStreamingSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearStreamingSources();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function ClearStreamingSources *******************

// ********** Begin Class APCGWorldPartitionManager Function CreateDataLayer ***********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics
{
	struct PCGWorldPartitionManager_eventCreateDataLayer_Parms
	{
		FPCGDataLayerConfig LayerConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Data Layer Management ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Data Layer Management ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::NewProp_LayerConfig = { "LayerConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventCreateDataLayer_Parms, LayerConfig), Z_Construct_UScriptStruct_FPCGDataLayerConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerConfig_MetaData), NewProp_LayerConfig_MetaData) }; // 4011349172
void Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventCreateDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventCreateDataLayer_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::NewProp_LayerConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "CreateDataLayer", Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::PCGWorldPartitionManager_eventCreateDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::PCGWorldPartitionManager_eventCreateDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execCreateDataLayer)
{
	P_GET_STRUCT_REF(FPCGDataLayerConfig,Z_Param_Out_LayerConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateDataLayer(Z_Param_Out_LayerConfig);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function CreateDataLayer *************************

// ********** Begin Class APCGWorldPartitionManager Function CreateStreamingCell *******************
struct Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics
{
	struct PCGWorldPartitionManager_eventCreateStreamingCell_Parms
	{
		FIntPoint CellCoordinates;
		FPCGStreamingCell CellConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Cells" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Cell Management Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Cell Management Functions ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventCreateStreamingCell_Parms, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_CellConfig = { "CellConfig", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventCreateStreamingCell_Parms, CellConfig), Z_Construct_UScriptStruct_FPCGStreamingCell, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellConfig_MetaData), NewProp_CellConfig_MetaData) }; // 3906788436
void Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventCreateStreamingCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventCreateStreamingCell_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_CellCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_CellConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "CreateStreamingCell", Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::PCGWorldPartitionManager_eventCreateStreamingCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::PCGWorldPartitionManager_eventCreateStreamingCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execCreateStreamingCell)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_CellCoordinates);
	P_GET_STRUCT_REF(FPCGStreamingCell,Z_Param_Out_CellConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateStreamingCell(Z_Param_Out_CellCoordinates,Z_Param_Out_CellConfig);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function CreateStreamingCell *********************

// ********** Begin Class APCGWorldPartitionManager Function GetCellsInRadius **********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics
{
	struct PCGWorldPartitionManager_eventGetCellsInRadius_Parms
	{
		FVector WorldLocation;
		float Radius;
		TArray<FPCGStreamingCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Cells" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetCellsInRadius_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetCellsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGStreamingCell, METADATA_PARAMS(0, nullptr) }; // 3906788436
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetCellsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3906788436
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetCellsInRadius", Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::PCGWorldPartitionManager_eventGetCellsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::PCGWorldPartitionManager_eventGetCellsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetCellsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGStreamingCell>*)Z_Param__Result=P_THIS->GetCellsInRadius(Z_Param_Out_WorldLocation,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetCellsInRadius ************************

// ********** Begin Class APCGWorldPartitionManager Function GetCurrentState ***********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics
{
	struct PCGWorldPartitionManager_eventGetCurrentState_Parms
	{
		EPCGWorldPartitionState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === State Query Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== State Query Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetCurrentState_Parms, ReturnValue), Z_Construct_UEnum_Aura_EPCGWorldPartitionState, METADATA_PARAMS(0, nullptr) }; // 2129047537
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetCurrentState", Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::PCGWorldPartitionManager_eventGetCurrentState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::PCGWorldPartitionManager_eventGetCurrentState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetCurrentState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EPCGWorldPartitionState*)Z_Param__Result=P_THIS->GetCurrentState();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetCurrentState *************************

// ********** Begin Class APCGWorldPartitionManager Function GetDataLayers *************************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics
{
	struct PCGWorldPartitionManager_eventGetDataLayers_Parms
	{
		TArray<FPCGDataLayerConfig> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layers" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGDataLayerConfig, METADATA_PARAMS(0, nullptr) }; // 4011349172
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetDataLayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4011349172
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetDataLayers", Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::PCGWorldPartitionManager_eventGetDataLayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::PCGWorldPartitionManager_eventGetDataLayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetDataLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGDataLayerConfig>*)Z_Param__Result=P_THIS->GetDataLayers();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetDataLayers ***************************

// ********** Begin Class APCGWorldPartitionManager Function GetGeneratingCellCount ****************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics
{
	struct PCGWorldPartitionManager_eventGetGeneratingCellCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetGeneratingCellCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetGeneratingCellCount", Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::PCGWorldPartitionManager_eventGetGeneratingCellCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::PCGWorldPartitionManager_eventGetGeneratingCellCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetGeneratingCellCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetGeneratingCellCount();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetGeneratingCellCount ******************

// ********** Begin Class APCGWorldPartitionManager Function GetLoadedCellCount ********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics
{
	struct PCGWorldPartitionManager_eventGetLoadedCellCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetLoadedCellCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetLoadedCellCount", Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::PCGWorldPartitionManager_eventGetLoadedCellCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::PCGWorldPartitionManager_eventGetLoadedCellCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetLoadedCellCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedCellCount();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetLoadedCellCount **********************

// ********** Begin Class APCGWorldPartitionManager Function GetLoadedCells ************************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics
{
	struct PCGWorldPartitionManager_eventGetLoadedCells_Parms
	{
		TArray<FPCGStreamingCell> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Cells" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGStreamingCell, METADATA_PARAMS(0, nullptr) }; // 3906788436
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetLoadedCells_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3906788436
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetLoadedCells", Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::PCGWorldPartitionManager_eventGetLoadedCells_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::PCGWorldPartitionManager_eventGetLoadedCells_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetLoadedCells)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGStreamingCell>*)Z_Param__Result=P_THIS->GetLoadedCells();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetLoadedCells **************************

// ********** Begin Class APCGWorldPartitionManager Function GetMemoryUsageMB **********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics
{
	struct PCGWorldPartitionManager_eventGetMemoryUsageMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetMemoryUsageMB", Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::PCGWorldPartitionManager_eventGetMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::PCGWorldPartitionManager_eventGetMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMemoryUsageMB();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetMemoryUsageMB ************************

// ********** Begin Class APCGWorldPartitionManager Function GetOverallGenerationProgress **********
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics
{
	struct PCGWorldPartitionManager_eventGetOverallGenerationProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetOverallGenerationProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetOverallGenerationProgress", Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::PCGWorldPartitionManager_eventGetOverallGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::PCGWorldPartitionManager_eventGetOverallGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetOverallGenerationProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetOverallGenerationProgress();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetOverallGenerationProgress ************

// ********** Begin Class APCGWorldPartitionManager Function GetStreamingCell **********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics
{
	struct PCGWorldPartitionManager_eventGetStreamingCell_Parms
	{
		FIntPoint CellCoordinates;
		FPCGStreamingCell OutCell;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Cells" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutCell;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetStreamingCell_Parms, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_OutCell = { "OutCell", nullptr, (EPropertyFlags)0x0010008000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetStreamingCell_Parms, OutCell), Z_Construct_UScriptStruct_FPCGStreamingCell, METADATA_PARAMS(0, nullptr) }; // 3906788436
void Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventGetStreamingCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventGetStreamingCell_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_CellCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_OutCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetStreamingCell", Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::PCGWorldPartitionManager_eventGetStreamingCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::PCGWorldPartitionManager_eventGetStreamingCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetStreamingCell)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_CellCoordinates);
	P_GET_STRUCT_REF(FPCGStreamingCell,Z_Param_Out_OutCell);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetStreamingCell(Z_Param_Out_CellCoordinates,Z_Param_Out_OutCell);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetStreamingCell ************************

// ********** Begin Class APCGWorldPartitionManager Function GetWorldPartitionConfig ***************
struct Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics
{
	struct PCGWorldPartitionManager_eventGetWorldPartitionConfig_Parms
	{
		FPCGWorldPartitionConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG World Partition" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventGetWorldPartitionConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGWorldPartitionConfig, METADATA_PARAMS(0, nullptr) }; // 864525450
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "GetWorldPartitionConfig", Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::PCGWorldPartitionManager_eventGetWorldPartitionConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::PCGWorldPartitionManager_eventGetWorldPartitionConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execGetWorldPartitionConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGWorldPartitionConfig*)Z_Param__Result=P_THIS->GetWorldPartitionConfig();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function GetWorldPartitionConfig *****************

// ********** Begin Class APCGWorldPartitionManager Function InitializeWorldPartition **************
struct Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics
{
	struct PCGWorldPartitionManager_eventInitializeWorldPartition_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG World Partition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Core World Partition Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Core World Partition Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventInitializeWorldPartition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventInitializeWorldPartition_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "InitializeWorldPartition", Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::PCGWorldPartitionManager_eventInitializeWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::PCGWorldPartitionManager_eventInitializeWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execInitializeWorldPartition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeWorldPartition();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function InitializeWorldPartition ****************

// ********** Begin Class APCGWorldPartitionManager Function IntegrateWithLumen ********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics
{
	struct PCGWorldPartitionManager_eventIntegrateWithLumen_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventIntegrateWithLumen_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventIntegrateWithLumen_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "IntegrateWithLumen", Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::PCGWorldPartitionManager_eventIntegrateWithLumen_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::PCGWorldPartitionManager_eventIntegrateWithLumen_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execIntegrateWithLumen)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithLumen();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function IntegrateWithLumen **********************

// ********** Begin Class APCGWorldPartitionManager Function IntegrateWithNanite *******************
struct Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics
{
	struct PCGWorldPartitionManager_eventIntegrateWithNanite_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventIntegrateWithNanite_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventIntegrateWithNanite_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "IntegrateWithNanite", Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::PCGWorldPartitionManager_eventIntegrateWithNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::PCGWorldPartitionManager_eventIntegrateWithNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execIntegrateWithNanite)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithNanite();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function IntegrateWithNanite *********************

// ********** Begin Class APCGWorldPartitionManager Function IsInitialized *************************
struct Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics
{
	struct PCGWorldPartitionManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventIsInitialized_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "IsInitialized", Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::PCGWorldPartitionManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::PCGWorldPartitionManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function IsInitialized ***************************

// ********** Begin Class APCGWorldPartitionManager Function OnCellLoaded **************************
struct Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics
{
	struct PCGWorldPartitionManager_eventOnCellLoaded_Parms
	{
		FIntPoint CellCoordinates;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Event Handlers ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Event Handlers ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventOnCellLoaded_Parms, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::NewProp_CellCoordinates,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "OnCellLoaded", Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::PCGWorldPartitionManager_eventOnCellLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::PCGWorldPartitionManager_eventOnCellLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execOnCellLoaded)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_CellCoordinates);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCellLoaded(Z_Param_Out_CellCoordinates);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function OnCellLoaded ****************************

// ********** Begin Class APCGWorldPartitionManager Function OnCellUnloaded ************************
struct Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics
{
	struct PCGWorldPartitionManager_eventOnCellUnloaded_Parms
	{
		FIntPoint CellCoordinates;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventOnCellUnloaded_Parms, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::NewProp_CellCoordinates,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "OnCellUnloaded", Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::PCGWorldPartitionManager_eventOnCellUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::PCGWorldPartitionManager_eventOnCellUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execOnCellUnloaded)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_CellCoordinates);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCellUnloaded(Z_Param_Out_CellCoordinates);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function OnCellUnloaded **************************

// ********** Begin Class APCGWorldPartitionManager Function OnPCGGenerationComplete ***************
struct Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics
{
	struct PCGWorldPartitionManager_eventOnPCGGenerationComplete_Parms
	{
		UPCGComponent* PCGComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventOnPCGGenerationComplete_Parms, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::NewProp_PCGComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "OnPCGGenerationComplete", Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::PCGWorldPartitionManager_eventOnPCGGenerationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::PCGWorldPartitionManager_eventOnPCGGenerationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execOnPCGGenerationComplete)
{
	P_GET_OBJECT(UPCGComponent,Z_Param_PCGComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPCGGenerationComplete(Z_Param_PCGComponent);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function OnPCGGenerationComplete *****************

// ********** Begin Class APCGWorldPartitionManager Function RemoveDataLayer ***********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics
{
	struct PCGWorldPartitionManager_eventRemoveDataLayer_Parms
	{
		FName LayerName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layers" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LayerName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventRemoveDataLayer_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
void Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventRemoveDataLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventRemoveDataLayer_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "RemoveDataLayer", Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::PCGWorldPartitionManager_eventRemoveDataLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::PCGWorldPartitionManager_eventRemoveDataLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execRemoveDataLayer)
{
	P_GET_PROPERTY_REF(FNameProperty,Z_Param_Out_LayerName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveDataLayer(Z_Param_Out_LayerName);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function RemoveDataLayer *************************

// ********** Begin Class APCGWorldPartitionManager Function RemoveStreamingCell *******************
struct Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics
{
	struct PCGWorldPartitionManager_eventRemoveStreamingCell_Parms
	{
		FIntPoint CellCoordinates;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Cells" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventRemoveStreamingCell_Parms, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
void Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventRemoveStreamingCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventRemoveStreamingCell_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::NewProp_CellCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "RemoveStreamingCell", Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::PCGWorldPartitionManager_eventRemoveStreamingCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::PCGWorldPartitionManager_eventRemoveStreamingCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execRemoveStreamingCell)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_CellCoordinates);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveStreamingCell(Z_Param_Out_CellCoordinates);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function RemoveStreamingCell *********************

// ********** Begin Class APCGWorldPartitionManager Function RemoveStreamingSource *****************
struct Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics
{
	struct PCGWorldPartitionManager_eventRemoveStreamingSource_Parms
	{
		FVector WorldLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventRemoveStreamingSource_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::NewProp_WorldLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "RemoveStreamingSource", Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::PCGWorldPartitionManager_eventRemoveStreamingSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::PCGWorldPartitionManager_eventRemoveStreamingSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execRemoveStreamingSource)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveStreamingSource(Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function RemoveStreamingSource *******************

// ********** Begin Class APCGWorldPartitionManager Function SetDataLayerState *********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics
{
	struct PCGWorldPartitionManager_eventSetDataLayerState_Parms
	{
		FName LayerName;
		bool bShouldBeLoaded;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Layers" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LayerName;
	static void NewProp_bShouldBeLoaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShouldBeLoaded;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventSetDataLayerState_Parms, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
void Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_bShouldBeLoaded_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventSetDataLayerState_Parms*)Obj)->bShouldBeLoaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_bShouldBeLoaded = { "bShouldBeLoaded", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventSetDataLayerState_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_bShouldBeLoaded_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventSetDataLayerState_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventSetDataLayerState_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_bShouldBeLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "SetDataLayerState", Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::PCGWorldPartitionManager_eventSetDataLayerState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::PCGWorldPartitionManager_eventSetDataLayerState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execSetDataLayerState)
{
	P_GET_PROPERTY_REF(FNameProperty,Z_Param_Out_LayerName);
	P_GET_UBOOL(Z_Param_bShouldBeLoaded);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetDataLayerState(Z_Param_Out_LayerName,Z_Param_bShouldBeLoaded);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function SetDataLayerState ***********************

// ********** Begin Class APCGWorldPartitionManager Function SetProceduralMapGenerator *************
struct Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics
{
	struct PCGWorldPartitionManager_eventSetProceduralMapGenerator_Parms
	{
		AProceduralMapGenerator* Generator;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Integration Functions ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Integration Functions ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Generator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::NewProp_Generator = { "Generator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventSetProceduralMapGenerator_Parms, Generator), Z_Construct_UClass_AProceduralMapGenerator_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::NewProp_Generator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "SetProceduralMapGenerator", Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::PCGWorldPartitionManager_eventSetProceduralMapGenerator_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::PCGWorldPartitionManager_eventSetProceduralMapGenerator_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execSetProceduralMapGenerator)
{
	P_GET_OBJECT(AProceduralMapGenerator,Z_Param_Generator);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetProceduralMapGenerator(Z_Param_Generator);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function SetProceduralMapGenerator ***************

// ********** Begin Class APCGWorldPartitionManager Function SetStreamingSource ********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics
{
	struct PCGWorldPartitionManager_eventSetStreamingSource_Parms
	{
		FVector WorldLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Streaming Control ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Streaming Control ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventSetStreamingSource_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::NewProp_WorldLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "SetStreamingSource", Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::PCGWorldPartitionManager_eventSetStreamingSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::PCGWorldPartitionManager_eventSetStreamingSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execSetStreamingSource)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStreamingSource(Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function SetStreamingSource **********************

// ********** Begin Class APCGWorldPartitionManager Function SetWorldPartitionConfig ***************
struct Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics
{
	struct PCGWorldPartitionManager_eventSetWorldPartitionConfig_Parms
	{
		FPCGWorldPartitionConfig NewConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG World Partition" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventSetWorldPartitionConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FPCGWorldPartitionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 864525450
void Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventSetWorldPartitionConfig_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventSetWorldPartitionConfig_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::NewProp_NewConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "SetWorldPartitionConfig", Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::PCGWorldPartitionManager_eventSetWorldPartitionConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::PCGWorldPartitionManager_eventSetWorldPartitionConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execSetWorldPartitionConfig)
{
	P_GET_STRUCT_REF(FPCGWorldPartitionConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetWorldPartitionConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function SetWorldPartitionConfig *****************

// ********** Begin Class APCGWorldPartitionManager Function ShutdownWorldPartition ****************
struct Z_Construct_UFunction_APCGWorldPartitionManager_ShutdownWorldPartition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG World Partition" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_ShutdownWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "ShutdownWorldPartition", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_ShutdownWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_ShutdownWorldPartition_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_ShutdownWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_ShutdownWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execShutdownWorldPartition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownWorldPartition();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function ShutdownWorldPartition ******************

// ********** Begin Class APCGWorldPartitionManager Function StartAllCellGeneration ****************
struct Z_Construct_UFunction_APCGWorldPartitionManager_StartAllCellGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_StartAllCellGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "StartAllCellGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StartAllCellGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_StartAllCellGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_StartAllCellGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_StartAllCellGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execStartAllCellGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartAllCellGeneration();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function StartAllCellGeneration ******************

// ********** Begin Class APCGWorldPartitionManager Function StartCellGeneration *******************
struct Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics
{
	struct PCGWorldPartitionManager_eventStartCellGeneration_Parms
	{
		FIntPoint CellCoordinates;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === PCG Generation Control ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== PCG Generation Control ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventStartCellGeneration_Parms, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
void Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventStartCellGeneration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventStartCellGeneration_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::NewProp_CellCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "StartCellGeneration", Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::PCGWorldPartitionManager_eventStartCellGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::PCGWorldPartitionManager_eventStartCellGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execStartCellGeneration)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_CellCoordinates);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartCellGeneration(Z_Param_Out_CellCoordinates);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function StartCellGeneration *********************

// ********** Begin Class APCGWorldPartitionManager Function StopAllCellGeneration *****************
struct Z_Construct_UFunction_APCGWorldPartitionManager_StopAllCellGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_StopAllCellGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "StopAllCellGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StopAllCellGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_StopAllCellGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_StopAllCellGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_StopAllCellGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execStopAllCellGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAllCellGeneration();
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function StopAllCellGeneration *******************

// ********** Begin Class APCGWorldPartitionManager Function StopCellGeneration ********************
struct Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics
{
	struct PCGWorldPartitionManager_eventStopCellGeneration_Parms
	{
		FIntPoint CellCoordinates;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Generation" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellCoordinates_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellCoordinates;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::NewProp_CellCoordinates = { "CellCoordinates", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGWorldPartitionManager_eventStopCellGeneration_Parms, CellCoordinates), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellCoordinates_MetaData), NewProp_CellCoordinates_MetaData) };
void Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGWorldPartitionManager_eventStopCellGeneration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGWorldPartitionManager_eventStopCellGeneration_Parms), &Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::NewProp_CellCoordinates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGWorldPartitionManager, nullptr, "StopCellGeneration", Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::PCGWorldPartitionManager_eventStopCellGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::PCGWorldPartitionManager_eventStopCellGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGWorldPartitionManager::execStopCellGeneration)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_CellCoordinates);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopCellGeneration(Z_Param_Out_CellCoordinates);
	P_NATIVE_END;
}
// ********** End Class APCGWorldPartitionManager Function StopCellGeneration **********************

// ********** Begin Class APCGWorldPartitionManager ************************************************
void APCGWorldPartitionManager::StaticRegisterNativesAPCGWorldPartitionManager()
{
	UClass* Class = APCGWorldPartitionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddStreamingSource", &APCGWorldPartitionManager::execAddStreamingSource },
		{ "ClearStreamingSources", &APCGWorldPartitionManager::execClearStreamingSources },
		{ "CreateDataLayer", &APCGWorldPartitionManager::execCreateDataLayer },
		{ "CreateStreamingCell", &APCGWorldPartitionManager::execCreateStreamingCell },
		{ "GetCellsInRadius", &APCGWorldPartitionManager::execGetCellsInRadius },
		{ "GetCurrentState", &APCGWorldPartitionManager::execGetCurrentState },
		{ "GetDataLayers", &APCGWorldPartitionManager::execGetDataLayers },
		{ "GetGeneratingCellCount", &APCGWorldPartitionManager::execGetGeneratingCellCount },
		{ "GetLoadedCellCount", &APCGWorldPartitionManager::execGetLoadedCellCount },
		{ "GetLoadedCells", &APCGWorldPartitionManager::execGetLoadedCells },
		{ "GetMemoryUsageMB", &APCGWorldPartitionManager::execGetMemoryUsageMB },
		{ "GetOverallGenerationProgress", &APCGWorldPartitionManager::execGetOverallGenerationProgress },
		{ "GetStreamingCell", &APCGWorldPartitionManager::execGetStreamingCell },
		{ "GetWorldPartitionConfig", &APCGWorldPartitionManager::execGetWorldPartitionConfig },
		{ "InitializeWorldPartition", &APCGWorldPartitionManager::execInitializeWorldPartition },
		{ "IntegrateWithLumen", &APCGWorldPartitionManager::execIntegrateWithLumen },
		{ "IntegrateWithNanite", &APCGWorldPartitionManager::execIntegrateWithNanite },
		{ "IsInitialized", &APCGWorldPartitionManager::execIsInitialized },
		{ "OnCellLoaded", &APCGWorldPartitionManager::execOnCellLoaded },
		{ "OnCellUnloaded", &APCGWorldPartitionManager::execOnCellUnloaded },
		{ "OnPCGGenerationComplete", &APCGWorldPartitionManager::execOnPCGGenerationComplete },
		{ "RemoveDataLayer", &APCGWorldPartitionManager::execRemoveDataLayer },
		{ "RemoveStreamingCell", &APCGWorldPartitionManager::execRemoveStreamingCell },
		{ "RemoveStreamingSource", &APCGWorldPartitionManager::execRemoveStreamingSource },
		{ "SetDataLayerState", &APCGWorldPartitionManager::execSetDataLayerState },
		{ "SetProceduralMapGenerator", &APCGWorldPartitionManager::execSetProceduralMapGenerator },
		{ "SetStreamingSource", &APCGWorldPartitionManager::execSetStreamingSource },
		{ "SetWorldPartitionConfig", &APCGWorldPartitionManager::execSetWorldPartitionConfig },
		{ "ShutdownWorldPartition", &APCGWorldPartitionManager::execShutdownWorldPartition },
		{ "StartAllCellGeneration", &APCGWorldPartitionManager::execStartAllCellGeneration },
		{ "StartCellGeneration", &APCGWorldPartitionManager::execStartCellGeneration },
		{ "StopAllCellGeneration", &APCGWorldPartitionManager::execStopAllCellGeneration },
		{ "StopCellGeneration", &APCGWorldPartitionManager::execStopCellGeneration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_APCGWorldPartitionManager;
UClass* APCGWorldPartitionManager::GetPrivateStaticClass()
{
	using TClass = APCGWorldPartitionManager;
	if (!Z_Registration_Info_UClass_APCGWorldPartitionManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGWorldPartitionManager"),
			Z_Registration_Info_UClass_APCGWorldPartitionManager.InnerSingleton,
			StaticRegisterNativesAPCGWorldPartitionManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_APCGWorldPartitionManager.InnerSingleton;
}
UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister()
{
	return APCGWorldPartitionManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_APCGWorldPartitionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * APCGWorldPartitionManager\n * \n * Advanced World Partition manager for PCG content generation in large worlds.\n * Integrates with UE5.6's World Partition system to provide seamless streaming\n * of procedurally generated content across massive game worlds.\n * \n * Features:\n * - Automatic cell-based content streaming\n * - Data layer management for different content types\n * - HLOD integration for distant content\n * - Memory-efficient PCG generation\n * - Multi-threaded streaming operations\n * - Integration with Nanite and Lumen systems\n */" },
#endif
		{ "IncludePath", "APCGWorldPartitionManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "APCGWorldPartitionManager\n\nAdvanced World Partition manager for PCG content generation in large worlds.\nIntegrates with UE5.6's World Partition system to provide seamless streaming\nof procedurally generated content across massive game worlds.\n\nFeatures:\n- Automatic cell-based content streaming\n- Data layer management for different content types\n- HLOD integration for distant content\n- Memory-efficient PCG generation\n- Multi-threaded streaming operations\n- Integration with Nanite and Lumen systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Core Properties ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Core Properties ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerConfigs_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Subsystem References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Subsystem References ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataLayerManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGSubsystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingCells_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Streaming Data ===\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Streaming Data ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingSources_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralMapGenerator_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGWorldPartitionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPartitionConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DataLayerConfigs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DataLayerConfigs;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DataLayerManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGSubsystem;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingCells_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingCells_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StreamingCells;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingSources_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StreamingSources;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ProceduralMapGenerator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_AddStreamingSource, "AddStreamingSource" }, // 2004899057
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_ClearStreamingSources, "ClearStreamingSources" }, // 213905163
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_CreateDataLayer, "CreateDataLayer" }, // 2396269955
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_CreateStreamingCell, "CreateStreamingCell" }, // 256178200
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetCellsInRadius, "GetCellsInRadius" }, // 2838251154
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetCurrentState, "GetCurrentState" }, // 3253459311
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetDataLayers, "GetDataLayers" }, // 2793985399
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetGeneratingCellCount, "GetGeneratingCellCount" }, // 2610267265
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCellCount, "GetLoadedCellCount" }, // 2861905829
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetLoadedCells, "GetLoadedCells" }, // 386475290
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetMemoryUsageMB, "GetMemoryUsageMB" }, // 1947070409
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetOverallGenerationProgress, "GetOverallGenerationProgress" }, // 1079934742
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetStreamingCell, "GetStreamingCell" }, // 2073174948
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_GetWorldPartitionConfig, "GetWorldPartitionConfig" }, // 3497038813
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_InitializeWorldPartition, "InitializeWorldPartition" }, // 1536703801
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithLumen, "IntegrateWithLumen" }, // 3168041128
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_IntegrateWithNanite, "IntegrateWithNanite" }, // 3177290907
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_IsInitialized, "IsInitialized" }, // 3944249471
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_OnCellLoaded, "OnCellLoaded" }, // 1876647865
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_OnCellUnloaded, "OnCellUnloaded" }, // 171796602
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_OnPCGGenerationComplete, "OnPCGGenerationComplete" }, // 1938208121
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_RemoveDataLayer, "RemoveDataLayer" }, // 678464576
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingCell, "RemoveStreamingCell" }, // 270873427
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_RemoveStreamingSource, "RemoveStreamingSource" }, // 1803478547
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_SetDataLayerState, "SetDataLayerState" }, // 1949046265
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_SetProceduralMapGenerator, "SetProceduralMapGenerator" }, // 35400330
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_SetStreamingSource, "SetStreamingSource" }, // 822149330
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_SetWorldPartitionConfig, "SetWorldPartitionConfig" }, // 402836847
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_ShutdownWorldPartition, "ShutdownWorldPartition" }, // 1047258054
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_StartAllCellGeneration, "StartAllCellGeneration" }, // 1403515529
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_StartCellGeneration, "StartCellGeneration" }, // 3250854475
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_StopAllCellGeneration, "StopAllCellGeneration" }, // 2741379537
		{ &Z_Construct_UFunction_APCGWorldPartitionManager_StopCellGeneration, "StopCellGeneration" }, // 2222162049
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APCGWorldPartitionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_WorldPartitionConfig = { "WorldPartitionConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, WorldPartitionConfig), Z_Construct_UScriptStruct_FPCGWorldPartitionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionConfig_MetaData), NewProp_WorldPartitionConfig_MetaData) }; // 864525450
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_DataLayerConfigs_Inner = { "DataLayerConfigs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGDataLayerConfig, METADATA_PARAMS(0, nullptr) }; // 4011349172
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_DataLayerConfigs = { "DataLayerConfigs", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, DataLayerConfigs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerConfigs_MetaData), NewProp_DataLayerConfigs_MetaData) }; // 4011349172
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, CurrentState), Z_Construct_UEnum_Aura_EPCGWorldPartitionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 2129047537
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_WorldPartitionSubsystem = { "WorldPartitionSubsystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, WorldPartitionSubsystem), Z_Construct_UClass_UWorldPartitionSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionSubsystem_MetaData), NewProp_WorldPartitionSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_DataLayerManager = { "DataLayerManager", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, DataLayerManager), Z_Construct_UClass_UDataLayerManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataLayerManager_MetaData), NewProp_DataLayerManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_PCGSubsystem = { "PCGSubsystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, PCGSubsystem), Z_Construct_UClass_UPCGSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGSubsystem_MetaData), NewProp_PCGSubsystem_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingCells_ValueProp = { "StreamingCells", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGStreamingCell, METADATA_PARAMS(0, nullptr) }; // 3906788436
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingCells_Key_KeyProp = { "StreamingCells_Key", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingCells = { "StreamingCells", nullptr, (EPropertyFlags)0x0020088000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, StreamingCells), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingCells_MetaData), NewProp_StreamingCells_MetaData) }; // 3906788436
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingSources_Inner = { "StreamingSources", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingSources = { "StreamingSources", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, StreamingSources), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingSources_MetaData), NewProp_StreamingSources_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_ProceduralMapGenerator = { "ProceduralMapGenerator", nullptr, (EPropertyFlags)0x0024080000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGWorldPartitionManager, ProceduralMapGenerator), Z_Construct_UClass_AProceduralMapGenerator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralMapGenerator_MetaData), NewProp_ProceduralMapGenerator_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APCGWorldPartitionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_WorldPartitionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_DataLayerConfigs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_DataLayerConfigs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_WorldPartitionSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_DataLayerManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_PCGSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingCells_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingCells_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingSources_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_StreamingSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGWorldPartitionManager_Statics::NewProp_ProceduralMapGenerator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGWorldPartitionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APCGWorldPartitionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGWorldPartitionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_APCGWorldPartitionManager_Statics::ClassParams = {
	&APCGWorldPartitionManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APCGWorldPartitionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APCGWorldPartitionManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APCGWorldPartitionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_APCGWorldPartitionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APCGWorldPartitionManager()
{
	if (!Z_Registration_Info_UClass_APCGWorldPartitionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APCGWorldPartitionManager.OuterSingleton, Z_Construct_UClass_APCGWorldPartitionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APCGWorldPartitionManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APCGWorldPartitionManager);
APCGWorldPartitionManager::~APCGWorldPartitionManager() {}
// ********** End Class APCGWorldPartitionManager **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGWorldPartitionState_StaticEnum, TEXT("EPCGWorldPartitionState"), &Z_Registration_Info_UEnum_EPCGWorldPartitionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2129047537U) },
		{ EPCGStreamingPriority_StaticEnum, TEXT("EPCGStreamingPriority"), &Z_Registration_Info_UEnum_EPCGStreamingPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3992031022U) },
		{ EPCGDataLayerType_StaticEnum, TEXT("EPCGDataLayerType"), &Z_Registration_Info_UEnum_EPCGDataLayerType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2038321418U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGWorldPartitionConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGWorldPartitionConfig_Statics::NewStructOps, TEXT("PCGWorldPartitionConfig"), &Z_Registration_Info_UScriptStruct_FPCGWorldPartitionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGWorldPartitionConfig), 864525450U) },
		{ FPCGStreamingCell::StaticStruct, Z_Construct_UScriptStruct_FPCGStreamingCell_Statics::NewStructOps, TEXT("PCGStreamingCell"), &Z_Registration_Info_UScriptStruct_FPCGStreamingCell, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGStreamingCell), 3906788436U) },
		{ FPCGDataLayerConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGDataLayerConfig_Statics::NewStructOps, TEXT("PCGDataLayerConfig"), &Z_Registration_Info_UScriptStruct_FPCGDataLayerConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGDataLayerConfig), 4011349172U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APCGWorldPartitionManager, APCGWorldPartitionManager::StaticClass, TEXT("APCGWorldPartitionManager"), &Z_Registration_Info_UClass_APCGWorldPartitionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APCGWorldPartitionManager), 343225250U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_3495753113(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGWorldPartitionManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
