#include "ALaneManager.h"
#include "AMinionWaveManager.h"
#include "Components/SplineComponent.h"
#include "Components/SceneComponent.h"
#include "Math/UnrealMathUtility.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Actor.h"
#include "Engine/OverlapResult.h"

ALaneManager::ALaneManager()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Criar splines para cada lane
    SuperiorLaneSpline = CreateDefaultSubobject<USplineComponent>(TEXT("SuperiorLaneSpline"));
    SuperiorLaneSpline->SetupAttachment(RootComponent);

    CentralLaneSpline = CreateDefaultSubobject<USplineComponent>(TEXT("CentralLaneSpline"));
    CentralLaneSpline->SetupAttachment(RootComponent);

    InferiorLaneSpline = CreateDefaultSubobject<USplineComponent>(TEXT("InferiorLaneSpline"));
    InferiorLaneSpline->SetupAttachment(RootComponent);

    // Inicializar arrays de dados
    LanesData.SetNum(3);
    
    // Configurar dados da lane superior
    LanesData[0].LaneType = ELaneManagerType::Superior;
    LanesData[0].LaneWidth = SUPERIOR_LANE_WIDTH;
    LanesData[0].StartPosition = FVector(-7200.0f, 4157.0f, 0.0f);
    LanesData[0].EndPosition = FVector(7200.0f, -4157.0f, 0.0f);

    // Configurar dados da lane central
    LanesData[1].LaneType = ELaneManagerType::Central;
    LanesData[1].LaneWidth = CENTRAL_LANE_WIDTH;
    LanesData[1].StartPosition = FVector(-7200.0f, 0.0f, 0.0f);
    LanesData[1].EndPosition = FVector(7200.0f, 0.0f, 0.0f);

    // Configurar dados da lane inferior
    LanesData[2].LaneType = ELaneManagerType::Inferior;
    LanesData[2].LaneWidth = INFERIOR_LANE_WIDTH;
    LanesData[2].StartPosition = FVector(-7200.0f, -4157.0f, 0.0f);
    LanesData[2].EndPosition = FVector(7200.0f, 4157.0f, 0.0f);
}

void ALaneManager::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar sistema de lanes
    InitializeLanes();
    InitializeTowers();
    GenerateWaypoints();
    
    // Validar geometria
    if (!ValidateLaneGeometry())
    {
        UE_LOG(LogTemp, Error, TEXT("ALaneManager: Falha na validação da geometria das lanes!"));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("ALaneManager: Sistema de lanes inicializado com sucesso!"));
    }
}

void ALaneManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Debug: Desenhar lanes no mundo
    if (GetWorld() && GetWorld()->IsGameWorld())
    {
        // Desenhar lane superior
        for (float X = -7200.0f; X <= 7200.0f; X += 200.0f)
        {
            FVector Pos1 = CalculateSuperiorLanePosition(X);
            FVector Pos2 = CalculateSuperiorLanePosition(X + 200.0f);
            DrawDebugLine(GetWorld(), Pos1, Pos2, FColor::Red, false, -1.0f, 0, 5.0f);
        }
        
        // Desenhar lane central
        for (float X = -7200.0f; X <= 7200.0f; X += 200.0f)
        {
            FVector Pos1 = CalculateCentralLanePosition(X);
            FVector Pos2 = CalculateCentralLanePosition(X + 200.0f);
            DrawDebugLine(GetWorld(), Pos1, Pos2, FColor::Green, false, -1.0f, 0, 5.0f);
        }
        
        // Desenhar lane inferior
        for (float X = -7200.0f; X <= 7200.0f; X += 200.0f)
        {
            FVector Pos1 = CalculateInferiorLanePosition(X);
            FVector Pos2 = CalculateInferiorLanePosition(X + 200.0f);
            DrawDebugLine(GetWorld(), Pos1, Pos2, FColor::Blue, false, -1.0f, 0, 5.0f);
        }
    }
}

FVector ALaneManager::CalculateSuperiorLanePosition(float X) const
{
    // Lane Superior: Y = -0.577X + 6928
    float Y = SUPERIOR_LANE_SLOPE * X + SUPERIOR_LANE_INTERCEPT;
    return FVector(X, Y, 0.0f);
}

FVector ALaneManager::CalculateCentralLanePosition(float X) const
{
    // Lane Central: Y = 0
    return FVector(X, 0.0f, 0.0f);
}

FVector ALaneManager::CalculateInferiorLanePosition(float X) const
{
    // Lane Inferior: Y = 0.577X - 6928
    float Y = INFERIOR_LANE_SLOPE * X + INFERIOR_LANE_INTERCEPT;
    return FVector(X, Y, 0.0f);
}

void ALaneManager::InitializeLanes()
{
    SetupLaneSplines();
    
    UE_LOG(LogTemp, Log, TEXT("ALaneManager: Lanes inicializadas com geometria matemática precisa"));
}

void ALaneManager::InitializeTowers()
{
    // Limpar torres existentes
    TowersData.Empty();
    
    // Torres Externas - Cilindros cônicos
    TArray<FVector> ExternalTowerPositions = {
        FVector(-6000.0f, 3464.0f, 0.0f),   // Superior esquerda
        FVector(-6000.0f, -3464.0f, 0.0f),  // Inferior esquerda
        FVector(6000.0f, 3464.0f, 0.0f),    // Superior direita
        FVector(6000.0f, -3464.0f, 0.0f)    // Inferior direita
    };
    
    for (const FVector& Position : ExternalTowerPositions)
    {
        FTowerData TowerData;
        TowerData.TowerType = ETowerType::Externa;
        TowerData.Position = Position;
        TowerData.BaseRadius = 120.0f;
        TowerData.TopRadius = 80.0f;
        TowerData.Height = 600.0f;
        TowerData.AttackRange = 800.0f;
        TowerData.Health = 2000;
        
        TowersData.Add(TowerData);
        SpawnTower(TowerData);
    }
    
    // Torres Internas - Prismas octogonais
    TArray<FVector> InternalTowerPositions = {
        FVector(-3000.0f, 1732.0f, 0.0f),   // Superior esquerda
        FVector(-3000.0f, -1732.0f, 0.0f),  // Inferior esquerda
        FVector(3000.0f, 1732.0f, 0.0f),    // Superior direita
        FVector(3000.0f, -1732.0f, 0.0f)    // Inferior direita
    };
    
    for (const FVector& Position : InternalTowerPositions)
    {
        FTowerData TowerData;
        TowerData.TowerType = ETowerType::Interna;
        TowerData.Position = Position;
        TowerData.BaseRadius = 100.0f;
        TowerData.TopRadius = 100.0f;
        TowerData.Height = 800.0f;
        TowerData.AttackRange = 900.0f;
        TowerData.Health = 2500;
        
        TowersData.Add(TowerData);
        SpawnTower(TowerData);
    }
    
    // Torres de Inibidor - Torres duplas
    TArray<FVector> InhibitorTowerPositions = {
        FVector(-1500.0f, 866.0f, 0.0f),    // Superior esquerda
        FVector(-1500.0f, -866.0f, 0.0f),   // Inferior esquerda
        FVector(1500.0f, 866.0f, 0.0f),     // Superior direita
        FVector(1500.0f, -866.0f, 0.0f)     // Inferior direita
    };
    
    for (const FVector& Position : InhibitorTowerPositions)
    {
        FTowerData TowerData;
        TowerData.TowerType = ETowerType::Inibidor;
        TowerData.Position = Position;
        TowerData.BaseRadius = 150.0f;
        TowerData.TopRadius = 100.0f;
        TowerData.Height = 1000.0f;
        TowerData.AttackRange = 1000.0f;
        TowerData.Health = 3000;
        
        TowersData.Add(TowerData);
        SpawnTower(TowerData);
    }
    
    UE_LOG(LogTemp, Log, TEXT("ALaneManager: %d torres inicializadas"), TowersData.Num());
}

void ALaneManager::GenerateWaypoints()
{
    // Gerar waypoints para cada lane
    for (int32 i = 0; i < LanesData.Num(); i++)
    {
        CalculateWaypointsForLane(LanesData[i].LaneType);
    }
    
    UE_LOG(LogTemp, Log, TEXT("ALaneManager: Waypoints gerados para todas as lanes"));
}

TArray<FVector> ALaneManager::FindPath(const FVector& StartPos, const FVector& EndPos, ELaneManagerType LaneType)
{
    TArray<FVector> Path;
    TArray<FPathNode> OpenSet;
    TArray<FPathNode> ClosedSet;
    
    // Obter waypoints da lane específica
    TArray<FVector> LaneWaypoints;
    if (LaneType == ELaneManagerType::Superior && LanesData.IsValidIndex(0))
    {
        LaneWaypoints = LanesData[0].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Central && LanesData.IsValidIndex(1))
    {
        LaneWaypoints = LanesData[1].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Inferior && LanesData.IsValidIndex(2))
    {
        LaneWaypoints = LanesData[2].Waypoints;
    }
    
    if (LaneWaypoints.Num() == 0)
    {
        // Fallback: linha direta
        Path.Add(StartPos);
        Path.Add(EndPos);
        return Path;
    }
    
    // Implementação simplificada do A*
    // Encontrar waypoint mais próximo do início
    int32 StartWaypointIndex = 0;
    float MinStartDistance = FVector::Dist(StartPos, LaneWaypoints[0]);
    for (int32 i = 1; i < LaneWaypoints.Num(); i++)
    {
        float Distance = FVector::Dist(StartPos, LaneWaypoints[i]);
        if (Distance < MinStartDistance)
        {
            MinStartDistance = Distance;
            StartWaypointIndex = i;
        }
    }
    
    // Encontrar waypoint mais próximo do fim
    int32 EndWaypointIndex = 0;
    float MinEndDistance = FVector::Dist(EndPos, LaneWaypoints[0]);
    for (int32 i = 1; i < LaneWaypoints.Num(); i++)
    {
        float Distance = FVector::Dist(EndPos, LaneWaypoints[i]);
        if (Distance < MinEndDistance)
        {
            MinEndDistance = Distance;
            EndWaypointIndex = i;
        }
    }
    
    // Construir caminho através dos waypoints
    Path.Add(StartPos);
    
    if (StartWaypointIndex < EndWaypointIndex)
    {
        for (int32 i = StartWaypointIndex; i <= EndWaypointIndex; i++)
        {
            Path.Add(LaneWaypoints[i]);
        }
    }
    else
    {
        for (int32 i = StartWaypointIndex; i >= EndWaypointIndex; i--)
        {
            Path.Add(LaneWaypoints[i]);
        }
    }
    
    Path.Add(EndPos);
    
    return Path;
}

float ALaneManager::CalculateHeuristic(const FVector& Start, const FVector& End) const
{
    // Distância euclidiana
    return FVector::Dist(Start, End);
}

TArray<FVector> ALaneManager::GetNearbyWaypoints(const FVector& Position, ELaneManagerType LaneType, float Radius) const
{
    TArray<FVector> NearbyWaypoints;
    
    // Obter waypoints da lane específica
    TArray<FVector> LaneWaypoints;
    if (LaneType == ELaneManagerType::Superior && LanesData.IsValidIndex(0))
    {
        LaneWaypoints = LanesData[0].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Central && LanesData.IsValidIndex(1))
    {
        LaneWaypoints = LanesData[1].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Inferior && LanesData.IsValidIndex(2))
    {
        LaneWaypoints = LanesData[2].Waypoints;
    }
    
    // Filtrar waypoints dentro do raio
    for (const FVector& Waypoint : LaneWaypoints)
    {
        if (FVector::Dist(Position, Waypoint) <= Radius)
        {
            NearbyWaypoints.Add(Waypoint);
        }
    }
    
    return NearbyWaypoints;
}

void ALaneManager::SpawnTower(const FTowerData& TowerData)
{
    // Esta função seria implementada para spawnar torres reais no mundo
    // Por enquanto, apenas log para debug
    UE_LOG(LogTemp, Log, TEXT("Torre %s spawnada em (%f, %f, %f)"), 
           *UEnum::GetValueAsString(TowerData.TowerType),
           TowerData.Position.X, TowerData.Position.Y, TowerData.Position.Z);
}

TArray<AActor*> ALaneManager::FindTargetsInRange(const FVector& TowerPosition, float Range) const
{
    TArray<AActor*> TargetsInRange;
    
    if (!GetWorld())
    {
        return TargetsInRange;
    }
    
    // Buscar todos os atores em um raio
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    
    bool bHit = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        TowerPosition,
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Range),
        QueryParams
    );
    
    if (bHit)
    {
        for (const FOverlapResult& Result : OverlapResults)
        {
            if (Result.GetActor() && Result.GetActor()->IsValidLowLevel())
            {
                TargetsInRange.Add(Result.GetActor());
            }
        }
    }
    
    return TargetsInRange;
}

AActor* ALaneManager::GetBestTarget(const FVector& TowerPosition, const TArray<AActor*>& PotentialTargets) const
{
    if (PotentialTargets.Num() == 0)
    {
        return nullptr;
    }
    
    // Prioridade: Torre > Minion > Campeão
    // Por enquanto, retornar o mais próximo
    AActor* BestTarget = PotentialTargets[0];
    float MinDistance = FVector::Dist(TowerPosition, BestTarget->GetActorLocation());
    
    for (AActor* Target : PotentialTargets)
    {
        float Distance = FVector::Dist(TowerPosition, Target->GetActorLocation());
        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            BestTarget = Target;
        }
    }
    
    return BestTarget;
}

bool ALaneManager::ValidateLaneGeometry() const
{
    // Validar equações matemáticas das lanes
    
    // Testar pontos conhecidos da lane superior
    FVector TestPoint1 = CalculateSuperiorLanePosition(0.0f);
    if (!FMath::IsNearlyEqual(TestPoint1.Y, SUPERIOR_LANE_INTERCEPT, 1.0f))
    {
        UE_LOG(LogTemp, Error, TEXT("Falha na validação da lane superior no ponto X=0"));
        return false;
    }
    
    // Testar pontos conhecidos da lane central
    FVector TestPoint2 = CalculateCentralLanePosition(1000.0f);
    if (!FMath::IsNearlyEqual(TestPoint2.Y, 0.0f, 1.0f))
    {
        UE_LOG(LogTemp, Error, TEXT("Falha na validação da lane central"));
        return false;
    }
    
    // Testar pontos conhecidos da lane inferior
    FVector TestPoint3 = CalculateInferiorLanePosition(0.0f);
    if (!FMath::IsNearlyEqual(TestPoint3.Y, INFERIOR_LANE_INTERCEPT, 1.0f))
    {
        UE_LOG(LogTemp, Error, TEXT("Falha na validação da lane inferior no ponto X=0"));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("Validação da geometria das lanes: SUCESSO"));
    return true;
}

bool ALaneManager::IsPositionInLane(const FVector& Position, ELaneManagerType LaneType, float Tolerance) const
{
    FVector LaneCenter;
    float LaneWidth;
    
    switch (LaneType)
    {
        case ELaneManagerType::Superior:
            LaneCenter = CalculateSuperiorLanePosition(Position.X);
            LaneWidth = SUPERIOR_LANE_WIDTH;
            break;
        case ELaneManagerType::Central:
            LaneCenter = CalculateCentralLanePosition(Position.X);
            LaneWidth = CENTRAL_LANE_WIDTH;
            break;
        case ELaneManagerType::Inferior:
            LaneCenter = CalculateInferiorLanePosition(Position.X);
            LaneWidth = INFERIOR_LANE_WIDTH;
            break;
        default:
            return false;
    }
    
    float DistanceFromCenter = FMath::Abs(Position.Y - LaneCenter.Y);
    return DistanceFromCenter <= (LaneWidth / 2.0f) + Tolerance;
}

ELaneManagerType ALaneManager::GetClosestLane(const FVector& Position) const
{
    float DistToSuperior = FMath::Abs(Position.Y - CalculateSuperiorLanePosition(Position.X).Y);
    float DistToCentral = FMath::Abs(Position.Y - CalculateCentralLanePosition(Position.X).Y);
    float DistToInferior = FMath::Abs(Position.Y - CalculateInferiorLanePosition(Position.X).Y);
    
    if (DistToSuperior <= DistToCentral && DistToSuperior <= DistToInferior)
    {
        return ELaneManagerType::Superior;
    }
    else if (DistToCentral <= DistToInferior)
    {
        return ELaneManagerType::Central;
    }
    else
    {
        return ELaneManagerType::Inferior;
    }
}

bool ALaneManager::IsPositionInRiver(const FVector& Position) const
{
    // Integração com o sistema de rio (será implementado no ARiverPrismalManager)
    // Por enquanto, assumir que o rio está na região central
    return FMath::Abs(Position.Y) <= 600.0f && FMath::Abs(Position.X) <= 4800.0f;
}

FVector ALaneManager::GetBridgePosition(ELaneManagerType LaneType) const
{
    // Pontes da lane central em X = ±600 UU
    if (LaneType == ELaneManagerType::Central)
    {
        // Retornar ponte mais próxima baseada na posição atual
        return FVector(0.0f, 0.0f, 0.0f); // Centro do mapa
    }
    
    return FVector::ZeroVector;
}

float ALaneManager::GetMovementSpeedModifier(const FVector& Position) const
{
    // Redução de velocidade no rio: 50%
    if (IsPositionInRiver(Position))
    {
        return 0.5f;
    }
    
    return 1.0f; // Velocidade normal
}

void ALaneManager::SetupLaneSplines()
{
    // Configurar spline da lane superior
    if (SuperiorLaneSpline)
    {
        SuperiorLaneSpline->ClearSplinePoints();
        for (float X = -7200.0f; X <= 7200.0f; X += 600.0f)
        {
            FVector Point = CalculateSuperiorLanePosition(X);
            SuperiorLaneSpline->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        SuperiorLaneSpline->UpdateSpline();
    }
    
    // Configurar spline da lane central
    if (CentralLaneSpline)
    {
        CentralLaneSpline->ClearSplinePoints();
        for (float X = -7200.0f; X <= 7200.0f; X += 600.0f)
        {
            FVector Point = CalculateCentralLanePosition(X);
            CentralLaneSpline->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        CentralLaneSpline->UpdateSpline();
    }
    
    // Configurar spline da lane inferior
    if (InferiorLaneSpline)
    {
        InferiorLaneSpline->ClearSplinePoints();
        for (float X = -7200.0f; X <= 7200.0f; X += 600.0f)
        {
            FVector Point = CalculateInferiorLanePosition(X);
            InferiorLaneSpline->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        InferiorLaneSpline->UpdateSpline();
    }
}

void ALaneManager::CalculateWaypointsForLane(ELaneManagerType LaneType)
{
    TArray<FVector> Waypoints;
    
    // Gerar 12 waypoints por lane com distâncias de 800-1200 UU
    for (int32 i = 0; i < 12; i++)
    {
        float Progress = static_cast<float>(i) / 11.0f; // 0.0 a 1.0
        float X = FMath::Lerp(-7200.0f, 7200.0f, Progress);
        
        FVector Waypoint;
        switch (LaneType)
        {
            case ELaneManagerType::Superior:
                Waypoint = CalculateSuperiorLanePosition(X);
                break;
            case ELaneManagerType::Central:
                Waypoint = CalculateCentralLanePosition(X);
                break;
            case ELaneManagerType::Inferior:
                Waypoint = CalculateInferiorLanePosition(X);
                break;
        }
        
        if (IsValidWaypointPosition(Waypoint))
        {
            Waypoints.Add(Waypoint);
        }
    }
    
    // Atualizar dados da lane
    int32 LaneIndex = static_cast<int32>(LaneType);
    if (LanesData.IsValidIndex(LaneIndex))
    {
        LanesData[LaneIndex].Waypoints = Waypoints;
    }
}

FVector ALaneManager::GetLaneDirection(ELaneManagerType LaneType, const FVector& Position) const
{
    switch (LaneType)
    {
        case ELaneManagerType::Superior:
            // Direção da lane superior (inclinação negativa)
            return FVector(1.0f, SUPERIOR_LANE_SLOPE, 0.0f).GetSafeNormal();
        case ELaneManagerType::Central:
            // Direção da lane central (horizontal)
            return FVector(1.0f, 0.0f, 0.0f);
        case ELaneManagerType::Inferior:
            // Direção da lane inferior (inclinação positiva)
            return FVector(1.0f, INFERIOR_LANE_SLOPE, 0.0f).GetSafeNormal();
        default:
            return FVector::ForwardVector;
    }
}

bool ALaneManager::IsValidWaypointPosition(const FVector& Position) const
{
    // Verificar se a posição está dentro dos limites do mapa
    return FMath::Abs(Position.X) <= PlayableArea / 2.0f && 
           FMath::Abs(Position.Y) <= PlayableArea / 2.0f;
}