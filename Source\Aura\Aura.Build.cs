// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class Aura : ModuleRules
{
	public Aura(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
	
		// Core UE5.6 modules
		PublicDependencyModuleNames.AddRange(new string[] { 
			"Core", 
			"CoreUObject", 
			"Engine", 
			"InputCore", 
			"EnhancedInput",
			"RenderCore",
			"RHI",
			"Renderer"
		});

		// UE5.6 Advanced Features
		PrivateDependencyModuleNames.AddRange(new string[] {
			// PCG (Procedural Content Generation) - Runtime only
			"PCG",

			// Editor modules (only available in editor builds)
			"StaticMeshEditor",
			"EditorSubsystem",
			"UnrealEd",
			"ToolMenus",

			// JSON and Serialization
			"Json",
			"JsonUtilities",

			// Nanite & Lumen - Runtime modules
			"Landscape",
			"MeshDescription",
			"StaticMeshDescription",
			"GeometryCore",
			"GeometryFramework",
			"GeometryScriptingCore",
			"DynamicMesh",

			// Physics & Collision
			"PhysicsCore",
			"Chaos",
			"ChaosSolverEngine",

			// UI & Slate
			"Slate",
			"SlateCore",
			"UMG",

			// Utilities
			"NavigationSystem",
			"AIModule",
			"GameplayTasks",
			"GameplayTags",
			"GameplayAbilities",
			"ProceduralMeshComponent",

			// Testing framework
			"AutomationController",
			"FunctionalTesting",

			// Niagara VFX System
			"Niagara",
			"NiagaraCore",
			"NiagaraShader"
		});

		// Enable modern C++ features for UE5.6
		CppStandard = CppStandardVersion.Cpp20;
		bUseUnity = false; // Disable unity builds for better debugging
		bEnableExceptions = true;
		bUseRTTI = true;
	}
}
