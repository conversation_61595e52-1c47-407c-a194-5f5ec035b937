#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture.h"
#include "Sound/SoundBase.h"
#include "Particles/ParticleSystem.h"

#include "Engine/AssetManager.h"
#include "Async/AsyncWork.h"
#include "Async/TaskGraphInterfaces.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Queue.h"
#include "Math/Vector.h"
#include "Math/Box.h"
#include "Math/Sphere.h"
#include "Templates/SharedPointer.h"
#include "UObject/SoftObjectPtr.h"
#include "UObject/WeakObjectPtr.h"
#include "APCGStreamingManager.generated.h"

// Forward Declarations
class UStaticMesh;
class UMaterialInterface;
class UTexture;
class USoundBase;
class UParticleSystem;
class ULevelStreaming;
struct FStreamableHandle;
class APCGWorldPartitionManager;
class APCGNaniteOptimizer;
class UPCGPerformanceProfiler;

// Enums
UENUM(BlueprintType)
enum class EPCGStreamingManagerPriority : uint8
{
    VeryLow     UMETA(DisplayName = "Very Low Priority"),
    Low         UMETA(DisplayName = "Low Priority"),
    Medium      UMETA(DisplayName = "Medium Priority"),
    High        UMETA(DisplayName = "High Priority"),
    Critical    UMETA(DisplayName = "Critical Priority")
};

UENUM(BlueprintType)
enum class EPCGStreamingState : uint8
{
    Unloaded        UMETA(DisplayName = "Unloaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Unloading       UMETA(DisplayName = "Unloading"),
    Failed          UMETA(DisplayName = "Failed"),
    Cached          UMETA(DisplayName = "Cached")
};

UENUM(BlueprintType)
enum class EPCGStreamingMode : uint8
{
    Distance        UMETA(DisplayName = "Distance Based"),
    Frustum         UMETA(DisplayName = "Frustum Based"),
    Hybrid          UMETA(DisplayName = "Hybrid Distance + Frustum"),
    Manual          UMETA(DisplayName = "Manual Control"),
    Adaptive        UMETA(DisplayName = "Adaptive Streaming")
};

UENUM(BlueprintType)
enum class EPCGStreamingAssetType : uint8
{
    StaticMesh      UMETA(DisplayName = "Static Mesh"),
    Material        UMETA(DisplayName = "Material"),
    Texture         UMETA(DisplayName = "Texture"),
    Sound           UMETA(DisplayName = "Sound"),
    ParticleSystem  UMETA(DisplayName = "Particle System"),
    Level           UMETA(DisplayName = "Level"),
    Blueprint       UMETA(DisplayName = "Blueprint"),
    Animation       UMETA(DisplayName = "Animation")
};

// Structures
USTRUCT(BlueprintType)
struct AURA_API FPCGStreamingConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    EPCGStreamingMode StreamingMode = EPCGStreamingMode::Hybrid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance", meta = (ClampMin = "100.0", ClampMax = "50000.0"))
    float LoadDistance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance", meta = (ClampMin = "50.0", ClampMax = "25000.0"))
    float UnloadDistance = 7500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "100"))
    int32 MaxConcurrentLoads = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1", ClampMax = "50"))
    int32 MaxConcurrentUnloads = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory", meta = (ClampMin = "100", ClampMax = "10000"))
    int32 MaxMemoryUsageMB = 2048;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache", meta = (ClampMin = "10", ClampMax = "1000"))
    int32 MaxCachedAssets = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncLoading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePreloading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableLODStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDebugInfo = false;

    FPCGStreamingConfig()
    {
        StreamingMode = EPCGStreamingMode::Hybrid;
        LoadDistance = 5000.0f;
        UnloadDistance = 7500.0f;
        MaxConcurrentLoads = 10;
        MaxConcurrentUnloads = 5;
        MaxMemoryUsageMB = 2048;
        MaxCachedAssets = 100;
        bEnableAsyncLoading = true;
        bEnablePreloading = true;
        bEnableLODStreaming = true;
        bShowDebugInfo = false;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGStreamingAsset
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    EPCGStreamingAssetType AssetType = EPCGStreamingAssetType::StaticMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    TSoftObjectPtr<UObject> AssetReference;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    FVector WorldPosition = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
    FBox BoundingBox = FBox(ForceInit);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    EPCGStreamingManagerPriority Priority = EPCGStreamingManagerPriority::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    EPCGStreamingState State = EPCGStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming", meta = (ClampMin = "0.0", ClampMax = "100000.0"))
    float CustomLoadDistance = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bIsEssential = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bCanBeUnloaded = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bPreloadOnStart = false;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Runtime")
    float LastAccessTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Runtime")
    int32 AccessCount = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Runtime")
    TWeakObjectPtr<UObject> LoadedAsset;

    FPCGStreamingAsset()
    {
        AssetType = EPCGStreamingAssetType::StaticMesh;
        AssetReference = nullptr;
        WorldPosition = FVector::ZeroVector;
        BoundingBox = FBox(ForceInit);
        Priority = EPCGStreamingManagerPriority::Medium;
        State = EPCGStreamingState::Unloaded;
        CustomLoadDistance = 0.0f;
        bIsEssential = false;
        bCanBeUnloaded = true;
        bPreloadOnStart = false;
        LastAccessTime = 0.0f;
        AccessCount = 0;
        LoadedAsset = nullptr;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGStreamingRegion
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    FString RegionName = TEXT("DefaultRegion");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    FBox RegionBounds = FBox(ForceInit);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    EPCGStreamingManagerPriority RegionPriority = EPCGStreamingManagerPriority::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Region")
    TArray<FPCGStreamingAsset> Assets;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming", meta = (ClampMin = "100.0", ClampMax = "50000.0"))
    float LoadDistance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming", meta = (ClampMin = "50.0", ClampMax = "25000.0"))
    float UnloadDistance = 7500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bAutoManaged = true;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Runtime")
    bool bIsLoaded = false;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Runtime")
    float LastUpdateTime = 0.0f;

    FPCGStreamingRegion()
    {
        RegionName = TEXT("DefaultRegion");
        RegionBounds = FBox(ForceInit);
        RegionPriority = EPCGStreamingManagerPriority::Medium;
        LoadDistance = 5000.0f;
        UnloadDistance = 7500.0f;
        bIsActive = true;
        bAutoManaged = true;
        bIsLoaded = false;
        LastUpdateTime = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGStreamingStats
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 TotalAssets = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 LoadedAssets = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 LoadingAssets = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 CachedAssets = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 FailedAssets = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Memory")
    int32 MemoryUsageMB = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Memory")
    int32 CacheMemoryMB = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float AverageLoadTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float StreamingEfficiency = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    int32 ActiveRegions = 0;

    FPCGStreamingStats()
    {
        TotalAssets = 0;
        LoadedAssets = 0;
        LoadingAssets = 0;
        CachedAssets = 0;
        FailedAssets = 0;
        MemoryUsageMB = 0;
        CacheMemoryMB = 0;
        AverageLoadTime = 0.0f;
        StreamingEfficiency = 0.0f;
        ActiveRegions = 0;
    }
};

// Delegates
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssetLoaded, const FPCGStreamingAsset&, Asset, UObject*, LoadedObject);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssetUnloaded, const FPCGStreamingAsset&, Asset);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssetLoadFailed, const FPCGStreamingAsset&, Asset, const FString&, ErrorMessage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRegionLoaded, const FString&, RegionName, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRegionUnloaded, const FString&, RegionName);

/**
 * APCGStreamingManager - Advanced streaming manager for procedural content generation
 * Provides intelligent asset streaming, memory management, and performance optimization
 * Optimized for UE5.6 with modern asset management and World Partition integration
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API APCGStreamingManager : public AActor
{
    GENERATED_BODY()

public:
    APCGStreamingManager();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    FPCGStreamingConfig StreamingConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceMonitoring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bAutoOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDebugVisualization = false;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Streaming Events")
    FOnAssetLoaded OnAssetLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Streaming Events")
    FOnAssetUnloaded OnAssetUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "Streaming Events")
    FOnAssetLoadFailed OnAssetLoadFailed;

    UPROPERTY(BlueprintAssignable, Category = "Streaming Events")
    FOnRegionLoaded OnRegionLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Streaming Events")
    FOnRegionUnloaded OnRegionUnloaded;

    // Asset Management
    UFUNCTION(BlueprintCallable, Category = "Streaming Assets")
    void RegisterAsset(const FPCGStreamingAsset& Asset);

    UFUNCTION(BlueprintCallable, Category = "Streaming Assets")
    void UnregisterAsset(const TSoftObjectPtr<UObject>& AssetReference);

    UFUNCTION(BlueprintCallable, Category = "Streaming Assets")
    void UpdateAssetPosition(const TSoftObjectPtr<UObject>& AssetReference, const FVector& NewPosition);

    UFUNCTION(BlueprintCallable, Category = "Streaming Assets")
    void SetAssetPriority(const TSoftObjectPtr<UObject>& AssetReference, EPCGStreamingManagerPriority Priority);

    UFUNCTION(BlueprintCallable, Category = "Streaming Assets")
    bool IsAssetLoaded(const TSoftObjectPtr<UObject>& AssetReference) const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Assets")
    UObject* GetLoadedAsset(const TSoftObjectPtr<UObject>& AssetReference) const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Assets")
    TArray<FPCGStreamingAsset> GetAllAssets() const;

    // Region Management
    UFUNCTION(BlueprintCallable, Category = "Streaming Regions")
    void CreateRegion(const FPCGStreamingRegion& Region);

    UFUNCTION(BlueprintCallable, Category = "Streaming Regions")
    void RemoveRegion(const FString& RegionName);

    UFUNCTION(BlueprintCallable, Category = "Streaming Regions")
    void UpdateRegion(const FString& RegionName, const FPCGStreamingRegion& UpdatedRegion);

    UFUNCTION(BlueprintCallable, Category = "Streaming Regions")
    void LoadRegion(const FString& RegionName);

    UFUNCTION(BlueprintCallable, Category = "Streaming Regions")
    void UnloadRegion(const FString& RegionName);

    UFUNCTION(BlueprintCallable, Category = "Streaming Regions")
    bool IsRegionLoaded(const FString& RegionName) const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Regions")
    TArray<FPCGStreamingRegion> GetAllRegions() const;

    // Manual Control
    UFUNCTION(BlueprintCallable, Category = "Streaming Control")
    void ForceLoadAsset(const TSoftObjectPtr<UObject>& AssetReference);

    UFUNCTION(BlueprintCallable, Category = "Streaming Control")
    void ForceUnloadAsset(const TSoftObjectPtr<UObject>& AssetReference);

    UFUNCTION(BlueprintCallable, Category = "Streaming Control")
    void PreloadAssetsInRadius(const FVector& Center, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Streaming Control")
    void UnloadAssetsOutsideRadius(const FVector& Center, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Streaming Control")
    void SetStreamingMode(EPCGStreamingMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Streaming Control")
    void PauseStreaming(bool bPause);

    // Performance Management
    UFUNCTION(BlueprintCallable, Category = "Streaming Performance")
    FPCGStreamingStats GetStreamingStats() const;

    UFUNCTION(BlueprintCallable, Category = "Streaming Performance")
    void OptimizeMemoryUsage();

    UFUNCTION(BlueprintCallable, Category = "Streaming Performance")
    void ClearCache();

    UFUNCTION(BlueprintCallable, Category = "Streaming Performance")
    void SetMaxMemoryUsage(int32 MaxMemoryMB);

    UFUNCTION(BlueprintCallable, Category = "Streaming Performance")
    void SetMaxConcurrentLoads(int32 MaxLoads);

    UFUNCTION(BlueprintCallable, Category = "Streaming Performance")
    float GetStreamingEfficiency() const;

    // Integration with other systems
    UFUNCTION(BlueprintCallable, Category = "Streaming Integration")
    void IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager);

    UFUNCTION(BlueprintCallable, Category = "Streaming Integration")
    void IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer);

    UFUNCTION(BlueprintCallable, Category = "Streaming Integration")
    void IntegrateWithPerformanceProfiler(UPCGPerformanceProfiler* PerformanceProfiler);

    UFUNCTION(BlueprintCallable, Category = "Streaming Integration")
    void SynchronizeWithPCGSystem();

    // Viewer Management
    UFUNCTION(BlueprintCallable, Category = "Streaming Viewers")
    void AddStreamingViewer(AActor* Viewer, float ViewDistance = 5000.0f);

    UFUNCTION(BlueprintCallable, Category = "Streaming Viewers")
    void RemoveStreamingViewer(AActor* Viewer);

    UFUNCTION(BlueprintCallable, Category = "Streaming Viewers")
    void UpdateViewerPosition(AActor* Viewer, const FVector& Position);

    UFUNCTION(BlueprintCallable, Category = "Streaming Viewers")
    TArray<AActor*> GetActiveViewers() const;

private:
    // Internal data
    UPROPERTY()
    TMap<TSoftObjectPtr<UObject>, FPCGStreamingAsset> RegisteredAssets;

    UPROPERTY()
    TMap<FString, FPCGStreamingRegion> StreamingRegions;

    UPROPERTY()
    TMap<TWeakObjectPtr<AActor>, float> StreamingViewers;

    UPROPERTY()
    FPCGStreamingStats CurrentStats;

    // Internal state
    FThreadSafeBool bIsInitialized;
    FThreadSafeBool bIsPaused;
    FThreadSafeBool bNeedsUpdate;
    float LastUpdateTime;
    float AccumulatedDeltaTime;

    // Async loading
    TMap<TSoftObjectPtr<UObject>, TSharedPtr<FStreamableHandle>> ActiveLoadHandles;
    TQueue<TSoftObjectPtr<UObject>> LoadQueue;
    TQueue<TSoftObjectPtr<UObject>> UnloadQueue;
    TSet<TSoftObjectPtr<UObject>> CurrentlyLoading;
    TSet<TSoftObjectPtr<UObject>> CurrentlyUnloading;

    // Cache management
    TMap<TSoftObjectPtr<UObject>, TWeakObjectPtr<UObject>> AssetCache;
    TArray<TSoftObjectPtr<UObject>> CacheAccessOrder;
    int32 CurrentCacheSize;

    // Integration references
    UPROPERTY()
    TWeakObjectPtr<APCGWorldPartitionManager> WorldPartitionManagerRef;

    UPROPERTY()
    TWeakObjectPtr<APCGNaniteOptimizer> NaniteOptimizerRef;

    UPROPERTY()
    TWeakObjectPtr<UPCGPerformanceProfiler> PerformanceProfilerRef;

    // Internal functions
    void InitializeStreamingSystem();
    void UpdateStreamingSystem(float DeltaTime);
    void ProcessLoadQueue();
    void ProcessUnloadQueue();
    void UpdateAssetStates();
    void UpdateRegionStates();
    void UpdatePerformanceStats();
    void ProcessViewerBasedStreaming();
    void ProcessDistanceBasedStreaming();
    void ProcessFrustumBasedStreaming();
    void ProcessAdaptiveStreaming();

    // Asset loading/unloading
    void StartAssetLoad(const TSoftObjectPtr<UObject>& AssetReference);
    void CompleteAssetLoad(const TSoftObjectPtr<UObject>& AssetReference, UObject* LoadedObject);
    void StartAssetUnload(const TSoftObjectPtr<UObject>& AssetReference);
    void CompleteAssetUnload(const TSoftObjectPtr<UObject>& AssetReference);
    void HandleLoadFailure(const TSoftObjectPtr<UObject>& AssetReference, const FString& ErrorMessage);

    // Cache management
    void AddToCache(const TSoftObjectPtr<UObject>& AssetReference, UObject* Asset);
    void RemoveFromCache(const TSoftObjectPtr<UObject>& AssetReference);
    void OptimizeCache();
    void ClearOldCacheEntries();
    UObject* GetFromCache(const TSoftObjectPtr<UObject>& AssetReference);

    // Distance calculations
    float CalculateDistanceToAsset(const FPCGStreamingAsset& Asset) const;
    float CalculateDistanceToRegion(const FPCGStreamingRegion& Region) const;
    bool IsAssetInViewerRange(const FPCGStreamingAsset& Asset) const;
    bool IsRegionInViewerRange(const FPCGStreamingRegion& Region) const;

    // Memory management
    void CheckMemoryUsage();
    void FreeMemoryIfNeeded();
    int32 CalculateAssetMemoryUsage(UObject* Asset) const;
    void UnloadLeastRecentlyUsedAssets(int32 TargetMemoryMB);

    // Utility functions
    void CleanupInvalidReferences();
    void ValidateStreamingData();
    void UpdateAssetAccessTime(const TSoftObjectPtr<UObject>& AssetReference);
    EPCGStreamingManagerPriority CalculateAssetPriority(const FPCGStreamingAsset& Asset) const;

    // Debug and visualization
    void DrawDebugInfo();
    void LogStreamingStats();

    // Event handlers
    UFUNCTION()
    void OnAssetLoadCompleted();

    UFUNCTION()
    void HandleAssetLoadFailure(const TSoftObjectPtr<UObject>& AssetReference);

    // Async operations
    void StartAsyncStreamingUpdate();
    void CompleteAsyncStreamingUpdate();
};