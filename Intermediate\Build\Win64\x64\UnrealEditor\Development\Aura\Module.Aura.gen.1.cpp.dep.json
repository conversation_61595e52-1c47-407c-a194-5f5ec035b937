{"Version": "1.2", "Data": {"Source": "c:\\aura\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\aura\\module.aura.gen.1.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\auraeditor\\development\\unrealed\\sharedpch.unrealed.project.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\aura\\definitions.aura.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\abaronauracronmanager.gen.cpp", "c:\\aura\\source\\aura\\public\\abaronauracronmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\abaronauracronmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\damageevents.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\damageevents.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\adragonprismalmanager.gen.cpp", "c:\\aura\\source\\aura\\public\\adragonprismalmanager.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\adragonprismalmanager.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\ageometricvalidator.gen.cpp", "c:\\aura\\source\\aura\\public\\ageometricvalidator.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\ageometricvalidator.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}