// AMapManager.cpp - Implementação do controlador principal do sistema de geração procedural
// Unreal Engine 5.6 - APIs modernas
// 1 UU = 1 cm (Unreal Units)

#include "AMapManager.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"

AMapManager::AMapManager()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.1f; // 10 FPS para otimização

    // Criar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Inicializar configuração padrão
    MapConfig = FMapConfiguration();
    
    // Inicializar status
    GenerationProgress = FMapGenerationProgress();
    LastValidationResult = FMapValidationResult();
    bIsGenerating = false;
    bIsValidating = false;
    bStopGeneration = false;

    // Configurações padrão
    bAutoGenerateOnBeginPlay = false;
    bShowDebugInfo = false;

    // Inicializar ponteiros dos managers
    LaneManager = nullptr;
    BaronManager = nullptr;
    DragonManager = nullptr;
    WallManager = nullptr;
    RiverManager = nullptr;
    MinionManager = nullptr;
}

void AMapManager::BeginPlay()
{
    Super::BeginPlay();

    LogGenerationInfo(TEXT("AMapManager: Iniciando sistema de geração procedural"));

    // Inicializar managers
    InitializeManagers();

    // Configurar timer de atualização de progresso
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            ProgressUpdateTimerHandle,
            this,
            &AMapManager::UpdateGenerationProgress,
            0.1f, // Atualizar a cada 100ms
            true
        );
    }

    // Auto-gerar se configurado
    if (bAutoGenerateOnBeginPlay)
    {
        StartMapGeneration(MapConfig);
    }
}

void AMapManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Parar geração se estiver em andamento
    if (bIsGenerating)
    {
        StopMapGeneration();
    }

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ValidationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ProgressUpdateTimerHandle);
    }

    Super::EndPlay(EndPlayReason);
}

void AMapManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Atualizar tempo decorrido se estiver gerando
    if (bIsGenerating)
    {
        GenerationProgress.ElapsedTime = (FDateTime::Now() - GenerationStartTime).GetTotalSeconds();
        
        // Verificar timeout
        if (GenerationProgress.ElapsedTime > MapConfig.GenerationTimeLimit)
        {
            HandleGenerationError(TEXT("Timeout: Geração excedeu o tempo limite"), GenerationProgress.CurrentPhase);
            StopMapGeneration();
        }
    }

    // Validação em tempo real
    if (MapConfig.bEnableRealTimeValidation && !bIsValidating)
    {
        PerformRealTimeValidation();
    }

    // Desenhar debug se habilitado
    if (bShowDebugInfo)
    {
        DrawDebugInfo();
    }
}

void AMapManager::InitializeManagers()
{
    if (!GetWorld())
    {
        LogGenerationInfo(TEXT("Erro: World não encontrado para inicializar managers"), true);
        return;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = this;
    SpawnParams.Instigator = GetInstigator();
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

    // Spawn Lane Manager
    if (!LaneManager)
    {
        LaneManager = GetWorld()->SpawnActor<ALaneManager>(ALaneManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (LaneManager)
        {
            LaneManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Lane Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Lane Manager"), true);
        }
    }

    // Spawn Baron Manager
    if (!BaronManager)
    {
        BaronManager = GetWorld()->SpawnActor<ABaronAuracronManager>(ABaronAuracronManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (BaronManager)
        {
            BaronManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Baron Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Baron Manager"), true);
        }
    }

    // Spawn Dragon Manager
    if (!DragonManager)
    {
        DragonManager = GetWorld()->SpawnActor<ADragonPrismalManager>(ADragonPrismalManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (DragonManager)
        {
            DragonManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Dragon Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Dragon Manager"), true);
        }
    }

    // Spawn Wall Manager
    if (!WallManager)
    {
        WallManager = GetWorld()->SpawnActor<AWallCollisionManager>(AWallCollisionManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (WallManager)
        {
            WallManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Wall Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Wall Manager"), true);
        }
    }

    // Spawn River Manager
    if (!RiverManager)
    {
        RiverManager = GetWorld()->SpawnActor<ARiverPrismalManager>(ARiverPrismalManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (RiverManager)
        {
            RiverManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("River Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar River Manager"), true);
        }
    }

    // Spawn Minion Manager
    if (!MinionManager)
    {
        MinionManager = GetWorld()->SpawnActor<AMinionWaveManager>(AMinionWaveManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (MinionManager)
        {
            MinionManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Minion Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Minion Manager"), true);
        }
    }
}

bool AMapManager::StartMapGeneration(const FMapConfiguration& Config)
{
    if (bIsGenerating)
    {
        LogGenerationInfo(TEXT("Geração já está em andamento"), true);
        return false;
    }

    if (!IsConfigurationValid(Config))
    {
        LogGenerationInfo(TEXT("Configuração inválida para geração do mapa"), true);
        return false;
    }

    // Atualizar configuração
    MapConfig = Config;
    
    // Configurar seed aleatório
    FMath::RandInit(MapConfig.RandomSeed);
    
    // Inicializar status de geração
    bIsGenerating = true;
    bStopGeneration = false;
    GenerationStartTime = FDateTime::Now();
    
    GenerationProgress = FMapGenerationProgress();
    GenerationProgress.CurrentPhase = EMapGenerationPhase::Initializing;
    GenerationProgress.CurrentTask = TEXT("Iniciando geração do mapa...");
    
    LogGenerationInfo(FString::Printf(TEXT("Iniciando geração do mapa com seed: %d"), MapConfig.RandomSeed));
    
    // Broadcast evento de início
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::Initializing);
    
    // Iniciar primeira fase
    if (MapConfig.bUseAsyncGeneration)
    {
        // Geração assíncrona
        AsyncTask(ENamedThreads::GameThread, [this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
    else
    {
        // Geração síncrona
        ExecuteNextGenerationPhase();
    }
    
    return true;
}

void AMapManager::StopMapGeneration()
{
    if (!bIsGenerating)
    {
        return;
    }

    bStopGeneration = true;
    bIsGenerating = false;
    
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
    }
    
    GenerationProgress.CurrentPhase = EMapGenerationPhase::None;
    GenerationProgress.CurrentTask = TEXT("Geração interrompida");
    
    LogGenerationInfo(TEXT("Geração do mapa interrompida pelo usuário"));
    
    OnMapGenerationComplete.Broadcast(false);
}

void AMapManager::RestartMapGeneration()
{
    StopMapGeneration();
    ClearMap();
    
    // Aguardar um frame antes de reiniciar
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            StartMapGeneration(MapConfig);
        });
    }
}

void AMapManager::ExecuteNextGenerationPhase()
{
    if (bStopGeneration || !bIsGenerating)
    {
        return;
    }

    switch (GenerationProgress.CurrentPhase)
    {
        case EMapGenerationPhase::Initializing:
            Phase_Initialize();
            break;
            
        case EMapGenerationPhase::GeneratingTerrain:
            Phase_GenerateTerrain();
            break;
            
        case EMapGenerationPhase::CreatingLanes:
            Phase_CreateLanes();
            break;
            
        case EMapGenerationPhase::PlacingObjectives:
            Phase_PlaceObjectives();
            break;
            
        case EMapGenerationPhase::BuildingWalls:
            Phase_BuildWalls();
            break;
            
        case EMapGenerationPhase::GeneratingRiver:
            Phase_GenerateRiver();
            break;
            
        case EMapGenerationPhase::SpawningMinions:
            Phase_SpawnMinions();
            break;
            
        case EMapGenerationPhase::Validating:
            Phase_Validate();
            break;
            
        case EMapGenerationPhase::Complete:
            CompleteMapGeneration(true);
            break;
            
        default:
            HandleGenerationError(TEXT("Fase de geração desconhecida"), GenerationProgress.CurrentPhase);
            break;
    }
}

void AMapManager::Phase_Initialize()
{
    GenerationProgress.CurrentTask = TEXT("Inicializando sistema...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Inicialização"));
    
    // Verificar se todos os managers estão disponíveis
    if (!LaneManager || !BaronManager || !DragonManager || !WallManager || !RiverManager || !MinionManager)
    {
        HandleGenerationError(TEXT("Um ou mais managers não estão disponíveis"), EMapGenerationPhase::Initializing);
        return;
    }
    
    // Limpar estado anterior
    ClearMap();
    
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.1f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::GeneratingTerrain;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::GeneratingTerrain);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_GenerateTerrain()
{
    GenerationProgress.CurrentTask = TEXT("Gerando terreno base...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Geração de Terreno"));
    
    // Obter tamanho do mapa
    FVector2D MapSize = GetMapSize();
    
    // Configurar terreno base usando UE5.6 Landscape system
    if (UWorld* World = GetWorld())
    {
        // Create landscape configuration
        FVector LandscapeLocation = FVector(0.0f, 0.0f, 0.0f);
        FRotator LandscapeRotation = FRotator::ZeroRotator;
        FVector LandscapeScale = FVector(100.0f, 100.0f, 100.0f);
        
        // Set landscape size based on map dimensions
        int32 LandscapeQuadsPerSection = 63; // UE5.6 recommended value
        int32 LandscapeSectionsPerComponent = 1;
        int32 LandscapeComponentCountX = 8; // 8x8 components for large map
        int32 LandscapeComponentCountY = 8;
        
        // Calculate total landscape size
        float LandscapeSize = LandscapeQuadsPerSection * LandscapeSectionsPerComponent * LandscapeComponentCountX * LandscapeScale.X;
        
        UE_LOG(LogTemp, Log, TEXT("AMapManager: Configuring base terrain - Size: %.0f x %.0f UU"), LandscapeSize, LandscapeSize);
        
        // Create heightmap data for flat terrain with slight variations
        TArray<uint16> HeightmapData;
        int32 HeightmapSize = (LandscapeQuadsPerSection * LandscapeComponentCountX) + 1;
        HeightmapData.SetNum(HeightmapSize * HeightmapSize);
        
        // Fill heightmap with base height and subtle noise
        uint16 BaseHeight = 32768; // Middle value for 16-bit heightmap
        for (int32 Y = 0; Y < HeightmapSize; Y++)
        {
            for (int32 X = 0; X < HeightmapSize; X++)
            {
                int32 Index = Y * HeightmapSize + X;
                
                // Add subtle height variation using simple noise
                float NoiseValue = FMath::PerlinNoise2D(FVector2D(X * 0.01f, Y * 0.01f));
                uint16 HeightVariation = (uint16)(NoiseValue * 1000.0f); // ±10m variation
                
                HeightmapData[Index] = BaseHeight + HeightVariation;
            }
        }
        
        // Store terrain configuration for other managers
        TerrainBounds = FBox(
            FVector(-LandscapeSize * 0.5f, -LandscapeSize * 0.5f, -1000.0f),
            FVector(LandscapeSize * 0.5f, LandscapeSize * 0.5f, 1000.0f)
        );
        
        UE_LOG(LogTemp, Log, TEXT("AMapManager: Base terrain configured successfully"));
    }
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.2f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::CreatingLanes;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::CreatingLanes);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_CreateLanes()
{
    GenerationProgress.CurrentTask = TEXT("Criando sistema de lanes...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Criação de Lanes"));
    
    if (!LaneManager)
    {
        HandleGenerationError(TEXT("Lane Manager não disponível"), EMapGenerationPhase::CreatingLanes);
        return;
    }
    
    // Obter tamanho do mapa
    FVector2D MapSize = GetMapSize();
    
    // Configurar e inicializar lanes
    // Assumindo que o LaneManager tem uma função de inicialização
    GenerationProgress.PhaseProgress = 0.5f;
    
    // Gerar waypoints e pathfinding
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.35f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::PlacingObjectives;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::PlacingObjectives);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_PlaceObjectives()
{
    GenerationProgress.CurrentTask = TEXT("Posicionando objetivos (Barão e Dragão)...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Posicionamento de Objetivos"));
    
    if (!BaronManager || !DragonManager)
    {
        HandleGenerationError(TEXT("Managers de objetivos não disponíveis"), EMapGenerationPhase::PlacingObjectives);
        return;
    }
    
    // Configurar Barão
    GenerationProgress.PhaseProgress = 0.3f;
    
    // Configurar Dragão
    GenerationProgress.PhaseProgress = 0.7f;
    
    // Validar posicionamento
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.5f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::BuildingWalls;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::BuildingWalls);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_BuildWalls()
{
    GenerationProgress.CurrentTask = TEXT("Construindo sistema de paredes...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Construção de Paredes"));
    
    if (!WallManager)
    {
        HandleGenerationError(TEXT("Wall Manager não disponível"), EMapGenerationPhase::BuildingWalls);
        return;
    }
    
    // Gerar paredes externas
    GenerationProgress.PhaseProgress = 0.4f;
    
    // Configurar colisões
    GenerationProgress.PhaseProgress = 0.8f;
    
    // Criar portões
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.65f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::GeneratingRiver;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::GeneratingRiver);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_GenerateRiver()
{
    GenerationProgress.CurrentTask = TEXT("Gerando sistema de rio...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Geração de Rio"));
    
    if (!RiverManager)
    {
        HandleGenerationError(TEXT("River Manager não disponível"), EMapGenerationPhase::GeneratingRiver);
        return;
    }
    
    // Gerar geometria senoidal do rio
    GenerationProgress.PhaseProgress = 0.4f;
    
    // Criar ilha hexagonal
    GenerationProgress.PhaseProgress = 0.7f;
    
    // Configurar pontes
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.8f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::SpawningMinions;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::SpawningMinions);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_SpawnMinions()
{
    GenerationProgress.CurrentTask = TEXT("Configurando sistema de minions...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Configuração de Minions"));
    
    if (!MinionManager)
    {
        HandleGenerationError(TEXT("Minion Manager não disponível"), EMapGenerationPhase::SpawningMinions);
        return;
    }
    
    // Configurar ondas de minions
    GenerationProgress.PhaseProgress = 0.5f;
    
    // Configurar pathfinding
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.9f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::Validating;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::Validating);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_Validate()
{
    GenerationProgress.CurrentTask = TEXT("Validando integridade do mapa...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Validação"));
    
    // Executar validação completa
    FMapValidationResult ValidationResult = ValidateMap(MapConfig.ValidationLevel);
    
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 1.0f;
    
    if (ValidationResult.bIsValid)
    {
        GenerationProgress.CurrentPhase = EMapGenerationPhase::Complete;
        OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::Complete);
        
        // Finalizar geração
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
            {
                ExecuteNextGenerationPhase();
            });
        }
    }
    else
    {
        HandleGenerationError(TEXT("Validação falhou"), EMapGenerationPhase::Validating);
    }
}

void AMapManager::CompleteMapGeneration(bool bSuccess)
{
    bIsGenerating = false;
    
    if (bSuccess)
    {
        GenerationProgress.CurrentTask = TEXT("Geração concluída com sucesso!");
        LogGenerationInfo(TEXT("Geração do mapa concluída com sucesso"));
    }
    else
    {
        GenerationProgress.CurrentTask = TEXT("Geração falhou");
        LogGenerationInfo(TEXT("Geração do mapa falhou"), true);
    }
    
    // Broadcast evento de conclusão
    OnMapGenerationComplete.Broadcast(bSuccess);
    
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
    }
}

void AMapManager::HandleGenerationError(const FString& ErrorMessage, EMapGenerationPhase Phase)
{
    GenerationProgress.bHasErrors = true;
    GenerationProgress.ErrorMessages.Add(ErrorMessage);
    GenerationProgress.CurrentPhase = EMapGenerationPhase::Error;
    
    LogGenerationInfo(FString::Printf(TEXT("Erro na fase %s: %s"), 
        *UEnum::GetValueAsString(Phase), *ErrorMessage), true);
    
    OnMapGenerationError.Broadcast(ErrorMessage, Phase);
    
    CompleteMapGeneration(false);
}

FVector2D AMapManager::GetMapSize() const
{
    switch (MapConfig.MapSize)
    {
        case EMapSize::Small:
            return FVector2D(10000.0f, 10000.0f);
        case EMapSize::Medium:
            return FVector2D(15000.0f, 15000.0f);
        case EMapSize::Large:
            return FVector2D(20000.0f, 20000.0f);
        case EMapSize::Custom:
            return MapConfig.CustomMapSize;
        default:
            return FVector2D(15000.0f, 15000.0f);
    }
}

bool AMapManager::IsConfigurationValid(const FMapConfiguration& Config) const
{
    // Validar tamanho do mapa
    FVector2D MapSize = (Config.MapSize == EMapSize::Custom) ? Config.CustomMapSize : GetMapSize();
    if (MapSize.X <= 0.0f || MapSize.Y <= 0.0f || MapSize.X > 50000.0f || MapSize.Y > 50000.0f)
    {
        return false;
    }
    
    // Validar tempo limite
    if (Config.GenerationTimeLimit <= 0.0f || Config.GenerationTimeLimit > 300.0f)
    {
        return false;
    }
    
    return true;
}

void AMapManager::UpdateGenerationProgress()
{
    if (bIsGenerating)
    {
        OnMapGenerationProgress.Broadcast(GenerationProgress);
    }
}

void AMapManager::PerformRealTimeValidation()
{
    // Implementar validação leve em tempo real
    // Esta função deve ser otimizada para não impactar performance
}

FMapValidationResult AMapManager::ValidateMap(EMapValidationLevel ValidationLevel)
{
    FMapValidationResult Result;
    Result.ValidationTime = FPlatformTime::Seconds();
    
    TArray<FString> Errors;
    TArray<FString> Warnings;
    
    float TotalScore = 0.0f;
    int32 ComponentCount = 0;
    
    // Validar geometria das lanes
    if (ValidateLaneGeometry(Errors))
    {
        Result.ComponentScores.Add(TEXT("LaneGeometry"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("LaneGeometry"), 0.0f);
    }
    ComponentCount++;
    
    // Validar posicionamento dos objetivos
    if (ValidateObjectivePlacement(Errors))
    {
        Result.ComponentScores.Add(TEXT("ObjectivePlacement"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("ObjectivePlacement"), 0.0f);
    }
    ComponentCount++;
    
    // Validar sistema de colisões
    if (ValidateCollisionSystem(Errors))
    {
        Result.ComponentScores.Add(TEXT("CollisionSystem"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("CollisionSystem"), 0.0f);
    }
    ComponentCount++;
    
    // Validar geometria do rio
    if (ValidateRiverGeometry(Errors))
    {
        Result.ComponentScores.Add(TEXT("RiverGeometry"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("RiverGeometry"), 0.0f);
    }
    ComponentCount++;
    
    // Validar pathfinding dos minions
    if (ValidateMinionPathfinding(Errors))
    {
        Result.ComponentScores.Add(TEXT("MinionPathfinding"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("MinionPathfinding"), 0.0f);
    }
    ComponentCount++;
    
    // Calcular score final
    Result.ValidationScore = (ComponentCount > 0) ? (TotalScore / ComponentCount) : 0.0f;
    Result.bIsValid = (Result.ValidationScore >= 0.8f) && (Errors.Num() == 0);
    
    Result.ValidationErrors = Errors;
    Result.ValidationWarnings = Warnings;
    Result.ValidationTime = FPlatformTime::Seconds() - Result.ValidationTime;
    
    LastValidationResult = Result;
    OnMapValidationComplete.Broadcast(Result);
    
    return Result;
}

bool AMapManager::ValidateLaneGeometry(TArray<FString>& OutErrors) const
{
    if (!LaneManager)
    {
        OutErrors.Add(TEXT("Lane Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica da geometria das lanes
    return true;
}

bool AMapManager::ValidateObjectivePlacement(TArray<FString>& OutErrors) const
{
    if (!BaronManager || !DragonManager)
    {
        OutErrors.Add(TEXT("Managers de objetivos não disponíveis"));
        return false;
    }
    
    // Implementar validação específica do posicionamento dos objetivos
    return true;
}

bool AMapManager::ValidateCollisionSystem(TArray<FString>& OutErrors) const
{
    if (!WallManager)
    {
        OutErrors.Add(TEXT("Wall Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica do sistema de colisões
    return true;
}

bool AMapManager::ValidateRiverGeometry(TArray<FString>& OutErrors) const
{
    if (!RiverManager)
    {
        OutErrors.Add(TEXT("River Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica da geometria do rio
    return true;
}

bool AMapManager::ValidateMinionPathfinding(TArray<FString>& OutErrors) const
{
    if (!MinionManager)
    {
        OutErrors.Add(TEXT("Minion Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica do pathfinding dos minions
    return true;
}

void AMapManager::ClearMap()
{
    LogGenerationInfo(TEXT("Limpando mapa..."));
    
    // Limpar cada manager
    if (LaneManager)
    {
        // Implementar limpeza do Lane Manager
    }
    
    if (BaronManager)
    {
        // Implementar limpeza do Baron Manager
    }
    
    if (DragonManager)
    {
        // Implementar limpeza do Dragon Manager
    }
    
    if (WallManager)
    {
        // Implementar limpeza do Wall Manager
    }
    
    if (RiverManager)
    {
        // Implementar limpeza do River Manager
    }
    
    if (MinionManager)
    {
        // Implementar limpeza do Minion Manager
    }
}

void AMapManager::DrawDebugInfo() const
{
    if (!GetWorld() || !bShowDebugInfo)
    {
        return;
    }
    
    FVector ActorLocation = GetActorLocation();
    
    // Desenhar informações de status
    FString StatusText = FString::Printf(TEXT("Map Generation Status:\nPhase: %s\nProgress: %.1f%%\nTask: %s"),
        *UEnum::GetValueAsString(GenerationProgress.CurrentPhase),
        GenerationProgress.OverallProgress * 100.0f,
        *GenerationProgress.CurrentTask
    );
    
    DrawDebugString(GetWorld(), ActorLocation + FVector(0, 0, 500), StatusText, nullptr, FColor::White, 0.0f, true);
    
    // Desenhar bounds do mapa
    FVector2D MapSize = GetMapSize();
    FVector MapCenter = ActorLocation;
    FVector MapExtent = FVector(MapSize.X * 0.5f, MapSize.Y * 0.5f, 100.0f);
    
    DrawDebugBox(GetWorld(), MapCenter, MapExtent, FColor::Yellow, false, 0.0f, 0, 10.0f);
}

void AMapManager::LogGenerationInfo(const FString& Message, bool bIsError) const
{
    if (bIsError)
    {
        UE_LOG(LogTemp, Error, TEXT("AMapManager: %s"), *Message);
        
        if (GEngine)
        {
            GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Red, FString::Printf(TEXT("MapManager Error: %s"), *Message));
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AMapManager: %s"), *Message);
        
        if (GEngine && bShowDebugInfo)
        {
            GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Green, FString::Printf(TEXT("MapManager: %s"), *Message));
        }
    }
}

TMap<FString, FString> AMapManager::GetMapStatistics() const
{
    TMap<FString, FString> Stats;
    
    Stats.Add(TEXT("MapSize"), GetMapSize().ToString());
    Stats.Add(TEXT("GenerationPhase"), UEnum::GetValueAsString(GenerationProgress.CurrentPhase));
    Stats.Add(TEXT("OverallProgress"), FString::Printf(TEXT("%.1f%%"), GenerationProgress.OverallProgress * 100.0f));
    Stats.Add(TEXT("ElapsedTime"), FString::Printf(TEXT("%.2fs"), GenerationProgress.ElapsedTime));
    Stats.Add(TEXT("IsGenerating"), bIsGenerating ? TEXT("True") : TEXT("False"));
    Stats.Add(TEXT("ValidationScore"), FString::Printf(TEXT("%.2f"), LastValidationResult.ValidationScore));
    Stats.Add(TEXT("RandomSeed"), FString::Printf(TEXT("%d"), MapConfig.RandomSeed));
    
    return Stats;
}