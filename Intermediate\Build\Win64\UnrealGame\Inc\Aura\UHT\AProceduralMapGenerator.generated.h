// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AProceduralMapGenerator.h"

#ifdef AURA_AProceduralMapGenerator_generated_h
#error "AProceduralMapGenerator.generated.h already included, missing '#pragma once' in AProceduralMapGenerator.h"
#endif
#define AURA_AProceduralMapGenerator_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EPCGGenerationPhase : uint8;
struct FPCGGenerationConfig;
struct FPCGGenerationProgress;
struct FPCGSpawnRule;

// ********** Begin ScriptStruct FPCGAssetReference ************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_82_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGAssetReference_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGAssetReference;
// ********** End ScriptStruct FPCGAssetReference **************************************************

// ********** Begin ScriptStruct FPCGSpawnRule *****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_131_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGSpawnRule_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGSpawnRule;
// ********** End ScriptStruct FPCGSpawnRule *******************************************************

// ********** Begin ScriptStruct FPCGGenerationConfig **********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_190_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGGenerationConfig;
// ********** End ScriptStruct FPCGGenerationConfig ************************************************

// ********** Begin ScriptStruct FPCGGenerationProgress ********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_240_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGGenerationProgress;
// ********** End ScriptStruct FPCGGenerationProgress **********************************************

// ********** Begin Delegate FOnPCGPhaseChanged ****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_287_DELEGATE \
AURA_API void FOnPCGPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPCGPhaseChanged, EPCGGenerationPhase NewPhase);


// ********** End Delegate FOnPCGPhaseChanged ******************************************************

// ********** Begin Delegate FOnPCGProgressUpdated *************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_288_DELEGATE \
AURA_API void FOnPCGProgressUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnPCGProgressUpdated, float Progress);


// ********** End Delegate FOnPCGProgressUpdated ***************************************************

// ********** Begin Delegate FOnPCGGenerationCompleted *********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_289_DELEGATE \
AURA_API void FOnPCGGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationCompleted);


// ********** End Delegate FOnPCGGenerationCompleted ***********************************************

// ********** Begin Delegate FOnPCGGenerationFailed ************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_290_DELEGATE \
AURA_API void FOnPCGGenerationFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationFailed, const FString& ErrorMessage);


// ********** End Delegate FOnPCGGenerationFailed **************************************************

// ********** Begin Delegate FOnPCGAssetGenerated **************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_291_DELEGATE \
AURA_API void FOnPCGAssetGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnPCGAssetGenerated, const FString& AssetName, int32 Count);


// ********** End Delegate FOnPCGAssetGenerated ****************************************************

// ********** Begin Class AProceduralMapGenerator **************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_298_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdatePCGFromGeometry); \
	DECLARE_FUNCTION(execSynchronizeWithMapManager); \
	DECLARE_FUNCTION(execValidateManagerReferences); \
	DECLARE_FUNCTION(execInitializeManagers); \
	DECLARE_FUNCTION(execGetGeneratedAssetCount); \
	DECLARE_FUNCTION(execGetOverallProgress); \
	DECLARE_FUNCTION(execGetCurrentPhase); \
	DECLARE_FUNCTION(execIsPaused); \
	DECLARE_FUNCTION(execIsGenerating); \
	DECLARE_FUNCTION(execGetGenerationProgress); \
	DECLARE_FUNCTION(execClearSpawnRules); \
	DECLARE_FUNCTION(execRemoveSpawnRule); \
	DECLARE_FUNCTION(execAddSpawnRule); \
	DECLARE_FUNCTION(execGetGenerationConfig); \
	DECLARE_FUNCTION(execSetGenerationConfig); \
	DECLARE_FUNCTION(execClearGeneration); \
	DECLARE_FUNCTION(execRestartGeneration); \
	DECLARE_FUNCTION(execResumeGeneration); \
	DECLARE_FUNCTION(execPauseGeneration); \
	DECLARE_FUNCTION(execStopGeneration); \
	DECLARE_FUNCTION(execStartGeneration);


AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister();

#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_298_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAProceduralMapGenerator(); \
	friend struct Z_Construct_UClass_AProceduralMapGenerator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister(); \
public: \
	DECLARE_CLASS2(AProceduralMapGenerator, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_AProceduralMapGenerator_NoRegister) \
	DECLARE_SERIALIZER(AProceduralMapGenerator)


#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_298_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AProceduralMapGenerator(AProceduralMapGenerator&&) = delete; \
	AProceduralMapGenerator(const AProceduralMapGenerator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AProceduralMapGenerator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AProceduralMapGenerator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AProceduralMapGenerator) \
	NO_API virtual ~AProceduralMapGenerator();


#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_295_PROLOG
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_298_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_298_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_298_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_298_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AProceduralMapGenerator;

// ********** End Class AProceduralMapGenerator ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h

// ********** Begin Enum EPCGGenerationPhase *******************************************************
#define FOREACH_ENUM_EPCGGENERATIONPHASE(op) \
	op(EPCGGenerationPhase::None) \
	op(EPCGGenerationPhase::Initialization) \
	op(EPCGGenerationPhase::Terrain) \
	op(EPCGGenerationPhase::Lanes) \
	op(EPCGGenerationPhase::Objectives) \
	op(EPCGGenerationPhase::Walls) \
	op(EPCGGenerationPhase::River) \
	op(EPCGGenerationPhase::Vegetation) \
	op(EPCGGenerationPhase::Props) \
	op(EPCGGenerationPhase::Lighting) \
	op(EPCGGenerationPhase::Finalization) \
	op(EPCGGenerationPhase::Completed) 

enum class EPCGGenerationPhase : uint8;
template<> struct TIsUEnumClass<EPCGGenerationPhase> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGGenerationPhase>();
// ********** End Enum EPCGGenerationPhase *********************************************************

// ********** Begin Enum EPCGMapAssetType **********************************************************
#define FOREACH_ENUM_EPCGMAPASSETTYPE(op) \
	op(EPCGMapAssetType::StaticMesh) \
	op(EPCGMapAssetType::SkeletalMesh) \
	op(EPCGMapAssetType::Material) \
	op(EPCGMapAssetType::Texture) \
	op(EPCGMapAssetType::Landscape) \
	op(EPCGMapAssetType::Foliage) \
	op(EPCGMapAssetType::Particle) \
	op(EPCGMapAssetType::Audio) \
	op(EPCGMapAssetType::Blueprint) 

enum class EPCGMapAssetType : uint8;
template<> struct TIsUEnumClass<EPCGMapAssetType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGMapAssetType>();
// ********** End Enum EPCGMapAssetType ************************************************************

// ********** Begin Enum EPCGDistributionType ******************************************************
#define FOREACH_ENUM_EPCGDISTRIBUTIONTYPE(op) \
	op(EPCGDistributionType::Random) \
	op(EPCGDistributionType::Grid) \
	op(EPCGDistributionType::Poisson) \
	op(EPCGDistributionType::Geometric) \
	op(EPCGDistributionType::Weighted) \
	op(EPCGDistributionType::Clustered) 

enum class EPCGDistributionType : uint8;
template<> struct TIsUEnumClass<EPCGDistributionType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGDistributionType>();
// ********** End Enum EPCGDistributionType ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
