#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Containers/Array.h"
#include "UObject/ObjectMacros.h"
#include "ALaneManager.generated.h"

// Enums para tipos de lanes e torres
UENUM(BlueprintType)
enum class ELaneManagerType : uint8
{
    Superior    UMETA(DisplayName = "Lane Superior"),
    Central     UMETA(DisplayName = "Lane Central"),
    Inferior    UMETA(DisplayName = "Lane Inferior")
};

UENUM(BlueprintType)
enum class ETowerType : uint8
{
    Externa     UMETA(DisplayName = "Torre Externa"),
    Interna     UMETA(DisplayName = "Torre Interna"),
    Inibidor    UMETA(DisplayName = "Torre Inibidor")
};

// Estruturas de dados para lanes e torres
USTRUCT(BlueprintType)
struct FLaneData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    ELaneManagerType LaneType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    float LaneWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    TArray<FVector> Waypoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    FVector StartPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    FVector EndPosition;

    FLaneData()
    {
        LaneType = ELaneManagerType::Central;
        LaneWidth = 400.0f;
        StartPosition = FVector::ZeroVector;
        EndPosition = FVector::ZeroVector;
    }
};

USTRUCT(BlueprintType)
struct FTowerData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    ETowerType TowerType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float BaseRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float TopRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float Height;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float AttackRange;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    int32 Health;

    FTowerData()
    {
        TowerType = ETowerType::Externa;
        Position = FVector::ZeroVector;
        BaseRadius = 120.0f;
        TopRadius = 80.0f;
        Height = 600.0f;
        AttackRange = 800.0f;
        Health = 2000;
    }
};

// Estrutura para pathfinding A*
USTRUCT(BlueprintType)
struct FPathNode
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float GCost;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float HCost;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float FCost;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    int32 ParentIndex;

    FPathNode()
    {
        Position = FVector::ZeroVector;
        GCost = 0.0f;
        HCost = 0.0f;
        FCost = 0.0f;
        ParentIndex = -1;
    }
};

UCLASS(BlueprintType, Blueprintable)
class AURA_API ALaneManager : public AActor
{
    GENERATED_BODY()

public:
    ALaneManager();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Componentes principais
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USplineComponent* SuperiorLaneSpline;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USplineComponent* CentralLaneSpline;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USplineComponent* InferiorLaneSpline;

    // Dados das lanes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Configuration")
    TArray<FLaneData> LanesData;

    // Dados das torres
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower Configuration")
    TArray<FTowerData> TowersData;

    // Configurações matemáticas das lanes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Mathematics")
    float MapSize = 16000.0f; // 160m x 160m em UU (1 UU = 1 cm)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Mathematics")
    float PlayableArea = 14400.0f; // 144m x 144m em UU

    // Funções públicas para geometria das lanes
    UFUNCTION(BlueprintCallable, Category = "Lane Mathematics")
    FVector CalculateSuperiorLanePosition(float X) const;

    UFUNCTION(BlueprintCallable, Category = "Lane Mathematics")
    FVector CalculateCentralLanePosition(float X) const;

    UFUNCTION(BlueprintCallable, Category = "Lane Mathematics")
    FVector CalculateInferiorLanePosition(float X) const;

    // Funções de inicialização
    UFUNCTION(BlueprintCallable, Category = "Lane Setup")
    void InitializeLanes();

    UFUNCTION(BlueprintCallable, Category = "Lane Setup")
    void InitializeTowers();

    UFUNCTION(BlueprintCallable, Category = "Lane Setup")
    void GenerateWaypoints();

    // Funções de pathfinding A*
    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<FVector> FindPath(const FVector& StartPos, const FVector& EndPos, ELaneManagerType LaneType);

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    float CalculateHeuristic(const FVector& Start, const FVector& End) const;

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<FVector> GetNearbyWaypoints(const FVector& Position, ELaneManagerType LaneType, float Radius = 1200.0f) const;

    // Funções de torres
    UFUNCTION(BlueprintCallable, Category = "Tower Management")
    void SpawnTower(const FTowerData& TowerData);

    UFUNCTION(BlueprintCallable, Category = "Tower Management")
    TArray<AActor*> FindTargetsInRange(const FVector& TowerPosition, float Range) const;

    UFUNCTION(BlueprintCallable, Category = "Tower Management")
    AActor* GetBestTarget(const FVector& TowerPosition, const TArray<AActor*>& PotentialTargets) const;

    // Funções de validação geométrica
    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateLaneGeometry() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool IsPositionInLane(const FVector& Position, ELaneManagerType LaneType, float Tolerance = 1.0f) const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    ELaneManagerType GetClosestLane(const FVector& Position) const;

    // Funções de integração com rio
    UFUNCTION(BlueprintCallable, Category = "River Integration")
    bool IsPositionInRiver(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    FVector GetBridgePosition(ELaneManagerType LaneType) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    float GetMovementSpeedModifier(const FVector& Position) const;

private:
    // Funções auxiliares privadas
    void SetupLaneSplines();
    void CalculateWaypointsForLane(ELaneManagerType LaneType);
    FVector GetLaneDirection(ELaneManagerType LaneType, const FVector& Position) const;
    bool IsValidWaypointPosition(const FVector& Position) const;
    
    // Constantes matemáticas para as lanes
    static constexpr float SUPERIOR_LANE_SLOPE = -0.577f; // -tan(30°)
    static constexpr float SUPERIOR_LANE_INTERCEPT = 6928.0f;
    static constexpr float INFERIOR_LANE_SLOPE = 0.577f; // tan(30°)
    static constexpr float INFERIOR_LANE_INTERCEPT = -6928.0f;
    
    // Larguras das lanes
    static constexpr float SUPERIOR_LANE_WIDTH = 300.0f;
    static constexpr float CENTRAL_LANE_WIDTH = 400.0f;
    static constexpr float INFERIOR_LANE_WIDTH = 300.0f;
    
    // Distâncias entre waypoints
    static constexpr float MIN_WAYPOINT_DISTANCE = 800.0f;
    static constexpr float MAX_WAYPOINT_DISTANCE = 1200.0f;
    
    // Configurações de pathfinding
    static constexpr float PATHFINDING_UPDATE_INTERVAL = 2.0f;
    static constexpr int32 MAX_PATHFINDING_ITERATIONS = 1000;
};